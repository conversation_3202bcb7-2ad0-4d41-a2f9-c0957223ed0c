2025-06-27 20:45:40,519 - root - INFO - Loaded environment variables from .env file
2025-06-27 20:45:41,505 - root - INFO - Loaded 2 trade records from logs/trades\trade_log_********.json
2025-06-27 20:45:41,507 - root - INFO - Loaded 1 asset selection records from logs/trades\asset_selection_********.json
2025-06-27 20:45:41,507 - root - INFO - Trade logger initialized with log directory: logs/trades
2025-06-27 20:45:43,148 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 20:45:43,170 - root - INFO - Configuration loaded successfully.
2025-06-27 20:45:43,172 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 20:45:43,191 - root - INFO - Configuration loaded successfully.
2025-06-27 20:45:43,192 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 20:45:43,205 - root - INFO - Configuration loaded successfully.
2025-06-27 20:45:43,205 - root - INFO - Loading notification configuration from config/notifications_bitvavo.json...
2025-06-27 20:45:43,205 - root - INFO - Notification configuration loaded successfully.
2025-06-27 20:45:44,423 - root - INFO - Telegram command handlers registered
2025-06-27 20:45:44,423 - root - INFO - Telegram bot polling started
2025-06-27 20:45:44,423 - root - INFO - Telegram notifier initialized with notification level: standard
2025-06-27 20:45:44,423 - root - INFO - Telegram notification channel initialized
2025-06-27 20:45:44,431 - root - INFO - Successfully loaded templates using utf-8 encoding
2025-06-27 20:45:44,431 - root - INFO - Loaded 24 templates from file
2025-06-27 20:45:44,431 - root - INFO - Notification manager initialized with 1 channels
2025-06-27 20:45:44,431 - root - INFO - Notification manager initialized
2025-06-27 20:45:44,431 - root - INFO - Added critical time window: 23:55 for 10 minutes - Before daily candle close (1d)
2025-06-27 20:45:44,431 - root - INFO - Added critical time window: 00:00 for 10 minutes - After daily candle close (1d)
2025-06-27 20:45:44,431 - root - INFO - Set up critical time windows for 1d timeframe
2025-06-27 20:45:44,431 - root - INFO - Network watchdog initialized with 10s check interval
2025-06-27 20:45:44,431 - root - INFO - Loaded recovery state from data/state\recovery_state.json
2025-06-27 20:45:44,439 - root - INFO - No state file found at C:\Users\<USER>\OneDrive\Stalinis kompiuteris\Asset_Screener_Python\data\state\data/state\background_service_20250626_114325.json.json
2025-06-27 20:45:44,442 - root - INFO - Recovery manager initialized
2025-06-27 20:45:44,442 - root - INFO - [DEBUG] TRADING EXECUTOR - Initializing with exchange: bitvavo
2025-06-27 20:45:44,442 - root - INFO - [DEBUG] TRADING EXECUTOR - Trading config: {'enabled': True, 'exchange': 'bitvavo', 'initial_capital': 100, 'max_slippage_pct': 0.5, 'min_order_values': {'default': 5.0}, 'mode': 'paper', 'order_type': 'market', 'position_size_pct': 10, 'risk_management': {'max_daily_trades': 5, 'max_open_positions': 10}, 'transaction_fee_rate': 0.0025}
2025-06-27 20:45:44,442 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 20:45:44,447 - root - INFO - Configuration loaded successfully.
2025-06-27 20:45:44,447 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-27 20:45:44,447 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-27 20:45:44,447 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-27 20:45:44,447 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-27 20:45:44,447 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-27 20:45:44,447 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-27 20:45:44,455 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-27 20:45:44,455 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-27 20:45:44,455 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 20:45:44,465 - root - INFO - Configuration loaded successfully.
2025-06-27 20:45:44,493 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getMe "HTTP/1.1 200 OK"
2025-06-27 20:45:44,505 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/deleteWebhook "HTTP/1.1 200 OK"
2025-06-27 20:45:44,507 - telegram.ext.Application - INFO - Application started
2025-06-27 20:45:44,905 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-27 20:45:44,905 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-27 20:45:44,905 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-27 20:45:44,907 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-27 20:45:44,907 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-27 20:45:44,907 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 20:45:44,918 - root - INFO - Configuration loaded successfully.
2025-06-27 20:45:44,922 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 20:45:44,930 - root - INFO - Configuration loaded successfully.
2025-06-27 20:45:44,932 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-27 20:45:44,940 - root - INFO - Configuration loaded successfully.
2025-06-27 20:45:44,948 - root - INFO - Loaded paper trading history from paper_trading_history.json
2025-06-27 20:45:44,948 - root - INFO - Paper trading initialized with EUR as quote currency
2025-06-27 20:45:44,948 - root - INFO - Trading executor initialized for bitvavo
2025-06-27 20:45:44,948 - root - INFO - Trading mode: paper
2025-06-27 20:45:44,948 - root - INFO - Trading enabled: True
2025-06-27 20:45:44,948 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-27 20:45:44,948 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-27 20:45:44,948 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-27 20:45:44,948 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-27 20:45:44,948 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 20:45:44,959 - root - INFO - Configuration loaded successfully.
2025-06-27 20:45:45,178 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-27 20:45:45,178 - root - INFO - Trading enabled in paper mode
2025-06-27 20:45:45,178 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-27 20:45:45,178 - root - INFO - Reset paper trading account to initial balance: 100 EUR
2025-06-27 20:45:45,178 - root - INFO - Reset paper trading account to initial balance
2025-06-27 20:45:45,178 - root - INFO - Generated run ID: ********_204545
2025-06-27 20:45:45,178 - root - INFO - Ensured metrics directory exists: Performance_Metrics
2025-06-27 20:45:45,178 - root - INFO - Background service initialized
2025-06-27 20:45:45,178 - root - INFO - Network watchdog started
2025-06-27 20:45:45,178 - root - INFO - Network watchdog started
2025-06-27 20:45:45,178 - root - INFO - Schedule set up for 1d timeframe
2025-06-27 20:45:45,178 - root - INFO - Background service started
2025-06-27 20:45:45,187 - root - INFO - Executing strategy (run #1)...
2025-06-27 20:45:45,187 - root - INFO - Resetting daily trade counters for this strategy execution
2025-06-27 20:45:45,187 - root - INFO - No trades recorded today (Max: 5)
2025-06-27 20:45:45,187 - root - INFO - Initialized daily trades counter for 2025-06-27
2025-06-27 20:45:45,192 - root - INFO - Creating snapshot for candle timestamp: ********
2025-06-27 20:45:45,261 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-27 20:45:51,213 - root - WARNING - Failed to send with Markdown formatting: Timed out
2025-06-27 20:45:51,290 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-27 20:45:51,292 - root - INFO - Using trend method 'PGO For Loop' for strategy execution
2025-06-27 20:45:51,292 - root - INFO - Using configured start date for 1d timeframe: 2025-02-10
2025-06-27 20:45:51,294 - root - INFO - Using recent date for performance tracking: 2025-06-20
2025-06-27 20:45:51,295 - root - INFO - Using real-time data fetching - only fetching new data since last cached timestamp
2025-06-27 20:45:51,331 - root - INFO - Loaded 2138 rows of ETH/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:45:51,331 - root - INFO - Last timestamp in cache for ETH/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:45:51,332 - root - INFO - Expected last timestamp for ETH/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:45:51,332 - root - INFO - Data is up to date for ETH/USDT
2025-06-27 20:45:51,334 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:45:51,352 - root - INFO - Loaded 2138 rows of BTC/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:45:51,353 - root - INFO - Last timestamp in cache for BTC/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:45:51,353 - root - INFO - Expected last timestamp for BTC/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:45:51,353 - root - INFO - Data is up to date for BTC/USDT
2025-06-27 20:45:51,355 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:45:51,370 - root - INFO - Loaded 1781 rows of SOL/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:45:51,371 - root - INFO - Last timestamp in cache for SOL/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:45:51,371 - root - INFO - Expected last timestamp for SOL/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:45:51,372 - root - INFO - Data is up to date for SOL/USDT
2025-06-27 20:45:51,372 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:45:51,385 - root - INFO - Loaded 786 rows of SUI/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:45:51,386 - root - INFO - Last timestamp in cache for SUI/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:45:51,386 - root - INFO - Expected last timestamp for SUI/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:45:51,386 - root - INFO - Data is up to date for SUI/USDT
2025-06-27 20:45:51,387 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:45:51,416 - root - INFO - Loaded 2138 rows of XRP/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:45:51,416 - root - INFO - Last timestamp in cache for XRP/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:45:51,417 - root - INFO - Expected last timestamp for XRP/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:45:51,417 - root - INFO - Data is up to date for XRP/USDT
2025-06-27 20:45:51,418 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:45:51,436 - root - INFO - Loaded 1716 rows of AAVE/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:45:51,437 - root - INFO - Last timestamp in cache for AAVE/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:45:51,437 - root - INFO - Expected last timestamp for AAVE/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:45:51,437 - root - INFO - Data is up to date for AAVE/USDT
2025-06-27 20:45:51,438 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:45:51,455 - root - INFO - Loaded 1739 rows of AVAX/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:45:51,456 - root - INFO - Last timestamp in cache for AVAX/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:45:51,456 - root - INFO - Expected last timestamp for AVAX/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:45:51,457 - root - INFO - Data is up to date for AVAX/USDT
2025-06-27 20:45:51,458 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:45:51,478 - root - INFO - Loaded 2138 rows of ADA/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:45:51,478 - root - INFO - Last timestamp in cache for ADA/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:45:51,479 - root - INFO - Expected last timestamp for ADA/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:45:51,479 - root - INFO - Data is up to date for ADA/USDT
2025-06-27 20:45:51,480 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:45:51,507 - root - INFO - Loaded 2138 rows of LINK/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:45:51,508 - root - INFO - Last timestamp in cache for LINK/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:45:51,509 - root - INFO - Expected last timestamp for LINK/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:45:51,509 - root - INFO - Data is up to date for LINK/USDT
2025-06-27 20:45:51,511 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:45:51,531 - root - INFO - Loaded 2138 rows of TRX/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:45:51,532 - root - INFO - Last timestamp in cache for TRX/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:45:51,533 - root - INFO - Expected last timestamp for TRX/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:45:51,533 - root - INFO - Data is up to date for TRX/USDT
2025-06-27 20:45:51,534 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:45:51,546 - root - INFO - Loaded 784 rows of PEPE/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:45:51,546 - root - INFO - Last timestamp in cache for PEPE/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:45:51,546 - root - INFO - Expected last timestamp for PEPE/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:45:51,546 - root - INFO - Data is up to date for PEPE/USDT
2025-06-27 20:45:51,547 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:45:51,565 - root - INFO - Loaded 2138 rows of DOGE/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:45:51,565 - root - INFO - Last timestamp in cache for DOGE/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:45:51,566 - root - INFO - Expected last timestamp for DOGE/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:45:51,566 - root - INFO - Data is up to date for DOGE/USDT
2025-06-27 20:45:51,567 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:45:51,588 - root - INFO - Loaded 2138 rows of BNB/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:45:51,589 - root - INFO - Last timestamp in cache for BNB/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:45:51,590 - root - INFO - Expected last timestamp for BNB/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:45:51,590 - root - INFO - Data is up to date for BNB/USDT
2025-06-27 20:45:51,594 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:45:51,618 - root - INFO - Loaded 1774 rows of DOT/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:45:51,618 - root - INFO - Last timestamp in cache for DOT/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:45:51,619 - root - INFO - Expected last timestamp for DOT/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:45:51,619 - root - INFO - Data is up to date for DOT/USDT
2025-06-27 20:45:51,620 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:45:51,622 - root - INFO - Using 14 trend assets (USDT) for analysis and 14 trading assets (EUR) for execution
2025-06-27 20:45:51,622 - root - INFO - MTPI Multi-Indicator Configuration:
2025-06-27 20:45:51,623 - root - INFO -   - Number of indicators: 8
2025-06-27 20:45:51,626 - root - INFO -   - Enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-27 20:45:51,628 - root - INFO -   - Combination method: consensus
2025-06-27 20:45:51,628 - root - INFO -   - Long threshold: 0.1
2025-06-27 20:45:51,628 - root - INFO -   - Short threshold: -0.1
2025-06-27 20:45:51,629 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trend_assets order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-27 20:45:51,629 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trading_assets order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-27 20:45:51,630 - root - ERROR - [DEBUG] BACKGROUND SERVICE - BTC position in trend_assets: 2
2025-06-27 20:45:51,630 - root - ERROR - [DEBUG] BACKGROUND SERVICE - TRX position in trend_assets: 10
2025-06-27 20:45:51,630 - root - INFO - === RUNNING STRATEGY FOR WEB USING TEST_ALLOCATION ===
2025-06-27 20:45:51,630 - root - INFO - Parameters: use_mtpi_signal=False, mtpi_indicator_type=PGO
2025-06-27 20:45:51,630 - root - INFO - mtpi_timeframe=1d, mtpi_pgo_length=35
2025-06-27 20:45:51,631 - root - INFO - mtpi_upper_threshold=1.1, mtpi_lower_threshold=-0.58
2025-06-27 20:45:51,631 - root - INFO - timeframe=1d, analysis_start_date='2025-02-10'
2025-06-27 20:45:51,631 - root - INFO - n_assets=1, use_weighted_allocation=False
2025-06-27 20:45:51,631 - root - INFO - Using provided trend method: PGO For Loop
2025-06-27 20:45:51,631 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 20:45:51,643 - root - INFO - Configuration loaded successfully.
2025-06-27 20:45:51,643 - root - INFO - Saving configuration to config/settings_bitvavo_eur.yaml...
2025-06-27 20:45:51,653 - root - INFO - Configuration saved successfully.
2025-06-27 20:45:51,653 - root - INFO - Trend detection assets (USDT): ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-27 20:45:51,654 - root - INFO - Number of trend detection assets: 14
2025-06-27 20:45:51,654 - root - INFO - Selected assets type: <class 'list'>
2025-06-27 20:45:51,654 - root - INFO - Trading execution assets (EUR): ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-27 20:45:51,655 - root - INFO - Number of trading assets: 14
2025-06-27 20:45:51,655 - root - INFO - Trading assets type: <class 'list'>
2025-06-27 20:45:51,967 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-27 20:45:51,977 - root - INFO - Configuration loaded successfully.
2025-06-27 20:45:51,986 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 20:45:51,999 - root - INFO - Configuration loaded successfully.
2025-06-27 20:45:51,999 - root - INFO - Execution context: backtesting
2025-06-27 20:45:51,999 - root - INFO - Execution timing: candle_close
2025-06-27 20:45:51,999 - root - INFO - Ratio calculation method: manual_inversion
2025-06-27 20:45:51,999 - root - INFO - Tie-breaking strategy: imcumbent
2025-06-27 20:45:52,000 - root - INFO - Asset trend PGO parameters: length=None, upper_threshold=None, lower_threshold=None
2025-06-27 20:45:52,000 - root - INFO - MTPI indicators override: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-27 20:45:52,000 - root - INFO - MTPI combination method override: consensus
2025-06-27 20:45:52,000 - root - INFO - MTPI long threshold override: 0.1
2025-06-27 20:45:52,000 - root - INFO - MTPI short threshold override: -0.1
2025-06-27 20:45:52,001 - root - INFO - Using standard warmup period of 60 days for equal allocation
2025-06-27 20:45:52,001 - root - INFO - Fetching data from 2024-12-12 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-27 20:45:52,003 - root - INFO - Loaded metadata for 48 assets
2025-06-27 20:45:52,003 - root - INFO - Loaded metadata for 48 assets
2025-06-27 20:45:52,003 - root - INFO - Loaded metadata for 48 assets
2025-06-27 20:45:52,004 - root - INFO - Loaded metadata for 48 assets
2025-06-27 20:45:52,005 - root - INFO - Loaded metadata for 48 assets
2025-06-27 20:45:52,005 - root - INFO - Loaded metadata for 48 assets
2025-06-27 20:45:52,005 - root - INFO - Loaded metadata for 48 assets
2025-06-27 20:45:52,005 - root - INFO - Loaded metadata for 48 assets
2025-06-27 20:45:52,006 - root - INFO - Loaded metadata for 48 assets
2025-06-27 20:45:52,007 - root - INFO - Loaded metadata for 48 assets
2025-06-27 20:45:52,007 - root - INFO - Loaded metadata for 48 assets
2025-06-27 20:45:52,008 - root - INFO - Loaded metadata for 48 assets
2025-06-27 20:45:52,008 - root - INFO - Loaded metadata for 48 assets
2025-06-27 20:45:52,009 - root - INFO - Loaded metadata for 48 assets
2025-06-27 20:45:52,009 - root - INFO - Checking cache for 14 symbols (1d)...
2025-06-27 20:45:52,037 - root - INFO - Loaded 197 rows of ETH/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:45:52,039 - root - INFO - Metadata for ETH/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 20:45:52,039 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:45:52,039 - root - INFO - Loaded 197 rows of ETH/USDT data from cache (after filtering).
2025-06-27 20:45:52,062 - root - INFO - Loaded 197 rows of BTC/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:45:52,064 - root - INFO - Metadata for BTC/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 20:45:52,064 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:45:52,065 - root - INFO - Loaded 197 rows of BTC/USDT data from cache (after filtering).
2025-06-27 20:45:52,083 - root - INFO - Loaded 197 rows of SOL/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:45:52,084 - root - INFO - Metadata for SOL/USDT shows data starting from 2020-08-11, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 20:45:52,085 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:45:52,085 - root - INFO - Loaded 197 rows of SOL/USDT data from cache (after filtering).
2025-06-27 20:45:52,098 - root - INFO - Loaded 197 rows of SUI/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:45:52,100 - root - INFO - Metadata for SUI/USDT shows data starting from 2023-05-03, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 20:45:52,101 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:45:52,101 - root - INFO - Loaded 197 rows of SUI/USDT data from cache (after filtering).
2025-06-27 20:45:52,130 - root - INFO - Loaded 197 rows of XRP/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:45:52,132 - root - INFO - Metadata for XRP/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 20:45:52,133 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:45:52,133 - root - INFO - Loaded 197 rows of XRP/USDT data from cache (after filtering).
2025-06-27 20:45:52,152 - root - INFO - Loaded 197 rows of AAVE/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:45:52,153 - root - INFO - Metadata for AAVE/USDT shows data starting from 2020-10-15, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 20:45:52,154 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:45:52,154 - root - INFO - Loaded 197 rows of AAVE/USDT data from cache (after filtering).
2025-06-27 20:45:52,176 - root - INFO - Loaded 197 rows of AVAX/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:45:52,180 - root - INFO - Metadata for AVAX/USDT shows data starting from 2020-09-22, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 20:45:52,181 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:45:52,181 - root - INFO - Loaded 197 rows of AVAX/USDT data from cache (after filtering).
2025-06-27 20:45:52,210 - root - INFO - Loaded 197 rows of ADA/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:45:52,213 - root - INFO - Metadata for ADA/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 20:45:52,213 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:45:52,213 - root - INFO - Loaded 197 rows of ADA/USDT data from cache (after filtering).
2025-06-27 20:45:52,234 - root - INFO - Loaded 197 rows of LINK/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:45:52,235 - root - INFO - Metadata for LINK/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 20:45:52,236 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:45:52,236 - root - INFO - Loaded 197 rows of LINK/USDT data from cache (after filtering).
2025-06-27 20:45:52,256 - root - INFO - Loaded 197 rows of TRX/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:45:52,259 - root - INFO - Metadata for TRX/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 20:45:52,260 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:45:52,260 - root - INFO - Loaded 197 rows of TRX/USDT data from cache (after filtering).
2025-06-27 20:45:52,272 - root - INFO - Loaded 197 rows of PEPE/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:45:52,274 - root - INFO - Metadata for PEPE/USDT shows data starting from 2023-05-05, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 20:45:52,275 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:45:52,275 - root - INFO - Loaded 197 rows of PEPE/USDT data from cache (after filtering).
2025-06-27 20:45:52,300 - root - INFO - Loaded 197 rows of DOGE/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:45:52,301 - root - INFO - Metadata for DOGE/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 20:45:52,302 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:45:52,302 - root - INFO - Loaded 197 rows of DOGE/USDT data from cache (after filtering).
2025-06-27 20:45:52,327 - root - INFO - Loaded 197 rows of BNB/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:45:52,329 - root - INFO - Metadata for BNB/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 20:45:52,330 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:45:52,330 - root - INFO - Loaded 197 rows of BNB/USDT data from cache (after filtering).
2025-06-27 20:45:52,349 - root - INFO - Loaded 197 rows of DOT/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:45:52,351 - root - INFO - Metadata for DOT/USDT shows data starting from 2020-08-18, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 20:45:52,351 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:45:52,351 - root - INFO - Loaded 197 rows of DOT/USDT data from cache (after filtering).
2025-06-27 20:45:52,351 - root - INFO - All 14 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-27 20:45:52,352 - root - INFO - Asset ETH/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 20:45:52,352 - root - INFO - Asset BTC/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 20:45:52,352 - root - INFO - Asset SOL/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 20:45:52,352 - root - INFO - Asset SUI/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 20:45:52,352 - root - INFO - Asset XRP/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 20:45:52,353 - root - INFO - Asset AAVE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 20:45:52,353 - root - INFO - Asset AVAX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 20:45:52,353 - root - INFO - Asset ADA/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 20:45:52,353 - root - INFO - Asset LINK/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 20:45:52,353 - root - INFO - Asset TRX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 20:45:52,353 - root - INFO - Asset PEPE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 20:45:52,354 - root - INFO - Asset DOGE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 20:45:52,354 - root - INFO - Asset BNB/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 20:45:52,354 - root - INFO - Asset DOT/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 20:45:52,374 - root - INFO - Using standard MTPI warmup period of 120 days
2025-06-27 20:45:52,375 - root - INFO - Fetching MTPI signals from 2024-10-13 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-27 20:45:52,375 - root - INFO - Fetching MTPI signals using trend method: PGO For Loop
2025-06-27 20:45:52,376 - root - INFO - Using multi-indicator MTPI with config override: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-27 20:45:52,376 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-27 20:45:52,386 - root - INFO - Configuration loaded successfully.
2025-06-27 20:45:52,386 - root - INFO - Loaded MTPI multi-indicator configuration with 8 enabled indicators
2025-06-27 20:45:52,386 - root - INFO - Applying configuration overrides: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-27 20:45:52,386 - root - INFO - Override: enabled_indicators = ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-27 20:45:52,386 - root - INFO - Override: combination_method = consensus
2025-06-27 20:45:52,386 - root - INFO - Override: long_threshold = 0.1
2025-06-27 20:45:52,387 - root - INFO - Override: short_threshold = -0.1
2025-06-27 20:45:52,387 - root - INFO - Using MTPI configuration: indicators=['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], method=consensus, thresholds=(-0.1, 0.1)
2025-06-27 20:45:52,387 - root - INFO - Calculated appropriate limit for 1d timeframe: 500 candles (minimum 180.0 candles needed for 60 length indicator)
2025-06-27 20:45:52,387 - root - INFO - Using adjusted limit: 500 for max indicator length: 60
2025-06-27 20:45:52,388 - root - INFO - Checking cache for 1 symbols (1d)...
2025-06-27 20:45:52,414 - root - INFO - Loaded 257 rows of BTC/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:45:52,414 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:45:52,415 - root - INFO - Loaded 257 rows of BTC/USDT data from cache (after filtering).
2025-06-27 20:45:52,415 - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-27 20:45:52,415 - root - INFO - Fetched BTC data: 257 candles from 2024-10-13 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 20:45:52,415 - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-06-27 20:45:52,455 - root - INFO - Generated PGO Score signals: {-1: 105, 0: 34, 1: 118}
2025-06-27 20:45:52,456 - root - INFO - Generated pgo signals: 257 values
2025-06-27 20:45:52,456 - root - INFO - Generating Bollinger Band signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False, heikin_src=close
2025-06-27 20:45:52,456 - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-06-27 20:45:52,472 - root - INFO - Generated BB Score signals: {-1: 107, 0: 32, 1: 118}
2025-06-27 20:45:52,472 - root - INFO - Generated Bollinger Band signals: 257 values
2025-06-27 20:45:52,472 - root - INFO - Generated bollinger_bands signals: 257 values
2025-06-27 20:45:53,079 - root - INFO - Generated DWMA signals using Weighted SD method
2025-06-27 20:45:53,081 - root - INFO - Generated dwma_score signals: 257 values
2025-06-27 20:45:53,151 - root - INFO - Generated DEMA Supertrend signals
2025-06-27 20:45:53,151 - root - INFO - Signal distribution: {-1: 143, 0: 1, 1: 113}
2025-06-27 20:45:53,151 - root - INFO - Generated DEMA Super Score signals
2025-06-27 20:45:53,155 - root - INFO - Generated dema_super_score signals: 257 values
2025-06-27 20:45:53,275 - root - INFO - Generated DPSD signals
2025-06-27 20:45:53,275 - root - INFO - Signal distribution: {-1: 99, 0: 87, 1: 71}
2025-06-27 20:45:53,277 - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-06-27 20:45:53,277 - root - INFO - Generated dpsd_score signals: 257 values
2025-06-27 20:45:53,287 - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-06-27 20:45:53,287 - root - INFO - Generated AAD Score signals using SMA method
2025-06-27 20:45:53,287 - root - INFO - Generated aad_score signals: 257 values
2025-06-27 20:45:53,360 - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-06-27 20:45:53,360 - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-06-27 20:45:53,360 - root - INFO - Generated dynamic_ema_score signals: 257 values
2025-06-27 20:45:53,487 - root - INFO - Generated quantile_dema_score signals: 257 values
2025-06-27 20:45:53,495 - root - INFO - Combined 8 signals using arithmetic mean aggregation
2025-06-27 20:45:53,495 - root - INFO - Signal distribution: {1: 145, -1: 111, 0: 1}
2025-06-27 20:45:53,495 - root - INFO - Generated combined MTPI signals: 257 values using consensus method
2025-06-27 20:45:53,495 - root - INFO - Signal distribution: {1: 145, -1: 111, 0: 1}
2025-06-27 20:45:53,495 - root - INFO - Calculating daily scores using trend method: PGO For Loop...
2025-06-27 20:45:53,500 - root - INFO - Saving configuration to config/settings.yaml...
2025-06-27 20:45:53,508 - root - INFO - Configuration saved successfully.
2025-06-27 20:45:53,510 - root - INFO - Updated config with trend method: PGO For Loop and PGO parameters
2025-06-27 20:45:53,510 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-27 20:45:53,520 - root - INFO - Configuration loaded successfully.
2025-06-27 20:45:53,520 - root - INFO - Using ratio-based approach with PGO (PGO For Loop)...
2025-06-27 20:45:53,520 - root - INFO - Calculating ratio PGO signals for 14 assets with PGO(35) using full OHLCV data
2025-06-27 20:45:53,520 - root - INFO - Using ratio calculation method: manual_inversion
2025-06-27 20:45:53,551 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:53,594 - root - INFO - Calculated ratio PGO signal for ETH/USDT/BTC/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:53,615 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 20:45:53,615 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:53,631 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 20:45:53,640 - root - INFO - Calculated ratio PGO signal for ETH/USDT/SOL/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:53,664 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-27 20:45:53,664 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:53,681 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-27 20:45:53,698 - root - INFO - Calculated ratio PGO signal for ETH/USDT/SUI/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:53,731 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 20:45:53,731 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:53,764 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 20:45:53,781 - root - INFO - Calculated ratio PGO signal for ETH/USDT/XRP/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:53,833 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 20:45:53,833 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:53,854 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 20:45:53,862 - root - INFO - Calculated ratio PGO signal for ETH/USDT/AAVE/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:53,895 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-27 20:45:53,895 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:53,929 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-27 20:45:53,944 - root - INFO - Calculated ratio PGO signal for ETH/USDT/AVAX/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:53,995 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 20:45:53,995 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:54,044 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 20:45:54,057 - root - INFO - Calculated ratio PGO signal for ETH/USDT/ADA/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:54,108 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-27 20:45:54,109 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:54,133 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-27 20:45:54,142 - root - INFO - Calculated ratio PGO signal for ETH/USDT/LINK/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:54,175 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-27 20:45:54,175 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:54,192 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-27 20:45:54,199 - root - INFO - Calculated ratio PGO signal for ETH/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:54,224 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 20:45:54,224 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:54,249 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 20:45:54,259 - root - INFO - Calculated ratio PGO signal for ETH/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:54,282 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 20:45:54,282 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:54,298 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 20:45:54,308 - root - INFO - Calculated ratio PGO signal for ETH/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:54,332 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:54,360 - root - INFO - Calculated ratio PGO signal for ETH/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:54,382 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-27 20:45:54,382 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:54,402 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-27 20:45:54,412 - root - INFO - Calculated ratio PGO signal for ETH/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:54,432 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:54,458 - root - INFO - Calculated ratio PGO signal for BTC/USDT/SOL/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:54,482 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 20:45:54,482 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:54,498 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 20:45:54,515 - root - INFO - Calculated ratio PGO signal for BTC/USDT/SUI/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:54,540 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 20:45:54,540 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:54,540 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:45:54,557 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 20:45:54,564 - root - INFO - Calculated ratio PGO signal for BTC/USDT/XRP/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:54,591 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 20:45:54,591 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:54,614 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 20:45:54,622 - root - INFO - Calculated ratio PGO signal for BTC/USDT/AAVE/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:54,647 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-27 20:45:54,647 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:54,663 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-27 20:45:54,679 - root - INFO - Calculated ratio PGO signal for BTC/USDT/AVAX/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:54,697 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 20:45:54,697 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:54,726 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 20:45:54,730 - root - INFO - Calculated ratio PGO signal for BTC/USDT/ADA/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:54,757 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 20:45:54,757 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:54,781 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 20:45:54,781 - root - INFO - Calculated ratio PGO signal for BTC/USDT/LINK/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:54,809 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 20:45:54,809 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:54,831 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 20:45:54,838 - root - INFO - Calculated ratio PGO signal for BTC/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:54,857 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 20:45:54,863 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:54,880 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 20:45:54,889 - root - INFO - Calculated ratio PGO signal for BTC/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:54,909 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:54,930 - root - INFO - Calculated ratio PGO signal for BTC/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:54,959 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:54,986 - root - INFO - Calculated ratio PGO signal for BTC/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:55,012 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-27 20:45:55,012 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:55,035 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-27 20:45:55,040 - root - INFO - Calculated ratio PGO signal for BTC/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:55,061 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 20:45:55,061 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:55,086 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 20:45:55,100 - root - INFO - Calculated ratio PGO signal for SOL/USDT/SUI/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:55,128 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 20:45:55,128 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:55,150 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 20:45:55,153 - root - INFO - Calculated ratio PGO signal for SOL/USDT/XRP/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:55,178 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-27 20:45:55,178 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:55,205 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-27 20:45:55,214 - root - INFO - Calculated ratio PGO signal for SOL/USDT/AAVE/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:55,236 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-27 20:45:55,236 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:55,259 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-27 20:45:55,261 - root - INFO - Calculated ratio PGO signal for SOL/USDT/AVAX/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:55,286 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 20:45:55,286 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:55,310 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 20:45:55,318 - root - INFO - Calculated ratio PGO signal for SOL/USDT/ADA/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:55,335 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 20:45:55,335 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:55,359 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 20:45:55,359 - root - INFO - Calculated ratio PGO signal for SOL/USDT/LINK/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:55,384 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-27 20:45:55,384 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:55,410 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-27 20:45:55,418 - root - INFO - Calculated ratio PGO signal for SOL/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:55,443 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-27 20:45:55,443 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:55,460 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-27 20:45:55,467 - root - INFO - Calculated ratio PGO signal for SOL/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:55,497 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 20:45:55,498 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:55,516 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 20:45:55,527 - root - INFO - Calculated ratio PGO signal for SOL/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:55,549 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 20:45:55,549 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:55,566 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 20:45:55,582 - root - INFO - Calculated ratio PGO signal for SOL/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:55,599 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 20:45:55,599 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:55,625 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 20:45:55,631 - root - INFO - Calculated ratio PGO signal for SOL/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:55,657 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:55,689 - root - INFO - Calculated ratio PGO signal for SUI/USDT/XRP/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:55,709 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-27 20:45:55,709 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:55,738 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-27 20:45:55,753 - root - INFO - Calculated ratio PGO signal for SUI/USDT/AAVE/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:55,779 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:55,814 - root - INFO - Calculated ratio PGO signal for SUI/USDT/AVAX/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:55,855 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:55,878 - root - INFO - Calculated ratio PGO signal for SUI/USDT/ADA/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:55,911 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 20:45:55,911 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:55,930 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 20:45:55,937 - root - INFO - Calculated ratio PGO signal for SUI/USDT/LINK/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:55,987 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:56,035 - root - INFO - Calculated ratio PGO signal for SUI/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:56,059 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:56,102 - root - INFO - Calculated ratio PGO signal for SUI/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:56,133 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 20:45:56,133 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:56,157 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 20:45:56,165 - root - INFO - Calculated ratio PGO signal for SUI/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:56,193 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:56,222 - root - INFO - Calculated ratio PGO signal for SUI/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:56,246 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:56,270 - root - INFO - Calculated ratio PGO signal for SUI/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:56,296 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 20:45:56,298 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:56,325 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 20:45:56,335 - root - INFO - Calculated ratio PGO signal for XRP/USDT/AAVE/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:56,359 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-27 20:45:56,359 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:56,391 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-27 20:45:56,393 - root - INFO - Calculated ratio PGO signal for XRP/USDT/AVAX/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:56,433 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-27 20:45:56,433 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:56,451 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-27 20:45:56,460 - root - INFO - Calculated ratio PGO signal for XRP/USDT/ADA/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:56,483 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 20:45:56,483 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:56,511 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 20:45:56,515 - root - INFO - Calculated ratio PGO signal for XRP/USDT/LINK/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:56,541 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 20:45:56,541 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:56,565 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 20:45:56,565 - root - INFO - Calculated ratio PGO signal for XRP/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:56,590 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 20:45:56,590 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:56,614 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 20:45:56,624 - root - INFO - Calculated ratio PGO signal for XRP/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:56,647 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:56,676 - root - INFO - Calculated ratio PGO signal for XRP/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:56,697 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:56,724 - root - INFO - Calculated ratio PGO signal for XRP/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:56,746 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-27 20:45:56,746 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:56,768 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-27 20:45:56,774 - root - INFO - Calculated ratio PGO signal for XRP/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:56,796 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 20:45:56,796 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:56,821 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 20:45:56,829 - root - INFO - Calculated ratio PGO signal for AAVE/USDT/AVAX/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:56,846 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 20:45:56,846 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:56,871 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 20:45:56,880 - root - INFO - Calculated ratio PGO signal for AAVE/USDT/ADA/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:56,896 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 20:45:56,896 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:56,924 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 20:45:56,929 - root - INFO - Calculated ratio PGO signal for AAVE/USDT/LINK/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:56,960 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:56,979 - root - INFO - Calculated ratio PGO signal for AAVE/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:57,007 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:57,029 - root - INFO - Calculated ratio PGO signal for AAVE/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:57,058 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-27 20:45:57,058 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:57,080 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-27 20:45:57,080 - root - INFO - Calculated ratio PGO signal for AAVE/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:57,107 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:57,138 - root - INFO - Calculated ratio PGO signal for AAVE/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:57,163 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 20:45:57,163 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:57,191 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 20:45:57,197 - root - INFO - Calculated ratio PGO signal for AAVE/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:57,213 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:57,246 - root - INFO - Calculated ratio PGO signal for AVAX/USDT/ADA/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:57,261 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-27 20:45:57,261 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:57,290 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-27 20:45:57,295 - root - INFO - Calculated ratio PGO signal for AVAX/USDT/LINK/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:57,319 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:57,343 - root - INFO - Calculated ratio PGO signal for AVAX/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:57,368 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:57,393 - root - INFO - Calculated ratio PGO signal for AVAX/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:57,417 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:57,451 - root - INFO - Calculated ratio PGO signal for AVAX/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:57,481 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:57,510 - root - INFO - Calculated ratio PGO signal for AVAX/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:57,532 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:57,557 - root - INFO - Calculated ratio PGO signal for AVAX/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:57,582 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-27 20:45:57,582 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:57,598 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-27 20:45:57,614 - root - INFO - Calculated ratio PGO signal for ADA/USDT/LINK/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:57,631 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:57,657 - root - INFO - Calculated ratio PGO signal for ADA/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:57,681 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 20:45:57,681 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:57,708 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 20:45:57,715 - root - INFO - Calculated ratio PGO signal for ADA/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:57,738 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 20:45:57,738 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:57,763 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 20:45:57,763 - root - INFO - Calculated ratio PGO signal for ADA/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:57,791 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:57,815 - root - INFO - Calculated ratio PGO signal for ADA/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:57,846 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 20:45:57,846 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:57,862 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 20:45:57,870 - root - INFO - Calculated ratio PGO signal for ADA/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:57,895 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:57,924 - root - INFO - Calculated ratio PGO signal for LINK/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:57,960 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 20:45:57,960 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:57,988 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 20:45:58,002 - root - INFO - Calculated ratio PGO signal for LINK/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:58,034 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:58,074 - root - INFO - Calculated ratio PGO signal for LINK/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:58,109 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:58,146 - root - INFO - Calculated ratio PGO signal for LINK/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:58,163 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:58,196 - root - INFO - Calculated ratio PGO signal for LINK/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:58,250 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 20:45:58,252 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:58,300 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 20:45:58,304 - root - INFO - Calculated ratio PGO signal for TRX/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:58,329 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:58,383 - root - INFO - Calculated ratio PGO signal for TRX/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:58,420 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:58,456 - root - INFO - Calculated ratio PGO signal for TRX/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:58,478 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:58,507 - root - INFO - Calculated ratio PGO signal for TRX/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:58,527 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:58,557 - root - INFO - Calculated ratio PGO signal for PEPE/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:58,577 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:58,609 - root - INFO - Calculated ratio PGO signal for PEPE/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:58,625 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:58,657 - root - INFO - Calculated ratio PGO signal for PEPE/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:58,675 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:58,700 - root - INFO - Calculated ratio PGO signal for DOGE/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:58,726 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 20:45:58,726 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:58,750 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 20:45:58,764 - root - INFO - Calculated ratio PGO signal for DOGE/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-27 20:45:58,787 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:45:58,809 - root - INFO - Calculated ratio PGO signal for BNB/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-27 20:46:01,245 - root - INFO - Successfully calculated daily scores using ratio-based comparisons.
2025-06-27 20:46:01,247 - root - INFO - Finished calculating daily scores. DataFrame shape: (197, 14)
2025-06-27 20:46:01,247 - root - WARNING - Running strategy with n_assets=1, equal allocation, MTPI filtering DISABLED
2025-06-27 20:46:01,248 - root - INFO - Date ranges for each asset:
2025-06-27 20:46:01,248 - root - INFO -   ETH/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 20:46:01,248 - root - INFO -   BTC/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 20:46:01,248 - root - INFO -   SOL/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 20:46:01,248 - root - INFO -   SUI/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 20:46:01,248 - root - INFO -   XRP/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 20:46:01,256 - root - INFO -   AAVE/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 20:46:01,257 - root - INFO -   AVAX/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 20:46:01,257 - root - INFO -   ADA/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 20:46:01,257 - root - INFO -   LINK/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 20:46:01,258 - root - INFO -   TRX/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 20:46:01,258 - root - INFO -   PEPE/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 20:46:01,258 - root - INFO -   DOGE/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 20:46:01,258 - root - INFO -   BNB/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 20:46:01,258 - root - INFO -   DOT/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 20:46:01,259 - root - INFO - Common dates range: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 20:46:01,259 - root - INFO - Analysis will run from: 2025-02-10 to 2025-06-26 (137 candles)
2025-06-27 20:46:01,260 - root - INFO - EXECUTION TIMING VERIFICATION:
2025-06-27 20:46:01,260 - root - INFO -    Execution Method: candle_close
2025-06-27 20:46:01,264 - root - INFO -    Automatic execution at 00:00 UTC (candle close)
2025-06-27 20:46:01,264 - root - INFO -    Signal generated and executed immediately
2025-06-27 20:46:01,281 - root - INFO - Including ETH/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,281 - root - INFO - Including BTC/USDT on 2025-02-10 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,281 - root - INFO - Including SOL/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,281 - root - INFO - Including SUI/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,281 - root - INFO - Including XRP/USDT on 2025-02-10 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,281 - root - INFO - Including AAVE/USDT on 2025-02-10 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,281 - root - INFO - Including AVAX/USDT on 2025-02-10 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,281 - root - INFO - Including ADA/USDT on 2025-02-10 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,281 - root - INFO - Including LINK/USDT on 2025-02-10 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,281 - root - INFO - Including TRX/USDT on 2025-02-10 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,281 - root - INFO - Including PEPE/USDT on 2025-02-10 00:00:00+00:00 with score 0.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,281 - root - INFO - Including DOGE/USDT on 2025-02-10 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,281 - root - INFO - Including BNB/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,281 - root - INFO - Including DOT/USDT on 2025-02-10 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,289 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 20:46:01,289 - root - INFO - ASSET CHANGE DETECTED on 2025-02-11:
2025-06-27 20:46:01,289 - root - INFO -    Signal Date: 2025-02-10 (generated at 00:00 UTC)
2025-06-27 20:46:01,289 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-11 00:00 UTC (immediate)
2025-06-27 20:46:01,289 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:46:01,289 - root - INFO -    Buying: ['TRX/USDT']
2025-06-27 20:46:01,289 - root - INFO -    TRX/USDT buy price: $0.2410 (close price)
2025-06-27 20:46:01,297 - root - INFO - Including ETH/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,297 - root - INFO - Including BTC/USDT on 2025-02-11 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,297 - root - INFO - Including SOL/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,297 - root - INFO - Including SUI/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,297 - root - INFO - Including XRP/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,297 - root - INFO - Including AAVE/USDT on 2025-02-11 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,297 - root - INFO - Including AVAX/USDT on 2025-02-11 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,297 - root - INFO - Including ADA/USDT on 2025-02-11 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,297 - root - INFO - Including LINK/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,297 - root - INFO - Including TRX/USDT on 2025-02-11 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,297 - root - INFO - Including PEPE/USDT on 2025-02-11 00:00:00+00:00 with score 0.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,297 - root - INFO - Including DOGE/USDT on 2025-02-11 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,297 - root - INFO - Including BNB/USDT on 2025-02-11 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,297 - root - INFO - Including DOT/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,305 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,312 - root - INFO - Including ETH/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,312 - root - INFO - Including BTC/USDT on 2025-02-12 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,312 - root - INFO - Including SOL/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,313 - root - INFO - Including SUI/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,313 - root - INFO - Including XRP/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,313 - root - INFO - Including AAVE/USDT on 2025-02-12 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,313 - root - INFO - Including AVAX/USDT on 2025-02-12 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,313 - root - INFO - Including ADA/USDT on 2025-02-12 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,313 - root - INFO - Including LINK/USDT on 2025-02-12 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,313 - root - INFO - Including TRX/USDT on 2025-02-12 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,313 - root - INFO - Including PEPE/USDT on 2025-02-12 00:00:00+00:00 with score 0.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,313 - root - INFO - Including DOGE/USDT on 2025-02-12 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,313 - root - INFO - Including BNB/USDT on 2025-02-12 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,313 - root - INFO - Including DOT/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,320 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,322 - root - INFO - ASSET CHANGE DETECTED on 2025-02-13:
2025-06-27 20:46:01,322 - root - INFO -    Signal Date: 2025-02-12 (generated at 00:00 UTC)
2025-06-27 20:46:01,322 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-13 00:00 UTC (immediate)
2025-06-27 20:46:01,322 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:46:01,322 - root - INFO -    Selling: ['TRX/USDT']
2025-06-27 20:46:01,322 - root - INFO -    Buying: ['BNB/USDT']
2025-06-27 20:46:01,322 - root - INFO -    BNB/USDT buy price: $664.7200 (close price)
2025-06-27 20:46:01,327 - root - INFO - Including ETH/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,327 - root - INFO - Including BTC/USDT on 2025-02-13 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,327 - root - INFO - Including SOL/USDT on 2025-02-13 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,327 - root - INFO - Including SUI/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,327 - root - INFO - Including XRP/USDT on 2025-02-13 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,327 - root - INFO - Including AAVE/USDT on 2025-02-13 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,327 - root - INFO - Including AVAX/USDT on 2025-02-13 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,327 - root - INFO - Including ADA/USDT on 2025-02-13 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,327 - root - INFO - Including LINK/USDT on 2025-02-13 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,327 - root - INFO - Including TRX/USDT on 2025-02-13 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,330 - root - INFO - Including PEPE/USDT on 2025-02-13 00:00:00+00:00 with score 0.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,330 - root - INFO - Including DOGE/USDT on 2025-02-13 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,330 - root - INFO - Including BNB/USDT on 2025-02-13 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,330 - root - INFO - Including DOT/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,330 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,338 - root - INFO - Including ETH/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,338 - root - INFO - Including BTC/USDT on 2025-02-14 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,338 - root - INFO - Including SOL/USDT on 2025-02-14 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,338 - root - INFO - Including SUI/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,338 - root - INFO - Including XRP/USDT on 2025-02-14 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,338 - root - INFO - Including AAVE/USDT on 2025-02-14 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,340 - root - INFO - Including AVAX/USDT on 2025-02-14 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,340 - root - INFO - Including ADA/USDT on 2025-02-14 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,340 - root - INFO - Including LINK/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,340 - root - INFO - Including TRX/USDT on 2025-02-14 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,340 - root - INFO - Including PEPE/USDT on 2025-02-14 00:00:00+00:00 with score 0.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,340 - root - INFO - Including DOGE/USDT on 2025-02-14 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,340 - root - INFO - Including BNB/USDT on 2025-02-14 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,340 - root - INFO - Including DOT/USDT on 2025-02-14 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:46:01,346 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,354 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,362 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,370 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,375 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,378 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,386 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,403 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,418 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,427 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,435 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,435 - root - INFO - ASSET CHANGE DETECTED on 2025-02-25:
2025-06-27 20:46:01,435 - root - INFO -    Signal Date: 2025-02-24 (generated at 00:00 UTC)
2025-06-27 20:46:01,435 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-25 00:00 UTC (immediate)
2025-06-27 20:46:01,435 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:46:01,435 - root - INFO -    Selling: ['BNB/USDT']
2025-06-27 20:46:01,435 - root - INFO -    Buying: ['TRX/USDT']
2025-06-27 20:46:01,435 - root - INFO -    TRX/USDT buy price: $0.2309 (close price)
2025-06-27 20:46:01,445 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,451 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,460 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,467 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,476 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,484 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,484 - root - INFO - ASSET CHANGE DETECTED on 2025-03-03:
2025-06-27 20:46:01,484 - root - INFO -    Signal Date: 2025-03-02 (generated at 00:00 UTC)
2025-06-27 20:46:01,484 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-03 00:00 UTC (immediate)
2025-06-27 20:46:01,484 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:46:01,484 - root - INFO -    Selling: ['TRX/USDT']
2025-06-27 20:46:01,484 - root - INFO -    Buying: ['ADA/USDT']
2025-06-27 20:46:01,484 - root - INFO -    ADA/USDT buy price: $0.8578 (close price)
2025-06-27 20:46:01,493 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,501 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,517 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,528 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,533 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,549 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,562 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,562 - root - INFO - ASSET CHANGE DETECTED on 2025-03-10:
2025-06-27 20:46:01,562 - root - INFO -    Signal Date: 2025-03-09 (generated at 00:00 UTC)
2025-06-27 20:46:01,562 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-10 00:00 UTC (immediate)
2025-06-27 20:46:01,562 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:46:01,562 - root - INFO -    Selling: ['ADA/USDT']
2025-06-27 20:46:01,562 - root - INFO -    Buying: ['TRX/USDT']
2025-06-27 20:46:01,562 - root - INFO -    TRX/USDT buy price: $0.2292 (close price)
2025-06-27 20:46:01,576 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,582 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,598 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,607 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,628 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,643 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,643 - root - INFO - ASSET CHANGE DETECTED on 2025-03-16:
2025-06-27 20:46:01,645 - root - INFO -    Signal Date: 2025-03-15 (generated at 00:00 UTC)
2025-06-27 20:46:01,645 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-16 00:00 UTC (immediate)
2025-06-27 20:46:01,645 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:46:01,645 - root - INFO -    Selling: ['TRX/USDT']
2025-06-27 20:46:01,646 - root - INFO -    Buying: ['ADA/USDT']
2025-06-27 20:46:01,646 - root - INFO -    ADA/USDT buy price: $0.7049 (close price)
2025-06-27 20:46:01,656 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,656 - root - INFO - ASSET CHANGE DETECTED on 2025-03-17:
2025-06-27 20:46:01,656 - root - INFO -    Signal Date: 2025-03-16 (generated at 00:00 UTC)
2025-06-27 20:46:01,656 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-17 00:00 UTC (immediate)
2025-06-27 20:46:01,660 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:46:01,662 - root - INFO -    Selling: ['ADA/USDT']
2025-06-27 20:46:01,662 - root - INFO -    Buying: ['BNB/USDT']
2025-06-27 20:46:01,662 - root - INFO -    BNB/USDT buy price: $631.6900 (close price)
2025-06-27 20:46:01,685 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,705 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,720 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,736 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,749 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,759 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,769 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,781 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,791 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,804 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,804 - root - INFO - ASSET CHANGE DETECTED on 2025-03-27:
2025-06-27 20:46:01,804 - root - INFO -    Signal Date: 2025-03-26 (generated at 00:00 UTC)
2025-06-27 20:46:01,804 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-27 00:00 UTC (immediate)
2025-06-27 20:46:01,804 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:46:01,804 - root - INFO -    Selling: ['BNB/USDT']
2025-06-27 20:46:01,804 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-27 20:46:01,805 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-27 20:46:01,818 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,834 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,848 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,862 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,864 - root - INFO - ASSET CHANGE DETECTED on 2025-03-31:
2025-06-27 20:46:01,864 - root - INFO -    Signal Date: 2025-03-30 (generated at 00:00 UTC)
2025-06-27 20:46:01,865 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-31 00:00 UTC (immediate)
2025-06-27 20:46:01,865 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:46:01,865 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-27 20:46:01,865 - root - INFO -    Buying: ['BNB/USDT']
2025-06-27 20:46:01,865 - root - INFO -    BNB/USDT buy price: $604.8100 (close price)
2025-06-27 20:46:01,883 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,899 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,915 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,935 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,935 - root - INFO - ASSET CHANGE DETECTED on 2025-04-04:
2025-06-27 20:46:01,937 - root - INFO -    Signal Date: 2025-04-03 (generated at 00:00 UTC)
2025-06-27 20:46:01,937 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-04 00:00 UTC (immediate)
2025-06-27 20:46:01,937 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:46:01,937 - root - INFO -    Selling: ['BNB/USDT']
2025-06-27 20:46:01,937 - root - INFO -    Buying: ['TRX/USDT']
2025-06-27 20:46:01,938 - root - INFO -    TRX/USDT buy price: $0.2390 (close price)
2025-06-27 20:46:01,956 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,970 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:01,997 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,016 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,038 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,054 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,070 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,085 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,104 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,132 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,148 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,162 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,221 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,245 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,255 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,270 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,270 - root - INFO - ASSET CHANGE DETECTED on 2025-04-20:
2025-06-27 20:46:02,270 - root - INFO -    Signal Date: 2025-04-19 (generated at 00:00 UTC)
2025-06-27 20:46:02,270 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-20 00:00 UTC (immediate)
2025-06-27 20:46:02,271 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:46:02,271 - root - INFO -    Selling: ['TRX/USDT']
2025-06-27 20:46:02,271 - root - INFO -    Buying: ['SOL/USDT']
2025-06-27 20:46:02,271 - root - INFO -    SOL/USDT buy price: $137.8600 (close price)
2025-06-27 20:46:02,288 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,303 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,318 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,318 - root - INFO - ASSET CHANGE DETECTED on 2025-04-23:
2025-06-27 20:46:02,318 - root - INFO -    Signal Date: 2025-04-22 (generated at 00:00 UTC)
2025-06-27 20:46:02,318 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-23 00:00 UTC (immediate)
2025-06-27 20:46:02,318 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:46:02,318 - root - INFO -    Selling: ['SOL/USDT']
2025-06-27 20:46:02,319 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-27 20:46:02,319 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-27 20:46:02,331 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,331 - root - INFO - ASSET CHANGE DETECTED on 2025-04-24:
2025-06-27 20:46:02,331 - root - INFO -    Signal Date: 2025-04-23 (generated at 00:00 UTC)
2025-06-27 20:46:02,331 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-24 00:00 UTC (immediate)
2025-06-27 20:46:02,331 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:46:02,331 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-27 20:46:02,331 - root - INFO -    Buying: ['SUI/USDT']
2025-06-27 20:46:02,331 - root - INFO -    SUI/USDT buy price: $3.3437 (close price)
2025-06-27 20:46:02,349 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,359 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,373 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,395 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,418 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,434 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,448 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,465 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,480 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,494 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,505 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,517 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,531 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,543 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,552 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,566 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,566 - root - INFO - ASSET CHANGE DETECTED on 2025-05-10:
2025-06-27 20:46:02,566 - root - INFO -    Signal Date: 2025-05-09 (generated at 00:00 UTC)
2025-06-27 20:46:02,566 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-10 00:00 UTC (immediate)
2025-06-27 20:46:02,566 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:46:02,566 - root - INFO -    Selling: ['SUI/USDT']
2025-06-27 20:46:02,566 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-27 20:46:02,566 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-27 20:46:02,580 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,594 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,600 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,621 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,637 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,653 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,667 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,678 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,693 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,703 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,713 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,713 - root - INFO - ASSET CHANGE DETECTED on 2025-05-21:
2025-06-27 20:46:02,713 - root - INFO -    Signal Date: 2025-05-20 (generated at 00:00 UTC)
2025-06-27 20:46:02,713 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-21 00:00 UTC (immediate)
2025-06-27 20:46:02,713 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:46:02,713 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-27 20:46:02,713 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-27 20:46:02,713 - root - INFO -    AAVE/USDT buy price: $247.5400 (close price)
2025-06-27 20:46:02,728 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,728 - root - INFO - ASSET CHANGE DETECTED on 2025-05-22:
2025-06-27 20:46:02,730 - root - INFO -    Signal Date: 2025-05-21 (generated at 00:00 UTC)
2025-06-27 20:46:02,730 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-22 00:00 UTC (immediate)
2025-06-27 20:46:02,730 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:46:02,730 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-27 20:46:02,730 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-27 20:46:02,730 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-27 20:46:02,740 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,751 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,759 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,767 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,767 - root - INFO - ASSET CHANGE DETECTED on 2025-05-26:
2025-06-27 20:46:02,767 - root - INFO -    Signal Date: 2025-05-25 (generated at 00:00 UTC)
2025-06-27 20:46:02,767 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-26 00:00 UTC (immediate)
2025-06-27 20:46:02,767 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:46:02,767 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-27 20:46:02,767 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-27 20:46:02,767 - root - INFO -    AAVE/USDT buy price: $267.5200 (close price)
2025-06-27 20:46:02,777 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,784 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,800 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,808 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,818 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,833 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,843 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,849 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,865 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,883 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,898 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,907 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,914 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,930 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,938 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,946 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,958 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,962 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,979 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,979 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:02,996 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:03,003 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:03,012 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:03,021 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:03,028 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:03,036 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:03,036 - root - INFO - ASSET CHANGE DETECTED on 2025-06-21:
2025-06-27 20:46:03,036 - root - INFO -    Signal Date: 2025-06-20 (generated at 00:00 UTC)
2025-06-27 20:46:03,036 - root - INFO -    AUTOMATIC EXECUTION: 2025-06-21 00:00 UTC (immediate)
2025-06-27 20:46:03,036 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:46:03,036 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-27 20:46:03,036 - root - INFO -    Buying: ['TRX/USDT']
2025-06-27 20:46:03,036 - root - INFO -    TRX/USDT buy price: $0.2710 (close price)
2025-06-27 20:46:03,044 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:03,235 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:03,247 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:03,330 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:03,343 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:46:03,386 - root - INFO - Entry trade at 2025-02-11 00:00:00+00:00: TRX/USDT
2025-06-27 20:46:03,386 - root - INFO - Swap trade at 2025-02-13 00:00:00+00:00: TRX/USDT -> BNB/USDT
2025-06-27 20:46:03,386 - root - INFO - Swap trade at 2025-02-25 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-27 20:46:03,386 - root - INFO - Swap trade at 2025-03-03 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-06-27 20:46:03,390 - root - INFO - Swap trade at 2025-03-10 00:00:00+00:00: ADA/USDT -> TRX/USDT
2025-06-27 20:46:03,390 - root - INFO - Swap trade at 2025-03-16 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-06-27 20:46:03,390 - root - INFO - Swap trade at 2025-03-17 00:00:00+00:00: ADA/USDT -> BNB/USDT
2025-06-27 20:46:03,390 - root - INFO - Swap trade at 2025-03-27 00:00:00+00:00: BNB/USDT -> PEPE/USDT
2025-06-27 20:46:03,390 - root - INFO - Swap trade at 2025-03-31 00:00:00+00:00: PEPE/USDT -> BNB/USDT
2025-06-27 20:46:03,390 - root - INFO - Swap trade at 2025-04-04 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-27 20:46:03,390 - root - INFO - Swap trade at 2025-04-20 00:00:00+00:00: TRX/USDT -> SOL/USDT
2025-06-27 20:46:03,390 - root - INFO - Swap trade at 2025-04-23 00:00:00+00:00: SOL/USDT -> PEPE/USDT
2025-06-27 20:46:03,390 - root - INFO - Swap trade at 2025-04-24 00:00:00+00:00: PEPE/USDT -> SUI/USDT
2025-06-27 20:46:03,390 - root - INFO - Swap trade at 2025-05-10 00:00:00+00:00: SUI/USDT -> PEPE/USDT
2025-06-27 20:46:03,390 - root - INFO - Swap trade at 2025-05-21 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-27 20:46:03,390 - root - INFO - Swap trade at 2025-05-22 00:00:00+00:00: AAVE/USDT -> PEPE/USDT
2025-06-27 20:46:03,390 - root - INFO - Swap trade at 2025-05-26 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-27 20:46:03,395 - root - INFO - Swap trade at 2025-06-21 00:00:00+00:00: AAVE/USDT -> TRX/USDT
2025-06-27 20:46:03,395 - root - INFO - Total trades: 18 (Entries: 1, Exits: 0, Swaps: 17)
2025-06-27 20:46:03,395 - root - INFO - Strategy execution completed in 2s
2025-06-27 20:46:03,395 - root - INFO - DEBUG: self.elapsed_time = 2.14742112159729 seconds
2025-06-27 20:46:03,403 - root - INFO - Saved allocation history to allocation_history_1d_1d_no_mtpi_no_rebal_manual_inversion_imcumbent_2025-02-10.csv
2025-06-27 20:46:03,403 - root - INFO - Strategy equity curve starts at: 2025-02-10
2025-06-27 20:46:03,403 - root - INFO - Assets included in buy-and-hold comparison:
2025-06-27 20:46:03,403 - root - INFO -   ETH/USDT: First available date 2024-12-12
2025-06-27 20:46:03,403 - root - INFO -   BTC/USDT: First available date 2024-12-12
2025-06-27 20:46:03,403 - root - INFO -   SOL/USDT: First available date 2024-12-12
2025-06-27 20:46:03,403 - root - INFO -   SUI/USDT: First available date 2024-12-12
2025-06-27 20:46:03,409 - root - INFO -   XRP/USDT: First available date 2024-12-12
2025-06-27 20:46:03,409 - root - INFO -   AAVE/USDT: First available date 2024-12-12
2025-06-27 20:46:03,409 - root - INFO -   AVAX/USDT: First available date 2024-12-12
2025-06-27 20:46:03,409 - root - INFO -   ADA/USDT: First available date 2024-12-12
2025-06-27 20:46:03,409 - root - INFO -   LINK/USDT: First available date 2024-12-12
2025-06-27 20:46:03,410 - root - INFO -   TRX/USDT: First available date 2024-12-12
2025-06-27 20:46:03,410 - root - INFO -   PEPE/USDT: First available date 2024-12-12
2025-06-27 20:46:03,410 - root - INFO -   DOGE/USDT: First available date 2024-12-12
2025-06-27 20:46:03,410 - root - INFO -   BNB/USDT: First available date 2024-12-12
2025-06-27 20:46:03,410 - root - INFO -   DOT/USDT: First available date 2024-12-12
2025-06-27 20:46:03,411 - root - INFO - Using provided start date for normalization: 2025-02-10 00:00:00+00:00
2025-06-27 20:46:03,414 - root - INFO - Normalized ETH/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 20:46:03,414 - root - INFO - Normalized BTC/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 20:46:03,414 - root - INFO - Normalized SOL/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 20:46:03,414 - root - INFO - Normalized SUI/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 20:46:03,414 - root - INFO - Normalized XRP/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 20:46:03,419 - root - INFO - Normalized AAVE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 20:46:03,419 - root - INFO - Normalized AVAX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 20:46:03,422 - root - INFO - Normalized ADA/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 20:46:03,422 - root - INFO - Normalized LINK/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 20:46:03,422 - root - INFO - Normalized TRX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 20:46:03,427 - root - INFO - Normalized PEPE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 20:46:03,427 - root - INFO - Normalized DOGE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 20:46:03,427 - root - INFO - Normalized BNB/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 20:46:03,427 - root - INFO - Normalized DOT/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 20:46:03,427 - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 197 points
2025-06-27 20:46:03,427 - root - INFO - ETH/USDT B&H total return: -9.22%
2025-06-27 20:46:03,435 - root - INFO - Added equity_curve to bnh_metrics for BTC/USDT with 197 points
2025-06-27 20:46:03,435 - root - INFO - BTC/USDT B&H total return: 9.77%
2025-06-27 20:46:03,440 - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 197 points
2025-06-27 20:46:03,440 - root - INFO - SOL/USDT B&H total return: -30.63%
2025-06-27 20:46:03,444 - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 197 points
2025-06-27 20:46:03,444 - root - INFO - SUI/USDT B&H total return: -19.11%
2025-06-27 20:46:03,446 - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 197 points
2025-06-27 20:46:03,446 - root - INFO - XRP/USDT B&H total return: -13.13%
2025-06-27 20:46:03,449 - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 197 points
2025-06-27 20:46:03,449 - root - INFO - AAVE/USDT B&H total return: -0.91%
2025-06-27 20:46:03,453 - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 197 points
2025-06-27 20:46:03,453 - root - INFO - AVAX/USDT B&H total return: -32.88%
2025-06-27 20:46:03,455 - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 197 points
2025-06-27 20:46:03,455 - root - INFO - ADA/USDT B&H total return: -22.16%
2025-06-27 20:46:03,461 - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 197 points
2025-06-27 20:46:03,461 - root - INFO - LINK/USDT B&H total return: -31.21%
2025-06-27 20:46:03,463 - root - INFO - Added equity_curve to bnh_metrics for TRX/USDT with 197 points
2025-06-27 20:46:03,465 - root - INFO - TRX/USDT B&H total return: 10.07%
2025-06-27 20:46:03,465 - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 197 points
2025-06-27 20:46:03,467 - root - INFO - PEPE/USDT B&H total return: -4.48%
2025-06-27 20:46:03,467 - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 197 points
2025-06-27 20:46:03,467 - root - INFO - DOGE/USDT B&H total return: -37.35%
2025-06-27 20:46:03,467 - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 197 points
2025-06-27 20:46:03,467 - root - INFO - BNB/USDT B&H total return: 3.82%
2025-06-27 20:46:03,467 - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 197 points
2025-06-27 20:46:03,467 - root - INFO - DOT/USDT B&H total return: -31.91%
2025-06-27 20:46:03,467 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-27 20:46:03,484 - root - INFO - Configuration loaded successfully.
2025-06-27 20:46:03,500 - root - INFO - Using colored segments for single-asset strategy visualization
2025-06-27 20:46:03,623 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-27 20:46:03,631 - root - INFO - Configuration loaded successfully.
2025-06-27 20:46:04,644 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:46:05,594 - root - INFO - Added ETH/USDT buy-and-hold curve with 197 points
2025-06-27 20:46:05,594 - root - INFO - Added BTC/USDT buy-and-hold curve with 197 points
2025-06-27 20:46:05,594 - root - INFO - Added SOL/USDT buy-and-hold curve with 197 points
2025-06-27 20:46:05,594 - root - INFO - Added SUI/USDT buy-and-hold curve with 197 points
2025-06-27 20:46:05,594 - root - INFO - Added XRP/USDT buy-and-hold curve with 197 points
2025-06-27 20:46:05,594 - root - INFO - Added AAVE/USDT buy-and-hold curve with 197 points
2025-06-27 20:46:05,594 - root - INFO - Added AVAX/USDT buy-and-hold curve with 197 points
2025-06-27 20:46:05,594 - root - INFO - Added ADA/USDT buy-and-hold curve with 197 points
2025-06-27 20:46:05,594 - root - INFO - Added LINK/USDT buy-and-hold curve with 197 points
2025-06-27 20:46:05,594 - root - INFO - Added TRX/USDT buy-and-hold curve with 197 points
2025-06-27 20:46:05,594 - root - INFO - Added PEPE/USDT buy-and-hold curve with 197 points
2025-06-27 20:46:05,594 - root - INFO - Added DOGE/USDT buy-and-hold curve with 197 points
2025-06-27 20:46:05,594 - root - INFO - Added BNB/USDT buy-and-hold curve with 197 points
2025-06-27 20:46:05,594 - root - INFO - Added DOT/USDT buy-and-hold curve with 197 points
2025-06-27 20:46:05,594 - root - INFO - Added 14 buy-and-hold curves to results
2025-06-27 20:46:05,594 - root - INFO -   - ETH/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 20:46:05,594 - root - INFO -   - BTC/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 20:46:05,594 - root - INFO -   - SOL/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 20:46:05,594 - root - INFO -   - SUI/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 20:46:05,594 - root - INFO -   - XRP/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 20:46:05,594 - root - INFO -   - AAVE/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 20:46:05,594 - root - INFO -   - AVAX/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 20:46:05,602 - root - INFO -   - ADA/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 20:46:05,602 - root - INFO -   - LINK/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 20:46:05,602 - root - INFO -   - TRX/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 20:46:05,602 - root - INFO -   - PEPE/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 20:46:05,602 - root - INFO -   - DOGE/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 20:46:05,602 - root - INFO -   - BNB/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 20:46:05,602 - root - INFO -   - DOT/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 20:46:05,619 - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-06-27 20:46:05,619 - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-27 20:46:05,619 - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-06-27 20:46:05,619 - root - INFO - MTPI disabled - skipping MTPI signal calculation
2025-06-27 20:46:05,626 - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-06-27 20:46:05,626 - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 8.0, 'BTC/USDT': 13.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-27 20:46:05,626 - root - ERROR - [DEBUG] CONVERSION - Original USDT scores: {'ETH/USDT': 8.0, 'BTC/USDT': 13.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-27 20:46:05,626 - root - ERROR - [DEBUG] CONVERSION - Original USDT keys order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-27 20:46:05,626 - root - ERROR - [DEBUG] CONVERSION - Conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-27 20:46:05,626 - root - ERROR - [DEBUG] CONVERSION - ETH/USDT -> ETH/EUR (score: 8.0)
2025-06-27 20:46:05,626 - root - ERROR - [DEBUG] CONVERSION - BTC/USDT -> BTC/EUR (score: 13.0)
2025-06-27 20:46:05,626 - root - ERROR - [DEBUG] CONVERSION - SOL/USDT -> SOL/EUR (score: 6.0)
2025-06-27 20:46:05,626 - root - ERROR - [DEBUG] CONVERSION - SUI/USDT -> SUI/EUR (score: 2.0)
2025-06-27 20:46:05,626 - root - ERROR - [DEBUG] CONVERSION - XRP/USDT -> XRP/EUR (score: 9.0)
2025-06-27 20:46:05,626 - root - ERROR - [DEBUG] CONVERSION - AAVE/USDT -> AAVE/EUR (score: 10.0)
2025-06-27 20:46:05,626 - root - ERROR - [DEBUG] CONVERSION - AVAX/USDT -> AVAX/EUR (score: 3.0)
2025-06-27 20:46:05,626 - root - ERROR - [DEBUG] CONVERSION - ADA/USDT -> ADA/EUR (score: 4.0)
2025-06-27 20:46:05,626 - root - ERROR - [DEBUG] CONVERSION - LINK/USDT -> LINK/EUR (score: 7.0)
2025-06-27 20:46:05,626 - root - ERROR - [DEBUG] CONVERSION - TRX/USDT -> TRX/EUR (score: 12.0)
2025-06-27 20:46:05,635 - root - ERROR - [DEBUG] CONVERSION - PEPE/USDT -> PEPE/EUR (score: 0.0)
2025-06-27 20:46:05,635 - root - ERROR - [DEBUG] CONVERSION - DOGE/USDT -> DOGE/EUR (score: 5.0)
2025-06-27 20:46:05,635 - root - ERROR - [DEBUG] CONVERSION - BNB/USDT -> BNB/EUR (score: 11.0)
2025-06-27 20:46:05,635 - root - ERROR - [DEBUG] CONVERSION - DOT/USDT -> DOT/EUR (score: 1.0)
2025-06-27 20:46:05,635 - root - ERROR - [DEBUG] CONVERSION - Final EUR scores: {'ETH/EUR': 8.0, 'BTC/EUR': 13.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 4.0, 'LINK/EUR': 7.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-27 20:46:05,635 - root - ERROR - [DEBUG] CONVERSION - Final EUR keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-27 20:46:05,635 - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 8.0, 'BTC/EUR': 13.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 4.0, 'LINK/EUR': 7.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-27 20:46:05,638 - root - INFO - Saved metrics to new file: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250620_run_********_204545.csv
2025-06-27 20:46:05,638 - root - INFO - Saved performance metrics to CSV: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250620_run_********_204545.csv
2025-06-27 20:46:05,638 - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-06-27 20:46:05,643 - root - INFO - Results type: <class 'dict'>
2025-06-27 20:46:05,643 - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-06-27 20:46:05,643 - root - INFO - Success flag set to: True
2025-06-27 20:46:05,643 - root - INFO - Message set to: Strategy calculation completed successfully
2025-06-27 20:46:05,643 - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 197 entries
2025-06-27 20:46:05,643 - root - INFO -   - buy_hold_curves: dict with 14 entries
2025-06-27 20:46:05,643 - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 197 entries
2025-06-27 20:46:05,643 - root - INFO -   - mtpi_signals: <class 'NoneType'>
2025-06-27 20:46:05,643 - root - INFO -   - mtpi_score: <class 'NoneType'>
2025-06-27 20:46:05,643 - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 197 entries
2025-06-27 20:46:05,643 - root - INFO -   - performance_metrics: dict with 3 entries
2025-06-27 20:46:05,643 - root - INFO -   - metrics_file: <class 'str'>
2025-06-27 20:46:05,643 - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-06-27 20:46:05,643 - root - INFO -   - success: <class 'bool'>
2025-06-27 20:46:05,643 - root - INFO -   - message: <class 'str'>
2025-06-27 20:46:05,643 - root - INFO - MTPI disabled - using default bullish signal (1) for trading execution
2025-06-27 20:46:05,643 - root - ERROR - [DEBUG] STRATEGY RESULTS - Available keys: ['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message']
2025-06-27 20:46:05,643 - root - INFO - [DEBUG] ASSET SELECTION - Extracted best_asset from best_asset_series: TRX/EUR
2025-06-27 20:46:05,643 - root - INFO - [DEBUG] ASSET SELECTION - Last 5 entries in best_asset_series: 2025-06-22 00:00:00+00:00    TRX/EUR
2025-06-23 00:00:00+00:00    TRX/EUR
2025-06-24 00:00:00+00:00    TRX/EUR
2025-06-25 00:00:00+00:00    TRX/EUR
2025-06-26 00:00:00+00:00    TRX/EUR
dtype: object
2025-06-27 20:46:05,643 - root - ERROR - [DEBUG] ASSET SELECTION - Latest scores available: {'ETH/EUR': 8.0, 'BTC/EUR': 13.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 4.0, 'LINK/EUR': 7.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-27 20:46:05,643 - root - ERROR - [DEBUG] ASSET SELECTION - Scores dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-27 20:46:05,651 - root - INFO - [DEBUG] TIE-BREAKING - Strategy: imcumbent
2025-06-27 20:46:05,651 - root - ERROR - [DEBUG] ASSET SELECTION - Maximum score: 13.0
2025-06-27 20:46:05,651 - root - ERROR - [DEBUG] ASSET SELECTION - Assets with max score: ['BTC/EUR']
2025-06-27 20:46:05,651 - root - ERROR - [DEBUG] NO TIE - Single winner: BTC/EUR
2025-06-27 20:46:05,667 - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
       'BNB/EUR', 'DOT/EUR'],
      dtype='object')
2025-06-27 20:46:05,667 - root - INFO - Last row columns: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-27 20:46:05,667 - root - INFO - Single asset strategy with best asset: TRX/EUR
2025-06-27 20:46:05,667 - root - INFO - [DEBUG] FINAL ASSET SELECTION SUMMARY:
2025-06-27 20:46:05,667 - root - INFO - [DEBUG]   - Best asset selected: TRX/EUR
2025-06-27 20:46:05,667 - root - INFO - [DEBUG]   - Assets held: {'TRX/EUR': 1.0}
2025-06-27 20:46:05,667 - root - INFO - [DEBUG]   - MTPI signal: 1
2025-06-27 20:46:05,667 - root - INFO - [DEBUG]   - Use MTPI signal: False
2025-06-27 20:46:05,667 - root - WARNING - [DEBUG] TRX WAS SELECTED - INVESTIGATING WHY!
2025-06-27 20:46:05,667 - root - INFO - Executing single-asset strategy with best asset: TRX/EUR
2025-06-27 20:46:05,667 - root - INFO - Executing strategy signal: best_asset=TRX/EUR, mtpi_signal=1, mode=paper
2025-06-27 20:46:05,667 - root - INFO - Incremented daily trade counter for TRX/EUR: 1/5
2025-06-27 20:46:05,667 - root - INFO - ASSET SELECTION - Attempting to enter position for selected asset: TRX/EUR
2025-06-27 20:46:05,667 - root - INFO - Attempting to enter position for TRX/EUR in paper mode
2025-06-27 20:46:05,667 - root - INFO - [DEBUG] TRADE - TRX/EUR: Starting enter_position attempt
2025-06-27 20:46:05,675 - root - INFO - [DEBUG] TRADE - TRX/EUR: Trading mode: paper
2025-06-27 20:46:05,675 - root - INFO - TRADE ATTEMPT - TRX/EUR: Getting current market price...
2025-06-27 20:46:05,675 - root - INFO - [DEBUG] PRICE - TRX/EUR: Starting get_current_price
2025-06-27 20:46:05,675 - root - INFO - [DEBUG] PRICE - TRX/EUR: Exchange ID: bitvavo
2025-06-27 20:46:05,675 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Initializing exchange bitvavo
2025-06-27 20:46:05,675 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Exchange initialized successfully
2025-06-27 20:46:05,675 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Exchange markets not loaded, loading now...
2025-06-27 20:46:05,972 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Symbol found after loading markets
2025-06-27 20:46:05,974 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Attempting to fetch ticker...
2025-06-27 20:46:06,013 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Ticker fetched successfully
2025-06-27 20:46:06,013 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Ticker data: {'symbol': 'TRX/EUR', 'timestamp': 1751049962586, 'datetime': '2025-06-27T18:46:02.586Z', 'high': 0.23315, 'low': 0.2309, 'bid': 0.23243, 'bidVolume': 4545.428344, 'ask': 0.2325, 'askVolume': 13140.0, 'vwap': 0.23193632199324057, 'open': 0.23116, 'close': 0.23257, 'last': 0.23257, 'previousClose': None, 'change': 0.00141, 'percentage': 0.6099671223395051, 'average': 0.231865, 'baseVolume': 1274902.672258, 'quoteVolume': 295696.2367028743, 'info': {'market': 'TRX-EUR', 'startTimestamp': 1750963562586, 'timestamp': 1751049962586, 'open': '0.23116', 'openTimestamp': 1750963644281, 'high': '0.23315', 'low': '0.2309', 'last': '0.23257', 'closeTimestamp': 1751049918653, 'bid': '0.2324300', 'bidSize': '4545.428344', 'ask': '0.2325000', 'askSize': '13140', 'volume': '1274902.672258', 'volumeQuote': '295696.23670287433'}, 'indexPrice': None, 'markPrice': None}
2025-06-27 20:46:06,013 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Last price: 0.23257
2025-06-27 20:46:06,013 - root - INFO - [DEBUG] TRADE - TRX/EUR: get_current_price returned: 0.23257
2025-06-27 20:46:06,014 - root - INFO - [DEBUG] TRADE - TRX/EUR: Price type: <class 'float'>
2025-06-27 20:46:06,014 - root - INFO - [DEBUG] TRADE - TRX/EUR: Price evaluation - not price: False
2025-06-27 20:46:06,015 - root - INFO - [DEBUG] TRADE - TRX/EUR: Price evaluation - price <= 0: False
2025-06-27 20:46:06,015 - root - INFO - TRADE ATTEMPT - TRX/EUR: Current price: 0.********
2025-06-27 20:46:06,015 - root - INFO - Available balance for EUR: 100.********
2025-06-27 20:46:06,026 - root - INFO - Loaded market info for 176 trading pairs
2025-06-27 20:46:06,026 - root - INFO - Calculated position size for TRX/EUR: 42.******** (using 10% of 100, accounting for fees)
2025-06-27 20:46:06,026 - root - INFO - Calculated position size: 42.******** TRX
2025-06-27 20:46:06,026 - root - INFO - Entering position for TRX/EUR: 42.******** units at 0.******** (value: 9.******** EUR)
2025-06-27 20:46:06,026 - root - INFO - Executing paper market buy order for TRX/EUR
2025-06-27 20:46:06,029 - root - INFO - Paper trading buy for TRX/EUR: original=42.********, adjusted=42.********, after_fee=42.********
2025-06-27 20:46:06,029 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-27 20:46:06,029 - root - INFO - Created paper market buy order: TRX/EUR, amount: 42.********191082, price: 0.23257
2025-06-27 20:46:06,029 - root - INFO - Filled amount: 42.******** TRX
2025-06-27 20:46:06,029 - root - INFO - Order fee: 0.******** EUR
2025-06-27 20:46:06,030 - root - INFO - Successfully entered position: TRX/EUR, amount: 42.********, price: 0.********
2025-06-27 20:46:06,030 - root - INFO - Trade executed: BUY TRX/EUR, amount=42.********, price=0.********, filled=42.********
2025-06-27 20:46:06,030 - root - INFO -   Fee: 0.******** EUR
2025-06-27 20:46:06,030 - root - INFO - TRADE SUCCESS - TRX/EUR: Successfully updated current asset
2025-06-27 20:46:06,033 - root - INFO - Trade executed: BUY TRX/EUR, amount=42.********, price=0.********, filled=42.********
2025-06-27 20:46:06,033 - root - INFO -   Fee: 0.******** EUR
2025-06-27 20:46:06,033 - root - INFO - Single-asset trade result logged to trade log file
2025-06-27 20:46:06,033 - root - INFO - Trade executed: {'success': True, 'symbol': 'TRX/EUR', 'side': 'buy', 'amount': 42.78281807627811, 'price': 0.23257, 'order': {'id': 'paper-1751049966-TRX/EUR-buy-42.********191082', 'symbol': 'TRX/EUR', 'side': 'buy', 'type': 'market', 'amount': 42.********191082, 'price': 0.23257, 'cost': 9.90025, 'fee': {'cost': 0.********, 'currency': 'EUR', 'rate': 0.001}, 'status': 'closed', 'filled': 42.********191082, 'remaining': 0, 'timestamp': 1751049966029, 'datetime': '2025-06-27T20:46:06.029198', 'trades': [], 'average': 0.23257, 'average_price': 0.23257}, 'filled_amount': 42.********191082, 'fee': {'cost': 0.********, 'currency': 'EUR', 'rate': 0.001}, 'quote_currency': 'EUR', 'base_currency': 'TRX', 'timestamp': '2025-06-27T20:46:06.030938'}
2025-06-27 20:46:06,100 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-27 20:46:06,103 - root - INFO - Asset selection logged: 1 assets selected with single_asset allocation
2025-06-27 20:46:06,103 - root - INFO - Asset scores (sorted by score):
2025-06-27 20:46:06,103 - root - INFO -   BTC/EUR: score=13.0, status=NOT SELECTED, weight=0.00
2025-06-27 20:46:06,103 - root - INFO -   TRX/EUR: score=12.0, status=SELECTED, weight=1.00
2025-06-27 20:46:06,103 - root - INFO -   BNB/EUR: score=11.0, status=NOT SELECTED, weight=0.00
2025-06-27 20:46:06,103 - root - INFO -   AAVE/EUR: score=10.0, status=NOT SELECTED, weight=0.00
2025-06-27 20:46:06,103 - root - INFO -   XRP/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-27 20:46:06,103 - root - INFO -   ETH/EUR: score=8.0, status=NOT SELECTED, weight=0.00
2025-06-27 20:46:06,103 - root - INFO -   LINK/EUR: score=7.0, status=NOT SELECTED, weight=0.00
2025-06-27 20:46:06,106 - root - INFO -   SOL/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-27 20:46:06,106 - root - INFO -   DOGE/EUR: score=5.0, status=NOT SELECTED, weight=0.00
2025-06-27 20:46:06,106 - root - INFO -   ADA/EUR: score=4.0, status=NOT SELECTED, weight=0.00
2025-06-27 20:46:06,106 - root - INFO -   AVAX/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-27 20:46:06,107 - root - INFO -   SUI/EUR: score=2.0, status=NOT SELECTED, weight=0.00
2025-06-27 20:46:06,107 - root - INFO -   DOT/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-06-27 20:46:06,107 - root - INFO -   PEPE/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-06-27 20:46:06,107 - root - INFO - Rejected assets:
2025-06-27 20:46:06,107 - root - INFO -   BTC/EUR: reason=Failed to trade, score=13.0, rank=1
2025-06-27 20:46:06,107 - root - WARNING -   HIGH-SCORING ASSET REJECTED: BTC/EUR (rank 1, score 13.0) - Failed to trade
2025-06-27 20:46:06,108 - root - INFO - Asset selection logged with 14 assets scored and 1 assets selected
2025-06-27 20:46:06,108 - root - INFO - MTPI disabled - skipping MTPI score extraction
2025-06-27 20:46:06,108 - root - INFO - Extracted asset scores: {'ETH/EUR': 8.0, 'BTC/EUR': 13.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 4.0, 'LINK/EUR': 7.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-27 20:46:06,159 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-27 20:46:06,161 - root - INFO - Strategy execution completed successfully in 20.97 seconds
2025-06-27 20:46:06,165 - root - INFO - Saved recovery state to data/state\recovery_state.json
2025-06-27 20:46:14,779 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:46:24,814 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:46:34,849 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:46:44,880 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:46:54,900 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:47:04,952 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:47:14,990 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:47:25,025 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:47:35,057 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:47:45,073 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:47:55,090 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:48:05,166 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:48:15,200 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:48:25,233 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:48:35,269 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:48:45,307 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:48:55,341 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:49:05,380 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:49:15,391 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:49:25,411 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:49:35,428 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:49:45,441 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:49:55,455 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:50:05,485 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:50:15,500 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:50:25,512 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:50:35,589 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:50:45,629 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:50:55,762 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:51:05,799 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:51:15,836 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:51:25,870 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:51:35,914 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:51:45,923 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:51:55,972 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:52:06,009 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:52:16,045 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:52:26,080 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:52:36,115 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:52:46,127 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:52:56,187 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:53:06,220 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:53:16,227 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:53:26,293 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:53:36,331 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:53:46,361 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:53:56,394 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:54:06,429 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:54:16,453 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:54:26,465 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:54:36,480 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:54:46,494 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:54:56,502 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:55:06,524 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:55:16,575 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:55:26,609 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:55:36,633 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:55:46,643 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:55:56,718 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:56:06,752 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:56:16,784 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:56:26,818 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:56:36,860 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:56:46,893 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:56:56,928 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:57:06,951 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:57:16,995 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:57:27,034 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:57:37,067 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:57:47,109 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:57:57,132 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:58:07,177 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:58:17,192 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:58:27,241 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:58:37,280 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:58:47,315 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:58:57,330 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:59:07,383 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:59:17,411 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:59:27,445 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:59:37,490 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:59:47,503 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:59:57,518 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:00:07,598 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:00:17,633 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:00:27,667 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:00:37,714 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:00:47,739 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:00:57,773 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:01:07,787 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:01:17,798 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:01:27,814 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:01:37,826 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:01:47,843 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:01:57,881 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:02:07,917 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:02:17,960 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:02:28,088 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:02:38,227 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:02:48,264 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:02:58,299 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:03:08,330 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:03:18,346 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:03:28,400 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:03:38,433 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:03:48,447 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:03:58,508 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:04:08,644 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:04:18,669 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:04:28,688 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:04:38,706 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:04:48,720 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:04:58,740 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:05:08,754 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:05:18,790 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:05:28,820 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:05:38,834 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:05:48,896 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:05:58,930 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:06:08,962 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:06:19,003 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:06:29,036 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:06:39,071 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:06:49,202 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:06:59,245 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:07:09,279 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:07:19,411 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:07:29,452 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:07:39,480 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:07:49,502 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:07:59,512 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:08:09,593 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:08:19,628 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:08:29,660 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:08:39,690 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:08:49,700 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:08:59,717 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:09:09,736 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:09:19,750 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:09:29,772 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:09:39,803 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:09:49,845 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:09:59,859 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:10:09,876 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:10:19,949 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:10:29,963 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:10:39,986 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:10:50,051 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:11:00,087 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:11:10,121 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:11:20,141 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:11:30,190 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:11:40,230 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:11:50,264 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:12:00,292 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:12:10,336 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:12:20,369 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:12:30,398 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:12:40,440 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:12:50,471 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:13:00,509 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:13:10,544 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:13:20,580 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:13:30,624 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:13:40,648 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:13:50,687 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:14:00,723 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:14:10,737 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:14:20,793 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:14:33,797 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:14:43,841 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:14:53,859 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:15:03,876 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:15:13,942 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:15:23,956 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:15:34,008 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:15:44,044 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:15:54,079 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:16:04,115 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:16:14,122 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:16:24,139 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:16:34,213 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:16:44,233 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:16:54,246 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:17:04,258 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:17:14,361 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:17:24,393 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:17:34,410 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:17:44,427 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:17:54,498 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:18:04,518 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:18:14,670 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:18:24,687 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:18:34,703 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:18:44,779 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:18:54,815 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:19:04,829 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:19:14,880 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:19:24,939 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:19:34,956 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:19:44,970 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:19:54,986 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:20:05,060 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:20:15,096 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:20:25,125 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:20:35,165 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:20:45,181 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:20:55,194 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:21:05,205 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:21:15,220 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:21:25,343 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:21:35,352 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:21:45,369 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:21:55,385 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:22:05,736 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:22:15,751 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:22:25,857 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:22:35,894 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:22:45,929 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:22:55,964 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:23:06,103 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:23:16,138 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:23:26,170 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:23:36,210 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:23:46,241 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:23:56,279 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:24:06,419 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:24:16,452 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:24:26,467 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:24:36,727 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:24:46,763 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:24:56,776 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:25:06,833 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:25:16,849 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:25:26,862 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:25:36,875 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:25:46,893 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:25:57,001 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:26:07,021 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:26:17,089 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:26:27,111 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:26:37,150 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:26:47,187 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:26:57,199 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:27:07,214 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:27:17,232 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:27:27,251 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:27:37,264 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:27:47,288 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:27:57,307 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:28:07,321 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:28:17,400 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:28:27,414 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:28:37,468 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:28:47,505 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:28:57,517 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:29:07,576 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:29:17,601 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:29:27,749 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:29:40,139 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:29:50,174 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:30:00,185 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:30:10,201 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:30:20,216 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:30:30,518 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:30:40,535 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:30:50,540 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:31:00,556 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:31:10,562 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:31:20,583 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:31:30,598 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:31:40,616 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:31:50,623 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:32:00,732 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:32:10,762 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:32:20,779 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:32:30,783 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:32:40,875 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:32:50,904 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:33:00,941 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:33:10,980 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:33:21,021 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:33:31,045 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:33:41,078 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:33:51,099 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:34:01,155 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:34:11,162 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:34:21,224 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:34:31,232 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:34:41,250 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:34:51,264 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:35:01,276 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:35:11,289 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:35:21,304 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:35:31,316 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:35:41,331 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:35:51,351 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:36:01,361 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:36:11,459 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:36:21,542 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:36:31,685 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:36:39,720 - root - INFO - Received signal 2, shutting down...
2025-06-27 21:36:41,718 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:36:49,746 - root - INFO - Network watchdog stopped
2025-06-27 21:36:49,746 - root - INFO - Network watchdog stopped
2025-06-27 21:36:49,746 - root - INFO - Background service stopped
2025-06-27 21:36:49,850 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
