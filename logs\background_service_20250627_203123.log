2025-06-27 20:31:23,101 - root - INFO - Loaded environment variables from .env file
2025-06-27 20:31:24,060 - root - INFO - Trade logger initialized with log directory: logs/trades
2025-06-27 20:31:25,967 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 20:31:25,979 - root - INFO - Configuration loaded successfully.
2025-06-27 20:31:25,984 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 20:31:25,994 - root - INFO - Configuration loaded successfully.
2025-06-27 20:31:25,994 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 20:31:26,004 - root - INFO - Configuration loaded successfully.
2025-06-27 20:31:26,004 - root - INFO - Loading notification configuration from config/notifications_bitvavo.json...
2025-06-27 20:31:26,016 - root - INFO - Notification configuration loaded successfully.
2025-06-27 20:31:27,166 - root - INFO - Telegram command handlers registered
2025-06-27 20:31:27,177 - root - INFO - Telegram bot polling started
2025-06-27 20:31:27,177 - root - INFO - Telegram notifier initialized with notification level: standard
2025-06-27 20:31:27,177 - root - INFO - Telegram notification channel initialized
2025-06-27 20:31:27,195 - root - INFO - Successfully loaded templates using utf-8 encoding
2025-06-27 20:31:27,195 - root - INFO - Loaded 24 templates from file
2025-06-27 20:31:27,195 - root - INFO - Notification manager initialized with 1 channels
2025-06-27 20:31:27,195 - root - INFO - Notification manager initialized
2025-06-27 20:31:27,195 - root - INFO - Added critical time window: 23:55 for 10 minutes - Before daily candle close (1d)
2025-06-27 20:31:27,195 - root - INFO - Added critical time window: 00:00 for 10 minutes - After daily candle close (1d)
2025-06-27 20:31:27,195 - root - INFO - Set up critical time windows for 1d timeframe
2025-06-27 20:31:27,195 - root - INFO - Network watchdog initialized with 10s check interval
2025-06-27 20:31:27,219 - root - INFO - Loaded recovery state from data/state\recovery_state.json
2025-06-27 20:31:27,219 - root - INFO - No state file found at C:\Users\<USER>\OneDrive\Stalinis kompiuteris\Asset_Screener_Python\data\state\data/state\background_service_20250626_114325.json.json
2025-06-27 20:31:27,219 - root - INFO - Recovery manager initialized
2025-06-27 20:31:27,219 - root - INFO - [DEBUG] TRADING EXECUTOR - Initializing with exchange: bitvavo
2025-06-27 20:31:27,219 - root - INFO - [DEBUG] TRADING EXECUTOR - Trading config: {'enabled': True, 'exchange': 'bitvavo', 'initial_capital': 100, 'max_slippage_pct': 0.5, 'min_order_values': {'default': 5.0}, 'mode': 'paper', 'order_type': 'market', 'position_size_pct': 10, 'risk_management': {'max_daily_trades': 5, 'max_open_positions': 10}, 'transaction_fee_rate': 0.0025}
2025-06-27 20:31:27,219 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 20:31:27,225 - root - INFO - Configuration loaded successfully.
2025-06-27 20:31:27,232 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-27 20:31:27,232 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-27 20:31:27,232 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-27 20:31:27,232 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-27 20:31:27,232 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-27 20:31:27,232 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-27 20:31:27,234 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-27 20:31:27,234 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-27 20:31:27,234 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 20:31:27,244 - root - INFO - Configuration loaded successfully.
2025-06-27 20:31:27,299 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getMe "HTTP/1.1 200 OK"
2025-06-27 20:31:27,311 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/deleteWebhook "HTTP/1.1 200 OK"
2025-06-27 20:31:27,311 - telegram.ext.Application - INFO - Application started
2025-06-27 20:31:27,569 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-27 20:31:27,569 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-27 20:31:27,569 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-27 20:31:27,569 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-27 20:31:27,569 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-27 20:31:27,569 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 20:31:27,586 - root - INFO - Configuration loaded successfully.
2025-06-27 20:31:27,594 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 20:31:27,601 - root - INFO - Configuration loaded successfully.
2025-06-27 20:31:27,601 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-27 20:31:27,609 - root - INFO - Configuration loaded successfully.
2025-06-27 20:31:27,628 - root - INFO - Loaded paper trading history from paper_trading_history.json
2025-06-27 20:31:27,628 - root - INFO - Paper trading initialized with EUR as quote currency
2025-06-27 20:31:27,628 - root - INFO - Trading executor initialized for bitvavo
2025-06-27 20:31:27,628 - root - INFO - Trading mode: paper
2025-06-27 20:31:27,628 - root - INFO - Trading enabled: True
2025-06-27 20:31:27,628 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-27 20:31:27,628 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-27 20:31:27,628 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-27 20:31:27,628 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-27 20:31:27,628 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 20:31:27,639 - root - INFO - Configuration loaded successfully.
2025-06-27 20:31:27,999 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-27 20:31:27,999 - root - INFO - Trading enabled in paper mode
2025-06-27 20:31:27,999 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-27 20:31:27,999 - root - INFO - Reset paper trading account to initial balance: 100 EUR
2025-06-27 20:31:27,999 - root - INFO - Reset paper trading account to initial balance
2025-06-27 20:31:27,999 - root - INFO - Generated run ID: ********_203127
2025-06-27 20:31:27,999 - root - INFO - Ensured metrics directory exists: Performance_Metrics
2025-06-27 20:31:27,999 - root - INFO - Background service initialized
2025-06-27 20:31:27,999 - root - INFO - Network watchdog started
2025-06-27 20:31:27,999 - root - INFO - Network watchdog started
2025-06-27 20:31:28,007 - root - INFO - Schedule set up for 1d timeframe
2025-06-27 20:31:28,007 - root - INFO - Background service started
2025-06-27 20:31:28,007 - root - INFO - Executing strategy (run #1)...
2025-06-27 20:31:28,016 - root - INFO - Resetting daily trade counters for this strategy execution
2025-06-27 20:31:28,018 - root - INFO - No trades recorded today (Max: 5)
2025-06-27 20:31:28,018 - root - INFO - Initialized daily trades counter for 2025-06-27
2025-06-27 20:31:28,018 - root - INFO - Creating snapshot for candle timestamp: ********
2025-06-27 20:31:28,093 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-27 20:31:34,023 - root - WARNING - Failed to send with Markdown formatting: Timed out
2025-06-27 20:31:34,123 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-27 20:31:34,131 - root - INFO - Using trend method 'PGO For Loop' for strategy execution
2025-06-27 20:31:34,133 - root - INFO - Using configured start date for 1d timeframe: 2025-02-10
2025-06-27 20:31:34,133 - root - INFO - Using recent date for performance tracking: 2025-06-20
2025-06-27 20:31:34,136 - root - INFO - Using real-time data fetching - only fetching new data since last cached timestamp
2025-06-27 20:31:34,189 - root - INFO - Loaded 2138 rows of ETH/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:31:34,190 - root - INFO - Last timestamp in cache for ETH/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:31:34,190 - root - INFO - Expected last timestamp for ETH/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:31:34,191 - root - INFO - Data is up to date for ETH/USDT
2025-06-27 20:31:34,192 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:31:34,208 - root - INFO - Loaded 2138 rows of BTC/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:31:34,209 - root - INFO - Last timestamp in cache for BTC/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:31:34,209 - root - INFO - Expected last timestamp for BTC/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:31:34,210 - root - INFO - Data is up to date for BTC/USDT
2025-06-27 20:31:34,210 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:31:34,225 - root - INFO - Loaded 1781 rows of SOL/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:31:34,226 - root - INFO - Last timestamp in cache for SOL/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:31:34,226 - root - INFO - Expected last timestamp for SOL/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:31:34,226 - root - INFO - Data is up to date for SOL/USDT
2025-06-27 20:31:34,227 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:31:34,237 - root - INFO - Loaded 786 rows of SUI/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:31:34,238 - root - INFO - Last timestamp in cache for SUI/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:31:34,238 - root - INFO - Expected last timestamp for SUI/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:31:34,239 - root - INFO - Data is up to date for SUI/USDT
2025-06-27 20:31:34,241 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:31:34,259 - root - INFO - Loaded 2138 rows of XRP/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:31:34,261 - root - INFO - Last timestamp in cache for XRP/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:31:34,262 - root - INFO - Expected last timestamp for XRP/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:31:34,262 - root - INFO - Data is up to date for XRP/USDT
2025-06-27 20:31:34,264 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:31:34,283 - root - INFO - Loaded 1716 rows of AAVE/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:31:34,283 - root - INFO - Last timestamp in cache for AAVE/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:31:34,284 - root - INFO - Expected last timestamp for AAVE/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:31:34,284 - root - INFO - Data is up to date for AAVE/USDT
2025-06-27 20:31:34,285 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:31:34,301 - root - INFO - Loaded 1739 rows of AVAX/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:31:34,301 - root - INFO - Last timestamp in cache for AVAX/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:31:34,302 - root - INFO - Expected last timestamp for AVAX/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:31:34,302 - root - INFO - Data is up to date for AVAX/USDT
2025-06-27 20:31:34,304 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:31:34,325 - root - INFO - Loaded 2138 rows of ADA/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:31:34,326 - root - INFO - Last timestamp in cache for ADA/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:31:34,326 - root - INFO - Expected last timestamp for ADA/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:31:34,326 - root - INFO - Data is up to date for ADA/USDT
2025-06-27 20:31:34,327 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:31:34,345 - root - INFO - Loaded 2138 rows of LINK/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:31:34,346 - root - INFO - Last timestamp in cache for LINK/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:31:34,346 - root - INFO - Expected last timestamp for LINK/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:31:34,346 - root - INFO - Data is up to date for LINK/USDT
2025-06-27 20:31:34,346 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:31:34,366 - root - INFO - Loaded 2138 rows of TRX/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:31:34,368 - root - INFO - Last timestamp in cache for TRX/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:31:34,369 - root - INFO - Expected last timestamp for TRX/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:31:34,369 - root - INFO - Data is up to date for TRX/USDT
2025-06-27 20:31:34,371 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:31:34,381 - root - INFO - Loaded 784 rows of PEPE/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:31:34,381 - root - INFO - Last timestamp in cache for PEPE/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:31:34,382 - root - INFO - Expected last timestamp for PEPE/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:31:34,383 - root - INFO - Data is up to date for PEPE/USDT
2025-06-27 20:31:34,383 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:31:34,412 - root - INFO - Loaded 2138 rows of DOGE/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:31:34,412 - root - INFO - Last timestamp in cache for DOGE/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:31:34,413 - root - INFO - Expected last timestamp for DOGE/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:31:34,413 - root - INFO - Data is up to date for DOGE/USDT
2025-06-27 20:31:34,414 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:31:34,428 - root - INFO - Loaded 2138 rows of BNB/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:31:34,429 - root - INFO - Last timestamp in cache for BNB/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:31:34,429 - root - INFO - Expected last timestamp for BNB/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:31:34,429 - root - INFO - Data is up to date for BNB/USDT
2025-06-27 20:31:34,430 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:31:34,445 - root - INFO - Loaded 1774 rows of DOT/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:31:34,446 - root - INFO - Last timestamp in cache for DOT/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:31:34,446 - root - INFO - Expected last timestamp for DOT/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 20:31:34,447 - root - INFO - Data is up to date for DOT/USDT
2025-06-27 20:31:34,447 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:31:34,451 - root - INFO - Using 14 trend assets (USDT) for analysis and 14 trading assets (EUR) for execution
2025-06-27 20:31:34,452 - root - INFO - MTPI Multi-Indicator Configuration:
2025-06-27 20:31:34,452 - root - INFO -   - Number of indicators: 8
2025-06-27 20:31:34,452 - root - INFO -   - Enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-27 20:31:34,453 - root - INFO -   - Combination method: consensus
2025-06-27 20:31:34,453 - root - INFO -   - Long threshold: 0.1
2025-06-27 20:31:34,453 - root - INFO -   - Short threshold: -0.1
2025-06-27 20:31:34,453 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trend_assets order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-27 20:31:34,455 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trading_assets order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-27 20:31:34,455 - root - ERROR - [DEBUG] BACKGROUND SERVICE - BTC position in trend_assets: 2
2025-06-27 20:31:34,455 - root - ERROR - [DEBUG] BACKGROUND SERVICE - TRX position in trend_assets: 10
2025-06-27 20:31:34,455 - root - INFO - === RUNNING STRATEGY FOR WEB USING TEST_ALLOCATION ===
2025-06-27 20:31:34,456 - root - INFO - Parameters: use_mtpi_signal=False, mtpi_indicator_type=PGO
2025-06-27 20:31:34,456 - root - INFO - mtpi_timeframe=1d, mtpi_pgo_length=35
2025-06-27 20:31:34,456 - root - INFO - mtpi_upper_threshold=1.1, mtpi_lower_threshold=-0.58
2025-06-27 20:31:34,457 - root - INFO - timeframe=1d, analysis_start_date='2025-02-10'
2025-06-27 20:31:34,457 - root - INFO - n_assets=1, use_weighted_allocation=False
2025-06-27 20:31:34,457 - root - INFO - Using provided trend method: PGO For Loop
2025-06-27 20:31:34,457 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 20:31:34,471 - root - INFO - Configuration loaded successfully.
2025-06-27 20:31:34,472 - root - INFO - Saving configuration to config/settings_bitvavo_eur.yaml...
2025-06-27 20:31:34,481 - root - INFO - Configuration saved successfully.
2025-06-27 20:31:34,481 - root - INFO - Trend detection assets (USDT): ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-27 20:31:34,481 - root - INFO - Number of trend detection assets: 14
2025-06-27 20:31:34,481 - root - INFO - Selected assets type: <class 'list'>
2025-06-27 20:31:34,481 - root - INFO - Trading execution assets (EUR): ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-27 20:31:34,482 - root - INFO - Number of trading assets: 14
2025-06-27 20:31:34,482 - root - INFO - Trading assets type: <class 'list'>
2025-06-27 20:31:34,954 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-27 20:31:34,956 - root - INFO - Configuration loaded successfully.
2025-06-27 20:31:34,967 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 20:31:34,978 - root - INFO - Configuration loaded successfully.
2025-06-27 20:31:34,978 - root - INFO - Execution context: backtesting
2025-06-27 20:31:34,979 - root - INFO - Execution timing: candle_close
2025-06-27 20:31:34,979 - root - INFO - Ratio calculation method: independent
2025-06-27 20:31:34,979 - root - INFO - Tie-breaking strategy: imcumbent
2025-06-27 20:31:34,979 - root - INFO - Asset trend PGO parameters: length=None, upper_threshold=None, lower_threshold=None
2025-06-27 20:31:34,979 - root - INFO - MTPI indicators override: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-27 20:31:34,979 - root - INFO - MTPI combination method override: consensus
2025-06-27 20:31:34,979 - root - INFO - MTPI long threshold override: 0.1
2025-06-27 20:31:34,980 - root - INFO - MTPI short threshold override: -0.1
2025-06-27 20:31:34,980 - root - INFO - Using standard warmup period of 60 days for equal allocation
2025-06-27 20:31:34,980 - root - INFO - Fetching data from 2024-12-12 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-27 20:31:34,982 - root - INFO - Loaded metadata for 48 assets
2025-06-27 20:31:34,982 - root - INFO - Loaded metadata for 48 assets
2025-06-27 20:31:34,984 - root - INFO - Loaded metadata for 48 assets
2025-06-27 20:31:34,984 - root - INFO - Loaded metadata for 48 assets
2025-06-27 20:31:34,985 - root - INFO - Loaded metadata for 48 assets
2025-06-27 20:31:34,986 - root - INFO - Loaded metadata for 48 assets
2025-06-27 20:31:34,986 - root - INFO - Loaded metadata for 48 assets
2025-06-27 20:31:34,987 - root - INFO - Loaded metadata for 48 assets
2025-06-27 20:31:34,987 - root - INFO - Loaded metadata for 48 assets
2025-06-27 20:31:34,988 - root - INFO - Loaded metadata for 48 assets
2025-06-27 20:31:34,989 - root - INFO - Loaded metadata for 48 assets
2025-06-27 20:31:34,990 - root - INFO - Loaded metadata for 48 assets
2025-06-27 20:31:34,991 - root - INFO - Loaded metadata for 48 assets
2025-06-27 20:31:34,991 - root - INFO - Loaded metadata for 48 assets
2025-06-27 20:31:34,991 - root - INFO - Checking cache for 14 symbols (1d)...
2025-06-27 20:31:35,014 - root - INFO - Loaded 197 rows of ETH/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:31:35,016 - root - INFO - Metadata for ETH/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 20:31:35,017 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:31:35,017 - root - INFO - Loaded 197 rows of ETH/USDT data from cache (after filtering).
2025-06-27 20:31:35,042 - root - INFO - Loaded 197 rows of BTC/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:31:35,044 - root - INFO - Metadata for BTC/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 20:31:35,045 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:31:35,045 - root - INFO - Loaded 197 rows of BTC/USDT data from cache (after filtering).
2025-06-27 20:31:35,062 - root - INFO - Loaded 197 rows of SOL/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:31:35,063 - root - INFO - Metadata for SOL/USDT shows data starting from 2020-08-11, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 20:31:35,063 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:31:35,063 - root - INFO - Loaded 197 rows of SOL/USDT data from cache (after filtering).
2025-06-27 20:31:35,074 - root - INFO - Loaded 197 rows of SUI/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:31:35,077 - root - INFO - Metadata for SUI/USDT shows data starting from 2023-05-03, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 20:31:35,077 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:31:35,078 - root - INFO - Loaded 197 rows of SUI/USDT data from cache (after filtering).
2025-06-27 20:31:35,106 - root - INFO - Loaded 197 rows of XRP/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:31:35,108 - root - INFO - Metadata for XRP/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 20:31:35,109 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:31:35,109 - root - INFO - Loaded 197 rows of XRP/USDT data from cache (after filtering).
2025-06-27 20:31:35,133 - root - INFO - Loaded 197 rows of AAVE/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:31:35,135 - root - INFO - Metadata for AAVE/USDT shows data starting from 2020-10-15, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 20:31:35,135 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:31:35,136 - root - INFO - Loaded 197 rows of AAVE/USDT data from cache (after filtering).
2025-06-27 20:31:35,162 - root - INFO - Loaded 197 rows of AVAX/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:31:35,165 - root - INFO - Metadata for AVAX/USDT shows data starting from 2020-09-22, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 20:31:35,165 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:31:35,166 - root - INFO - Loaded 197 rows of AVAX/USDT data from cache (after filtering).
2025-06-27 20:31:35,194 - root - INFO - Loaded 197 rows of ADA/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:31:35,197 - root - INFO - Metadata for ADA/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 20:31:35,198 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:31:35,198 - root - INFO - Loaded 197 rows of ADA/USDT data from cache (after filtering).
2025-06-27 20:31:35,230 - root - INFO - Loaded 197 rows of LINK/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:31:35,232 - root - INFO - Metadata for LINK/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 20:31:35,232 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:31:35,233 - root - INFO - Loaded 197 rows of LINK/USDT data from cache (after filtering).
2025-06-27 20:31:35,259 - root - INFO - Loaded 197 rows of TRX/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:31:35,261 - root - INFO - Metadata for TRX/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 20:31:35,262 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:31:35,262 - root - INFO - Loaded 197 rows of TRX/USDT data from cache (after filtering).
2025-06-27 20:31:35,278 - root - INFO - Loaded 197 rows of PEPE/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:31:35,280 - root - INFO - Metadata for PEPE/USDT shows data starting from 2023-05-05, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 20:31:35,280 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:31:35,281 - root - INFO - Loaded 197 rows of PEPE/USDT data from cache (after filtering).
2025-06-27 20:31:35,306 - root - INFO - Loaded 197 rows of DOGE/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:31:35,308 - root - INFO - Metadata for DOGE/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 20:31:35,309 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:31:35,309 - root - INFO - Loaded 197 rows of DOGE/USDT data from cache (after filtering).
2025-06-27 20:31:35,335 - root - INFO - Loaded 197 rows of BNB/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:31:35,340 - root - INFO - Metadata for BNB/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 20:31:35,340 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:31:35,341 - root - INFO - Loaded 197 rows of BNB/USDT data from cache (after filtering).
2025-06-27 20:31:35,363 - root - INFO - Loaded 197 rows of DOT/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:31:35,365 - root - INFO - Metadata for DOT/USDT shows data starting from 2020-08-18, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 20:31:35,365 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:31:35,366 - root - INFO - Loaded 197 rows of DOT/USDT data from cache (after filtering).
2025-06-27 20:31:35,366 - root - INFO - All 14 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-27 20:31:35,367 - root - INFO - Asset ETH/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 20:31:35,367 - root - INFO - Asset BTC/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 20:31:35,368 - root - INFO - Asset SOL/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 20:31:35,368 - root - INFO - Asset SUI/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 20:31:35,369 - root - INFO - Asset XRP/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 20:31:35,369 - root - INFO - Asset AAVE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 20:31:35,370 - root - INFO - Asset AVAX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 20:31:35,370 - root - INFO - Asset ADA/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 20:31:35,371 - root - INFO - Asset LINK/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 20:31:35,372 - root - INFO - Asset TRX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 20:31:35,372 - root - INFO - Asset PEPE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 20:31:35,373 - root - INFO - Asset DOGE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 20:31:35,373 - root - INFO - Asset BNB/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 20:31:35,373 - root - INFO - Asset DOT/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 20:31:35,411 - root - INFO - Using standard MTPI warmup period of 120 days
2025-06-27 20:31:35,412 - root - INFO - Fetching MTPI signals from 2024-10-13 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-27 20:31:35,412 - root - INFO - Fetching MTPI signals using trend method: PGO For Loop
2025-06-27 20:31:35,413 - root - INFO - Using multi-indicator MTPI with config override: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-27 20:31:35,413 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-27 20:31:35,417 - root - INFO - Configuration loaded successfully.
2025-06-27 20:31:35,418 - root - INFO - Loaded MTPI multi-indicator configuration with 1 enabled indicators
2025-06-27 20:31:35,418 - root - INFO - Applying configuration overrides: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-27 20:31:35,418 - root - INFO - Override: enabled_indicators = ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-27 20:31:35,419 - root - INFO - Override: combination_method = consensus
2025-06-27 20:31:35,419 - root - INFO - Override: long_threshold = 0.1
2025-06-27 20:31:35,419 - root - INFO - Override: short_threshold = -0.1
2025-06-27 20:31:35,420 - root - INFO - Using MTPI configuration: indicators=['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], method=consensus, thresholds=(-0.1, 0.1)
2025-06-27 20:31:35,421 - root - INFO - Calculated appropriate limit for 1d timeframe: 500 candles (minimum 180.0 candles needed for 60 length indicator)
2025-06-27 20:31:35,421 - root - INFO - Using adjusted limit: 500 for max indicator length: 60
2025-06-27 20:31:35,421 - root - INFO - Checking cache for 1 symbols (1d)...
2025-06-27 20:31:35,445 - root - INFO - Loaded 257 rows of BTC/USDT data from cache (last updated: 2025-06-27)
2025-06-27 20:31:35,445 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 20:31:35,446 - root - INFO - Loaded 257 rows of BTC/USDT data from cache (after filtering).
2025-06-27 20:31:35,446 - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-27 20:31:35,446 - root - INFO - Fetched BTC data: 257 candles from 2024-10-13 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 20:31:35,446 - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.35, lower_threshold=-1.0
2025-06-27 20:31:35,489 - root - INFO - Generated PGO Score signals: {-1: 93, 0: 34, 1: 130}
2025-06-27 20:31:35,490 - root - INFO - Generated pgo signals: 257 values
2025-06-27 20:31:35,490 - root - INFO - Generating Bollinger Band signals with length=33, multiplier=2.0, long_threshold=76.0, short_threshold=31.0, use_heikin_ashi=True, heikin_src=close
2025-06-27 20:31:35,491 - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=76.0, short_threshold=31.0, use_heikin_ashi=True
2025-06-27 20:31:35,786 - root - INFO - Generated BB Score signals: {-1: 108, 0: 32, 1: 117}
2025-06-27 20:31:35,807 - root - INFO - Generated Bollinger Band signals: 257 values
2025-06-27 20:31:35,813 - root - INFO - Generated bollinger_bands signals: 257 values
2025-06-27 20:31:36,581 - root - INFO - Generated DWMA signals using Weighted SD method
2025-06-27 20:31:36,582 - root - INFO - Generated dwma_score signals: 257 values
2025-06-27 20:31:36,654 - root - INFO - Generated DEMA Supertrend signals
2025-06-27 20:31:36,654 - root - INFO - Signal distribution: {-1: 98, 0: 1, 1: 158}
2025-06-27 20:31:36,655 - root - INFO - Generated DEMA Super Score signals
2025-06-27 20:31:36,655 - root - INFO - Generated dema_super_score signals: 257 values
2025-06-27 20:31:36,778 - root - INFO - Generated DPSD signals
2025-06-27 20:31:36,779 - root - INFO - Signal distribution: {-1: 92, 0: 87, 1: 78}
2025-06-27 20:31:36,779 - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-06-27 20:31:36,779 - root - INFO - Generated dpsd_score signals: 257 values
2025-06-27 20:31:36,793 - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.2
2025-06-27 20:31:36,793 - root - INFO - Generated AAD Score signals using SMA method
2025-06-27 20:31:36,794 - root - INFO - Generated aad_score signals: 257 values
2025-06-27 20:31:36,890 - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-06-27 20:31:36,890 - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-06-27 20:31:36,890 - root - INFO - Generated dynamic_ema_score signals: 257 values
2025-06-27 20:31:37,028 - root - INFO - Generated quantile_dema_score signals: 257 values
2025-06-27 20:31:37,037 - root - INFO - Combined 8 signals using arithmetic mean aggregation
2025-06-27 20:31:37,038 - root - INFO - Signal distribution: {1: 153, -1: 103, 0: 1}
2025-06-27 20:31:37,038 - root - INFO - Generated combined MTPI signals: 257 values using consensus method
2025-06-27 20:31:37,039 - root - INFO - Signal distribution: {1: 153, -1: 103, 0: 1}
2025-06-27 20:31:37,039 - root - INFO - Calculating daily scores using trend method: PGO For Loop...
2025-06-27 20:31:37,041 - root - INFO - Saving configuration to config/settings.yaml...
2025-06-27 20:31:37,047 - root - INFO - Configuration saved successfully.
2025-06-27 20:31:37,048 - root - INFO - Updated config with trend method: PGO For Loop and PGO parameters
2025-06-27 20:31:37,048 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-27 20:31:37,063 - root - INFO - Configuration loaded successfully.
2025-06-27 20:31:37,063 - root - INFO - Using ratio-based approach with PGO (PGO For Loop)...
2025-06-27 20:31:37,063 - root - INFO - Calculating ratio PGO signals for 14 assets with PGO(35) using full OHLCV data
2025-06-27 20:31:37,064 - root - INFO - Using ratio calculation method: independent
2025-06-27 20:31:37,098 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:37,144 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BTC/USDT using full OHLCV data
2025-06-27 20:31:37,174 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 20:31:37,174 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:37,204 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 20:31:37,215 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SOL/USDT using full OHLCV data
2025-06-27 20:31:37,248 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-27 20:31:37,248 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:37,275 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-27 20:31:37,284 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SUI/USDT using full OHLCV data
2025-06-27 20:31:37,313 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 20:31:37,313 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:37,341 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 20:31:37,349 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/XRP/USDT using full OHLCV data
2025-06-27 20:31:37,379 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 20:31:37,379 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:37,409 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 20:31:37,417 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AAVE/USDT using full OHLCV data
2025-06-27 20:31:37,418 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:31:37,473 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-27 20:31:37,473 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:37,498 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-27 20:31:37,510 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AVAX/USDT using full OHLCV data
2025-06-27 20:31:37,535 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 20:31:37,536 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:37,564 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 20:31:37,575 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/ADA/USDT using full OHLCV data
2025-06-27 20:31:37,607 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-27 20:31:37,607 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:37,634 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-27 20:31:37,641 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/LINK/USDT using full OHLCV data
2025-06-27 20:31:37,668 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-27 20:31:37,668 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:37,691 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-27 20:31:37,699 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/TRX/USDT using full OHLCV data
2025-06-27 20:31:37,727 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 20:31:37,727 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:37,749 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 20:31:37,758 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/PEPE/USDT using full OHLCV data
2025-06-27 20:31:37,788 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 20:31:37,790 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:37,824 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 20:31:37,839 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOGE/USDT using full OHLCV data
2025-06-27 20:31:37,876 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:37,907 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BNB/USDT using full OHLCV data
2025-06-27 20:31:37,933 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-27 20:31:37,933 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:37,959 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-27 20:31:37,968 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOT/USDT using full OHLCV data
2025-06-27 20:31:38,002 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 20:31:38,003 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:38,032 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 20:31:38,043 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ETH/USDT using full OHLCV data
2025-06-27 20:31:38,076 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:38,115 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SOL/USDT using full OHLCV data
2025-06-27 20:31:38,137 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 20:31:38,137 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:38,150 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 20:31:38,169 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SUI/USDT using full OHLCV data
2025-06-27 20:31:38,191 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 20:31:38,191 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:38,221 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 20:31:38,231 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/XRP/USDT using full OHLCV data
2025-06-27 20:31:38,259 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 20:31:38,259 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:38,286 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 20:31:38,288 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AAVE/USDT using full OHLCV data
2025-06-27 20:31:38,320 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-27 20:31:38,320 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:38,338 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-27 20:31:38,352 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AVAX/USDT using full OHLCV data
2025-06-27 20:31:38,388 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 20:31:38,388 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:38,420 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 20:31:38,436 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ADA/USDT using full OHLCV data
2025-06-27 20:31:38,480 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 20:31:38,481 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:38,507 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 20:31:38,515 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/LINK/USDT using full OHLCV data
2025-06-27 20:31:38,546 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 20:31:38,547 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:38,577 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 20:31:38,587 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/TRX/USDT using full OHLCV data
2025-06-27 20:31:38,614 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 20:31:38,614 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:38,640 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 20:31:38,647 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/PEPE/USDT using full OHLCV data
2025-06-27 20:31:38,674 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:38,705 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOGE/USDT using full OHLCV data
2025-06-27 20:31:38,732 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:38,768 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/BNB/USDT using full OHLCV data
2025-06-27 20:31:38,810 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-27 20:31:38,811 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:38,842 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-27 20:31:38,853 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOT/USDT using full OHLCV data
2025-06-27 20:31:38,880 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-27 20:31:38,881 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:38,911 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-27 20:31:38,923 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ETH/USDT using full OHLCV data
2025-06-27 20:31:38,948 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:38,980 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BTC/USDT using full OHLCV data
2025-06-27 20:31:39,009 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 20:31:39,009 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:39,036 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 20:31:39,040 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/SUI/USDT using full OHLCV data
2025-06-27 20:31:39,076 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 20:31:39,076 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:39,104 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 20:31:39,112 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/XRP/USDT using full OHLCV data
2025-06-27 20:31:39,141 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-27 20:31:39,142 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:39,167 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-27 20:31:39,178 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AAVE/USDT using full OHLCV data
2025-06-27 20:31:39,210 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-27 20:31:39,210 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:39,240 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-27 20:31:39,247 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AVAX/USDT using full OHLCV data
2025-06-27 20:31:39,270 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 20:31:39,270 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:39,290 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 20:31:39,300 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ADA/USDT using full OHLCV data
2025-06-27 20:31:39,341 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 20:31:39,341 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:39,383 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 20:31:39,396 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/LINK/USDT using full OHLCV data
2025-06-27 20:31:39,444 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-27 20:31:39,444 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:39,471 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-27 20:31:39,475 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/TRX/USDT using full OHLCV data
2025-06-27 20:31:39,520 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-27 20:31:39,520 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:39,539 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-27 20:31:39,539 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/PEPE/USDT using full OHLCV data
2025-06-27 20:31:39,570 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 20:31:39,570 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:39,590 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 20:31:39,599 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOGE/USDT using full OHLCV data
2025-06-27 20:31:39,626 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 20:31:39,626 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:39,652 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 20:31:39,656 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BNB/USDT using full OHLCV data
2025-06-27 20:31:39,686 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 20:31:39,686 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:39,704 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 20:31:39,704 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOT/USDT using full OHLCV data
2025-06-27 20:31:39,736 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 20:31:39,736 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:39,753 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 20:31:39,761 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ETH/USDT using full OHLCV data
2025-06-27 20:31:39,785 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:39,815 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BTC/USDT using full OHLCV data
2025-06-27 20:31:39,837 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:39,870 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/SOL/USDT using full OHLCV data
2025-06-27 20:31:39,888 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:39,921 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/XRP/USDT using full OHLCV data
2025-06-27 20:31:39,946 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-27 20:31:39,946 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:39,985 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-27 20:31:39,990 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AAVE/USDT using full OHLCV data
2025-06-27 20:31:40,019 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:40,060 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AVAX/USDT using full OHLCV data
2025-06-27 20:31:40,089 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:40,125 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ADA/USDT using full OHLCV data
2025-06-27 20:31:40,152 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 20:31:40,152 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:40,180 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 20:31:40,185 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/LINK/USDT using full OHLCV data
2025-06-27 20:31:40,219 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:40,252 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/TRX/USDT using full OHLCV data
2025-06-27 20:31:40,278 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:40,308 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/PEPE/USDT using full OHLCV data
2025-06-27 20:31:40,336 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 20:31:40,336 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:40,355 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 20:31:40,375 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOGE/USDT using full OHLCV data
2025-06-27 20:31:40,403 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:40,439 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BNB/USDT using full OHLCV data
2025-06-27 20:31:40,468 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:40,508 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOT/USDT using full OHLCV data
2025-06-27 20:31:40,537 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-27 20:31:40,537 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:40,556 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-27 20:31:40,576 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ETH/USDT using full OHLCV data
2025-06-27 20:31:40,609 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:40,645 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BTC/USDT using full OHLCV data
2025-06-27 20:31:40,678 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:40,705 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SOL/USDT using full OHLCV data
2025-06-27 20:31:40,740 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 20:31:40,740 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:40,761 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 20:31:40,769 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SUI/USDT using full OHLCV data
2025-06-27 20:31:40,802 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 20:31:40,804 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:40,832 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 20:31:40,846 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AAVE/USDT using full OHLCV data
2025-06-27 20:31:40,891 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-27 20:31:40,893 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:40,937 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-27 20:31:40,956 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AVAX/USDT using full OHLCV data
2025-06-27 20:31:41,008 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-27 20:31:41,008 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:41,041 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-27 20:31:41,052 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ADA/USDT using full OHLCV data
2025-06-27 20:31:41,104 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 20:31:41,104 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:41,141 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 20:31:41,150 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/LINK/USDT using full OHLCV data
2025-06-27 20:31:41,185 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 20:31:41,192 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:41,215 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 20:31:41,221 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/TRX/USDT using full OHLCV data
2025-06-27 20:31:41,243 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 20:31:41,249 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:41,269 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 20:31:41,282 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/PEPE/USDT using full OHLCV data
2025-06-27 20:31:41,302 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:41,340 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOGE/USDT using full OHLCV data
2025-06-27 20:31:41,364 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:41,397 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BNB/USDT using full OHLCV data
2025-06-27 20:31:41,424 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-27 20:31:41,425 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:41,448 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-27 20:31:41,457 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOT/USDT using full OHLCV data
2025-06-27 20:31:41,483 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:41,523 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ETH/USDT using full OHLCV data
2025-06-27 20:31:41,555 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:41,597 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BTC/USDT using full OHLCV data
2025-06-27 20:31:41,629 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:41,680 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SOL/USDT using full OHLCV data
2025-06-27 20:31:41,720 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:41,753 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SUI/USDT using full OHLCV data
2025-06-27 20:31:41,774 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:41,801 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/XRP/USDT using full OHLCV data
2025-06-27 20:31:41,826 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 20:31:41,827 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:41,847 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 20:31:41,857 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/AVAX/USDT using full OHLCV data
2025-06-27 20:31:41,878 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 20:31:41,879 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:41,901 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 20:31:41,908 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ADA/USDT using full OHLCV data
2025-06-27 20:31:41,936 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 20:31:41,936 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:41,970 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 20:31:41,982 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/LINK/USDT using full OHLCV data
2025-06-27 20:31:42,021 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:42,059 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/TRX/USDT using full OHLCV data
2025-06-27 20:31:42,081 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:42,107 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/PEPE/USDT using full OHLCV data
2025-06-27 20:31:42,126 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-27 20:31:42,126 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:42,148 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-27 20:31:42,159 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOGE/USDT using full OHLCV data
2025-06-27 20:31:42,182 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:42,211 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BNB/USDT using full OHLCV data
2025-06-27 20:31:42,232 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 20:31:42,232 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:42,253 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 20:31:42,258 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOT/USDT using full OHLCV data
2025-06-27 20:31:42,287 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:42,315 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ETH/USDT using full OHLCV data
2025-06-27 20:31:42,339 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:42,367 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BTC/USDT using full OHLCV data
2025-06-27 20:31:42,390 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:42,416 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SOL/USDT using full OHLCV data
2025-06-27 20:31:42,440 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:42,467 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SUI/USDT using full OHLCV data
2025-06-27 20:31:42,488 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:42,518 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/XRP/USDT using full OHLCV data
2025-06-27 20:31:42,537 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-27 20:31:42,537 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:42,557 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-27 20:31:42,567 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/AAVE/USDT using full OHLCV data
2025-06-27 20:31:42,595 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:42,625 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ADA/USDT using full OHLCV data
2025-06-27 20:31:42,646 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-27 20:31:42,646 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:42,668 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-27 20:31:42,676 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/LINK/USDT using full OHLCV data
2025-06-27 20:31:42,704 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:42,735 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/TRX/USDT using full OHLCV data
2025-06-27 20:31:42,756 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:42,785 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/PEPE/USDT using full OHLCV data
2025-06-27 20:31:42,805 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:42,835 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOGE/USDT using full OHLCV data
2025-06-27 20:31:42,855 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:42,885 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BNB/USDT using full OHLCV data
2025-06-27 20:31:42,904 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:42,934 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOT/USDT using full OHLCV data
2025-06-27 20:31:42,953 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 20:31:42,953 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:42,973 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 20:31:42,984 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/ETH/USDT using full OHLCV data
2025-06-27 20:31:43,010 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:43,035 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BTC/USDT using full OHLCV data
2025-06-27 20:31:43,062 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:43,089 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SOL/USDT using full OHLCV data
2025-06-27 20:31:43,109 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-27 20:31:43,109 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:43,127 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-27 20:31:43,138 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SUI/USDT using full OHLCV data
2025-06-27 20:31:43,160 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:43,187 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/XRP/USDT using full OHLCV data
2025-06-27 20:31:43,221 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-27 20:31:43,221 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:43,257 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-27 20:31:43,265 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AAVE/USDT using full OHLCV data
2025-06-27 20:31:43,295 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 20:31:43,296 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:43,314 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 20:31:43,323 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AVAX/USDT using full OHLCV data
2025-06-27 20:31:43,345 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-27 20:31:43,345 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:43,364 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-27 20:31:43,374 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/LINK/USDT using full OHLCV data
2025-06-27 20:31:43,394 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:43,449 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/TRX/USDT using full OHLCV data
2025-06-27 20:31:43,525 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 20:31:43,525 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:43,599 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 20:31:43,619 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/PEPE/USDT using full OHLCV data
2025-06-27 20:31:43,690 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 20:31:43,690 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:43,766 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 20:31:43,786 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOGE/USDT using full OHLCV data
2025-06-27 20:31:43,855 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:43,943 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BNB/USDT using full OHLCV data
2025-06-27 20:31:44,016 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 20:31:44,016 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:44,089 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 20:31:44,117 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOT/USDT using full OHLCV data
2025-06-27 20:31:44,197 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:44,281 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ETH/USDT using full OHLCV data
2025-06-27 20:31:44,339 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:44,423 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BTC/USDT using full OHLCV data
2025-06-27 20:31:44,486 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:44,570 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SOL/USDT using full OHLCV data
2025-06-27 20:31:44,642 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:44,733 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SUI/USDT using full OHLCV data
2025-06-27 20:31:44,800 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:44,885 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/XRP/USDT using full OHLCV data
2025-06-27 20:31:44,959 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-27 20:31:44,959 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:45,032 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-27 20:31:45,050 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AAVE/USDT using full OHLCV data
2025-06-27 20:31:45,130 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:45,213 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AVAX/USDT using full OHLCV data
2025-06-27 20:31:45,283 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:45,366 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ADA/USDT using full OHLCV data
2025-06-27 20:31:45,440 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:45,527 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/TRX/USDT using full OHLCV data
2025-06-27 20:31:45,610 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 20:31:45,610 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:45,682 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 20:31:45,716 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/PEPE/USDT using full OHLCV data
2025-06-27 20:31:45,800 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:45,917 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOGE/USDT using full OHLCV data
2025-06-27 20:31:45,999 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:46,091 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BNB/USDT using full OHLCV data
2025-06-27 20:31:46,160 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:46,251 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOT/USDT using full OHLCV data
2025-06-27 20:31:46,327 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:46,435 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ETH/USDT using full OHLCV data
2025-06-27 20:31:46,511 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:46,601 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BTC/USDT using full OHLCV data
2025-06-27 20:31:46,672 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 20:31:46,672 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:46,741 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 20:31:46,764 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SOL/USDT using full OHLCV data
2025-06-27 20:31:46,842 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:46,927 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SUI/USDT using full OHLCV data
2025-06-27 20:31:46,993 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 20:31:47,000 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:47,066 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 20:31:47,091 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/XRP/USDT using full OHLCV data
2025-06-27 20:31:47,166 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 20:31:47,166 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:47,238 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 20:31:47,263 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AAVE/USDT using full OHLCV data
2025-06-27 20:31:47,349 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 20:31:47,350 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:47,433 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 20:31:47,453 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AVAX/USDT using full OHLCV data
2025-06-27 20:31:47,478 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:31:47,564 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:47,671 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ADA/USDT using full OHLCV data
2025-06-27 20:31:47,738 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 20:31:47,749 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:47,805 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 20:31:47,834 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/LINK/USDT using full OHLCV data
2025-06-27 20:31:47,917 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 20:31:47,922 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:47,983 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 20:31:48,009 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/PEPE/USDT using full OHLCV data
2025-06-27 20:31:48,092 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:48,180 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOGE/USDT using full OHLCV data
2025-06-27 20:31:48,250 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:48,353 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BNB/USDT using full OHLCV data
2025-06-27 20:31:48,432 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:48,523 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOT/USDT using full OHLCV data
2025-06-27 20:31:48,598 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:48,686 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ETH/USDT using full OHLCV data
2025-06-27 20:31:48,752 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:48,839 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BTC/USDT using full OHLCV data
2025-06-27 20:31:48,928 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:49,023 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SOL/USDT using full OHLCV data
2025-06-27 20:31:49,102 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:49,189 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SUI/USDT using full OHLCV data
2025-06-27 20:31:49,266 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:49,359 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/XRP/USDT using full OHLCV data
2025-06-27 20:31:49,439 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-27 20:31:49,439 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:49,515 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-27 20:31:49,560 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AAVE/USDT using full OHLCV data
2025-06-27 20:31:49,649 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 20:31:49,649 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:49,732 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 20:31:49,768 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AVAX/USDT using full OHLCV data
2025-06-27 20:31:49,855 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 20:31:49,856 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:49,940 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 20:31:49,968 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ADA/USDT using full OHLCV data
2025-06-27 20:31:50,060 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-27 20:31:50,060 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:50,137 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-27 20:31:50,156 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/LINK/USDT using full OHLCV data
2025-06-27 20:31:50,244 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:50,366 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/TRX/USDT using full OHLCV data
2025-06-27 20:31:50,452 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:50,559 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOGE/USDT using full OHLCV data
2025-06-27 20:31:50,641 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:50,722 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BNB/USDT using full OHLCV data
2025-06-27 20:31:50,804 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:50,893 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOT/USDT using full OHLCV data
2025-06-27 20:31:50,976 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:51,059 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ETH/USDT using full OHLCV data
2025-06-27 20:31:51,136 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:51,218 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BTC/USDT using full OHLCV data
2025-06-27 20:31:51,270 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:51,333 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SOL/USDT using full OHLCV data
2025-06-27 20:31:51,374 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 20:31:51,383 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:51,440 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 20:31:51,454 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SUI/USDT using full OHLCV data
2025-06-27 20:31:51,528 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 20:31:51,529 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:51,613 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 20:31:51,643 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/XRP/USDT using full OHLCV data
2025-06-27 20:31:51,738 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 20:31:51,741 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:51,841 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 20:31:51,860 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AAVE/USDT using full OHLCV data
2025-06-27 20:31:51,924 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-27 20:31:51,926 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:52,002 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-27 20:31:52,019 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AVAX/USDT using full OHLCV data
2025-06-27 20:31:52,139 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-27 20:31:52,139 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:52,235 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-27 20:31:52,265 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ADA/USDT using full OHLCV data
2025-06-27 20:31:52,356 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 20:31:52,356 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:52,445 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 20:31:52,467 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/LINK/USDT using full OHLCV data
2025-06-27 20:31:52,563 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-27 20:31:52,563 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:52,640 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-27 20:31:52,668 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/TRX/USDT using full OHLCV data
2025-06-27 20:31:52,759 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-27 20:31:52,760 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:52,847 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-27 20:31:52,867 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/PEPE/USDT using full OHLCV data
2025-06-27 20:31:52,961 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:53,076 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BNB/USDT using full OHLCV data
2025-06-27 20:31:53,168 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 20:31:53,168 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:53,254 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 20:31:53,287 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/DOT/USDT using full OHLCV data
2025-06-27 20:31:53,377 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:53,484 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ETH/USDT using full OHLCV data
2025-06-27 20:31:53,567 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:53,672 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/BTC/USDT using full OHLCV data
2025-06-27 20:31:53,771 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 20:31:53,773 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:53,849 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 20:31:53,874 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SOL/USDT using full OHLCV data
2025-06-27 20:31:53,959 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 20:31:53,961 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:54,029 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 20:31:54,051 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SUI/USDT using full OHLCV data
2025-06-27 20:31:54,142 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 20:31:54,142 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:54,225 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 20:31:54,246 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/XRP/USDT using full OHLCV data
2025-06-27 20:31:54,329 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-27 20:31:54,329 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:54,416 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-27 20:31:54,441 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AAVE/USDT using full OHLCV data
2025-06-27 20:31:54,521 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-27 20:31:54,521 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:54,576 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-27 20:31:54,585 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AVAX/USDT using full OHLCV data
2025-06-27 20:31:54,617 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 20:31:54,617 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:54,643 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 20:31:54,650 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ADA/USDT using full OHLCV data
2025-06-27 20:31:54,680 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-27 20:31:54,680 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:54,709 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-27 20:31:54,717 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/LINK/USDT using full OHLCV data
2025-06-27 20:31:54,758 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 20:31:54,758 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:54,783 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 20:31:54,793 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/TRX/USDT using full OHLCV data
2025-06-27 20:31:54,814 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 20:31:54,815 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:54,835 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 20:31:54,847 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/PEPE/USDT using full OHLCV data
2025-06-27 20:31:54,876 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:54,925 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOGE/USDT using full OHLCV data
2025-06-27 20:31:54,950 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:54,991 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOT/USDT using full OHLCV data
2025-06-27 20:31:55,015 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:55,048 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ETH/USDT using full OHLCV data
2025-06-27 20:31:55,074 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:55,106 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BTC/USDT using full OHLCV data
2025-06-27 20:31:55,156 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:55,212 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SOL/USDT using full OHLCV data
2025-06-27 20:31:55,236 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 20:31:55,236 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:55,256 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 20:31:55,266 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SUI/USDT using full OHLCV data
2025-06-27 20:31:55,303 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 20:31:55,304 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:55,326 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 20:31:55,334 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/XRP/USDT using full OHLCV data
2025-06-27 20:31:55,359 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 20:31:55,360 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:55,384 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 20:31:55,391 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AAVE/USDT using full OHLCV data
2025-06-27 20:31:55,433 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-27 20:31:55,434 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:55,471 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-27 20:31:55,480 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AVAX/USDT using full OHLCV data
2025-06-27 20:31:55,505 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-27 20:31:55,506 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:55,528 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-27 20:31:55,537 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ADA/USDT using full OHLCV data
2025-06-27 20:31:55,569 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-27 20:31:55,569 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:55,610 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-27 20:31:55,623 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/LINK/USDT using full OHLCV data
2025-06-27 20:31:55,669 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:55,710 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/TRX/USDT using full OHLCV data
2025-06-27 20:31:55,747 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 20:31:55,748 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:55,774 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 20:31:55,781 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/PEPE/USDT using full OHLCV data
2025-06-27 20:31:55,809 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:55,836 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/DOGE/USDT using full OHLCV data
2025-06-27 20:31:55,853 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 20:31:55,886 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BNB/USDT using full OHLCV data
2025-06-27 20:31:57,614 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:31:58,356 - root - INFO - Successfully calculated daily scores using ratio-based comparisons.
2025-06-27 20:31:58,356 - root - INFO - Finished calculating daily scores. DataFrame shape: (197, 14)
2025-06-27 20:31:58,356 - root - WARNING - Running strategy with n_assets=1, equal allocation, MTPI filtering DISABLED
2025-06-27 20:31:58,365 - root - INFO - Date ranges for each asset:
2025-06-27 20:31:58,365 - root - INFO -   ETH/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 20:31:58,365 - root - INFO -   BTC/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 20:31:58,365 - root - INFO -   SOL/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 20:31:58,365 - root - INFO -   SUI/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 20:31:58,365 - root - INFO -   XRP/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 20:31:58,365 - root - INFO -   AAVE/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 20:31:58,365 - root - INFO -   AVAX/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 20:31:58,365 - root - INFO -   ADA/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 20:31:58,365 - root - INFO -   LINK/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 20:31:58,372 - root - INFO -   TRX/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 20:31:58,372 - root - INFO -   PEPE/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 20:31:58,372 - root - INFO -   DOGE/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 20:31:58,372 - root - INFO -   BNB/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 20:31:58,372 - root - INFO -   DOT/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 20:31:58,372 - root - INFO - Common dates range: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 20:31:58,372 - root - INFO - Analysis will run from: 2025-02-10 to 2025-06-26 (137 candles)
2025-06-27 20:31:58,372 - root - INFO - EXECUTION TIMING VERIFICATION:
2025-06-27 20:31:58,372 - root - INFO -    Execution Method: candle_close
2025-06-27 20:31:58,372 - root - INFO -    Automatic execution at 00:00 UTC (candle close)
2025-06-27 20:31:58,372 - root - INFO -    Signal generated and executed immediately
2025-06-27 20:31:58,506 - root - INFO - Including ETH/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,506 - root - INFO - Including BTC/USDT on 2025-02-10 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,506 - root - INFO - Including SOL/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,506 - root - INFO - Including SUI/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,506 - root - INFO - Including XRP/USDT on 2025-02-10 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,506 - root - INFO - Including AAVE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,506 - root - INFO - Including AVAX/USDT on 2025-02-10 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,506 - root - INFO - Including ADA/USDT on 2025-02-10 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,506 - root - INFO - Including LINK/USDT on 2025-02-10 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,506 - root - INFO - Including TRX/USDT on 2025-02-10 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,506 - root - INFO - Including PEPE/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,506 - root - INFO - Including DOGE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,506 - root - INFO - Including BNB/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,506 - root - INFO - Including DOT/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,512 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 20:31:58,512 - root - INFO - ASSET CHANGE DETECTED on 2025-02-11:
2025-06-27 20:31:58,512 - root - INFO -    Signal Date: 2025-02-10 (generated at 00:00 UTC)
2025-06-27 20:31:58,512 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-11 00:00 UTC (immediate)
2025-06-27 20:31:58,512 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:31:58,512 - root - INFO -    Buying: ['TRX/USDT']
2025-06-27 20:31:58,512 - root - INFO -    TRX/USDT buy price: $0.2410 (close price)
2025-06-27 20:31:58,520 - root - INFO - Including ETH/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,520 - root - INFO - Including BTC/USDT on 2025-02-11 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,520 - root - INFO - Including SOL/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,520 - root - INFO - Including SUI/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,520 - root - INFO - Including XRP/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,520 - root - INFO - Including AAVE/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,520 - root - INFO - Including AVAX/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,520 - root - INFO - Including ADA/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,520 - root - INFO - Including LINK/USDT on 2025-02-11 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,520 - root - INFO - Including TRX/USDT on 2025-02-11 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,520 - root - INFO - Including PEPE/USDT on 2025-02-11 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,520 - root - INFO - Including DOGE/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,520 - root - INFO - Including BNB/USDT on 2025-02-11 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,520 - root - INFO - Including DOT/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,529 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,529 - root - INFO - Including ETH/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,529 - root - INFO - Including BTC/USDT on 2025-02-12 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,529 - root - INFO - Including SOL/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,537 - root - INFO - Including SUI/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,537 - root - INFO - Including XRP/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,537 - root - INFO - Including AAVE/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,537 - root - INFO - Including AVAX/USDT on 2025-02-12 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,537 - root - INFO - Including ADA/USDT on 2025-02-12 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,537 - root - INFO - Including LINK/USDT on 2025-02-12 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,537 - root - INFO - Including TRX/USDT on 2025-02-12 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,537 - root - INFO - Including PEPE/USDT on 2025-02-12 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,537 - root - INFO - Including DOGE/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,537 - root - INFO - Including BNB/USDT on 2025-02-12 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,537 - root - INFO - Including DOT/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,537 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,537 - root - INFO - ASSET CHANGE DETECTED on 2025-02-13:
2025-06-27 20:31:58,537 - root - INFO -    Signal Date: 2025-02-12 (generated at 00:00 UTC)
2025-06-27 20:31:58,537 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-13 00:00 UTC (immediate)
2025-06-27 20:31:58,537 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:31:58,537 - root - INFO -    Selling: ['TRX/USDT']
2025-06-27 20:31:58,537 - root - INFO -    Buying: ['BNB/USDT']
2025-06-27 20:31:58,537 - root - INFO -    BNB/USDT buy price: $664.7200 (close price)
2025-06-27 20:31:58,545 - root - INFO - Including ETH/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,545 - root - INFO - Including BTC/USDT on 2025-02-13 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,545 - root - INFO - Including SOL/USDT on 2025-02-13 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,545 - root - INFO - Including SUI/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,545 - root - INFO - Including XRP/USDT on 2025-02-13 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,545 - root - INFO - Including AAVE/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,545 - root - INFO - Including AVAX/USDT on 2025-02-13 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,545 - root - INFO - Including ADA/USDT on 2025-02-13 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,545 - root - INFO - Including LINK/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,545 - root - INFO - Including TRX/USDT on 2025-02-13 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,545 - root - INFO - Including PEPE/USDT on 2025-02-13 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,545 - root - INFO - Including DOGE/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,545 - root - INFO - Including BNB/USDT on 2025-02-13 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,545 - root - INFO - Including DOT/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,550 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,553 - root - INFO - Including ETH/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,553 - root - INFO - Including BTC/USDT on 2025-02-14 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,553 - root - INFO - Including SOL/USDT on 2025-02-14 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,553 - root - INFO - Including SUI/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,553 - root - INFO - Including XRP/USDT on 2025-02-14 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,553 - root - INFO - Including AAVE/USDT on 2025-02-14 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,553 - root - INFO - Including AVAX/USDT on 2025-02-14 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,553 - root - INFO - Including ADA/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,553 - root - INFO - Including LINK/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,553 - root - INFO - Including TRX/USDT on 2025-02-14 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,553 - root - INFO - Including PEPE/USDT on 2025-02-14 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,553 - root - INFO - Including DOGE/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,553 - root - INFO - Including BNB/USDT on 2025-02-14 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,553 - root - INFO - Including DOT/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 20:31:58,561 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,567 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,569 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,577 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,585 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,585 - root - INFO - ASSET CHANGE DETECTED on 2025-02-19:
2025-06-27 20:31:58,585 - root - INFO -    Signal Date: 2025-02-18 (generated at 00:00 UTC)
2025-06-27 20:31:58,585 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-19 00:00 UTC (immediate)
2025-06-27 20:31:58,585 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:31:58,585 - root - INFO -    Selling: ['BNB/USDT']
2025-06-27 20:31:58,585 - root - INFO -    Buying: ['TRX/USDT']
2025-06-27 20:31:58,585 - root - INFO -    TRX/USDT buy price: $0.2424 (close price)
2025-06-27 20:31:58,594 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,602 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,610 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,618 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,618 - root - INFO - ASSET CHANGE DETECTED on 2025-02-23:
2025-06-27 20:31:58,618 - root - INFO -    Signal Date: 2025-02-22 (generated at 00:00 UTC)
2025-06-27 20:31:58,618 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-23 00:00 UTC (immediate)
2025-06-27 20:31:58,618 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:31:58,618 - root - INFO -    Selling: ['TRX/USDT']
2025-06-27 20:31:58,618 - root - INFO -    Buying: ['BNB/USDT']
2025-06-27 20:31:58,618 - root - INFO -    BNB/USDT buy price: $658.4200 (close price)
2025-06-27 20:31:58,626 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,626 - root - INFO - ASSET CHANGE DETECTED on 2025-02-24:
2025-06-27 20:31:58,626 - root - INFO -    Signal Date: 2025-02-23 (generated at 00:00 UTC)
2025-06-27 20:31:58,626 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-24 00:00 UTC (immediate)
2025-06-27 20:31:58,626 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:31:58,626 - root - INFO -    Selling: ['BNB/USDT']
2025-06-27 20:31:58,626 - root - INFO -    Buying: ['TRX/USDT']
2025-06-27 20:31:58,626 - root - INFO -    TRX/USDT buy price: $0.2401 (close price)
2025-06-27 20:31:58,635 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,643 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,643 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,651 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,659 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,667 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,675 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,675 - root - INFO - ASSET CHANGE DETECTED on 2025-03-03:
2025-06-27 20:31:58,675 - root - INFO -    Signal Date: 2025-03-02 (generated at 00:00 UTC)
2025-06-27 20:31:58,675 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-03 00:00 UTC (immediate)
2025-06-27 20:31:58,675 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:31:58,675 - root - INFO -    Selling: ['TRX/USDT']
2025-06-27 20:31:58,675 - root - INFO -    Buying: ['ADA/USDT']
2025-06-27 20:31:58,675 - root - INFO -    ADA/USDT buy price: $0.8578 (close price)
2025-06-27 20:31:58,683 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,683 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,691 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,701 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,708 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,716 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,724 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,724 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,724 - root - INFO - ASSET CHANGE DETECTED on 2025-03-11:
2025-06-27 20:31:58,724 - root - INFO -    Signal Date: 2025-03-10 (generated at 00:00 UTC)
2025-06-27 20:31:58,724 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-11 00:00 UTC (immediate)
2025-06-27 20:31:58,724 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:31:58,724 - root - INFO -    Selling: ['ADA/USDT']
2025-06-27 20:31:58,724 - root - INFO -    Buying: ['TRX/USDT']
2025-06-27 20:31:58,724 - root - INFO -    TRX/USDT buy price: $0.2244 (close price)
2025-06-27 20:31:58,732 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,740 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,750 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,757 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,757 - root - INFO - ASSET CHANGE DETECTED on 2025-03-15:
2025-06-27 20:31:58,757 - root - INFO -    Signal Date: 2025-03-14 (generated at 00:00 UTC)
2025-06-27 20:31:58,757 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-15 00:00 UTC (immediate)
2025-06-27 20:31:58,757 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:31:58,757 - root - INFO -    Selling: ['TRX/USDT']
2025-06-27 20:31:58,757 - root - INFO -    Buying: ['ADA/USDT']
2025-06-27 20:31:58,757 - root - INFO -    ADA/USDT buy price: $0.7468 (close price)
2025-06-27 20:31:58,767 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,773 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,773 - root - INFO - ASSET CHANGE DETECTED on 2025-03-17:
2025-06-27 20:31:58,773 - root - INFO -    Signal Date: 2025-03-16 (generated at 00:00 UTC)
2025-06-27 20:31:58,773 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-17 00:00 UTC (immediate)
2025-06-27 20:31:58,773 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:31:58,773 - root - INFO -    Selling: ['ADA/USDT']
2025-06-27 20:31:58,773 - root - INFO -    Buying: ['BNB/USDT']
2025-06-27 20:31:58,773 - root - INFO -    BNB/USDT buy price: $631.6900 (close price)
2025-06-27 20:31:58,781 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,789 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,789 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,789 - root - INFO - ASSET CHANGE DETECTED on 2025-03-20:
2025-06-27 20:31:58,789 - root - INFO -    Signal Date: 2025-03-19 (generated at 00:00 UTC)
2025-06-27 20:31:58,789 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-20 00:00 UTC (immediate)
2025-06-27 20:31:58,789 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:31:58,789 - root - INFO -    Selling: ['BNB/USDT']
2025-06-27 20:31:58,789 - root - INFO -    Buying: ['XRP/USDT']
2025-06-27 20:31:58,789 - root - INFO -    XRP/USDT buy price: $2.4361 (close price)
2025-06-27 20:31:58,805 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,805 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,805 - root - INFO - ASSET CHANGE DETECTED on 2025-03-22:
2025-06-27 20:31:58,805 - root - INFO -    Signal Date: 2025-03-21 (generated at 00:00 UTC)
2025-06-27 20:31:58,805 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-22 00:00 UTC (immediate)
2025-06-27 20:31:58,805 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:31:58,805 - root - INFO -    Selling: ['XRP/USDT']
2025-06-27 20:31:58,805 - root - INFO -    Buying: ['BNB/USDT']
2025-06-27 20:31:58,813 - root - INFO -    BNB/USDT buy price: $627.0100 (close price)
2025-06-27 20:31:58,824 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,829 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,838 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,838 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,838 - root - INFO - ASSET CHANGE DETECTED on 2025-03-26:
2025-06-27 20:31:58,838 - root - INFO -    Signal Date: 2025-03-25 (generated at 00:00 UTC)
2025-06-27 20:31:58,838 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-26 00:00 UTC (immediate)
2025-06-27 20:31:58,838 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:31:58,838 - root - INFO -    Selling: ['BNB/USDT']
2025-06-27 20:31:58,846 - root - INFO -    Buying: ['AVAX/USDT']
2025-06-27 20:31:58,846 - root - INFO -    AVAX/USDT buy price: $22.0400 (close price)
2025-06-27 20:31:58,846 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,846 - root - INFO - ASSET CHANGE DETECTED on 2025-03-27:
2025-06-27 20:31:58,846 - root - INFO -    Signal Date: 2025-03-26 (generated at 00:00 UTC)
2025-06-27 20:31:58,854 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-27 00:00 UTC (immediate)
2025-06-27 20:31:58,854 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:31:58,854 - root - INFO -    Selling: ['AVAX/USDT']
2025-06-27 20:31:58,854 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-27 20:31:58,854 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-27 20:31:58,854 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,862 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,870 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,878 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,878 - root - INFO - ASSET CHANGE DETECTED on 2025-03-31:
2025-06-27 20:31:58,878 - root - INFO -    Signal Date: 2025-03-30 (generated at 00:00 UTC)
2025-06-27 20:31:58,878 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-31 00:00 UTC (immediate)
2025-06-27 20:31:58,878 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:31:58,878 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-27 20:31:58,878 - root - INFO -    Buying: ['BNB/USDT']
2025-06-27 20:31:58,878 - root - INFO -    BNB/USDT buy price: $604.8100 (close price)
2025-06-27 20:31:58,886 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,886 - root - INFO - ASSET CHANGE DETECTED on 2025-04-01:
2025-06-27 20:31:58,886 - root - INFO -    Signal Date: 2025-03-31 (generated at 00:00 UTC)
2025-06-27 20:31:58,886 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-01 00:00 UTC (immediate)
2025-06-27 20:31:58,886 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:31:58,886 - root - INFO -    Selling: ['BNB/USDT']
2025-06-27 20:31:58,886 - root - INFO -    Buying: ['TRX/USDT']
2025-06-27 20:31:58,886 - root - INFO -    TRX/USDT buy price: $0.2378 (close price)
2025-06-27 20:31:58,894 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,900 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,902 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,911 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,919 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,927 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,935 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,935 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,944 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,952 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,960 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,968 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,968 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,976 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,984 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:58,992 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,001 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,008 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,008 - root - INFO - ASSET CHANGE DETECTED on 2025-04-19:
2025-06-27 20:31:59,008 - root - INFO -    Signal Date: 2025-04-18 (generated at 00:00 UTC)
2025-06-27 20:31:59,008 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-19 00:00 UTC (immediate)
2025-06-27 20:31:59,008 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:31:59,008 - root - INFO -    Selling: ['TRX/USDT']
2025-06-27 20:31:59,008 - root - INFO -    Buying: ['SOL/USDT']
2025-06-27 20:31:59,008 - root - INFO -    SOL/USDT buy price: $139.8700 (close price)
2025-06-27 20:31:59,008 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,016 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,024 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,033 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,041 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,041 - root - INFO - ASSET CHANGE DETECTED on 2025-04-24:
2025-06-27 20:31:59,041 - root - INFO -    Signal Date: 2025-04-23 (generated at 00:00 UTC)
2025-06-27 20:31:59,041 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-24 00:00 UTC (immediate)
2025-06-27 20:31:59,041 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:31:59,041 - root - INFO -    Selling: ['SOL/USDT']
2025-06-27 20:31:59,041 - root - INFO -    Buying: ['SUI/USDT']
2025-06-27 20:31:59,041 - root - INFO -    SUI/USDT buy price: $3.3437 (close price)
2025-06-27 20:31:59,051 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,057 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,057 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,065 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,073 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,082 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,090 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,093 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,100 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,106 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,117 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,122 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,130 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,138 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,138 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,151 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,151 - root - INFO - ASSET CHANGE DETECTED on 2025-05-10:
2025-06-27 20:31:59,151 - root - INFO -    Signal Date: 2025-05-09 (generated at 00:00 UTC)
2025-06-27 20:31:59,151 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-10 00:00 UTC (immediate)
2025-06-27 20:31:59,151 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:31:59,155 - root - INFO -    Selling: ['SUI/USDT']
2025-06-27 20:31:59,155 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-27 20:31:59,155 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-27 20:31:59,155 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,166 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,171 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,183 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,187 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,195 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,196 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,203 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,212 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,220 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,228 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,228 - root - INFO - ASSET CHANGE DETECTED on 2025-05-21:
2025-06-27 20:31:59,228 - root - INFO -    Signal Date: 2025-05-20 (generated at 00:00 UTC)
2025-06-27 20:31:59,228 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-21 00:00 UTC (immediate)
2025-06-27 20:31:59,228 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:31:59,228 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-27 20:31:59,228 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-27 20:31:59,228 - root - INFO -    AAVE/USDT buy price: $247.5400 (close price)
2025-06-27 20:31:59,236 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,236 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,236 - root - INFO - ASSET CHANGE DETECTED on 2025-05-23:
2025-06-27 20:31:59,236 - root - INFO -    Signal Date: 2025-05-22 (generated at 00:00 UTC)
2025-06-27 20:31:59,236 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-23 00:00 UTC (immediate)
2025-06-27 20:31:59,236 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:31:59,236 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-27 20:31:59,236 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-27 20:31:59,236 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-27 20:31:59,250 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,252 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,252 - root - INFO - ASSET CHANGE DETECTED on 2025-05-25:
2025-06-27 20:31:59,252 - root - INFO -    Signal Date: 2025-05-24 (generated at 00:00 UTC)
2025-06-27 20:31:59,252 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-25 00:00 UTC (immediate)
2025-06-27 20:31:59,252 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:31:59,252 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-27 20:31:59,252 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-27 20:31:59,252 - root - INFO -    AAVE/USDT buy price: $269.2800 (close price)
2025-06-27 20:31:59,260 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,269 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,277 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,285 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,285 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,293 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,301 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,309 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,317 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,317 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,325 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,338 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,342 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,354 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,358 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,366 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,374 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,385 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,390 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,390 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,398 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,406 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,417 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,423 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,431 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,439 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,439 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,439 - root - INFO - ASSET CHANGE DETECTED on 2025-06-21:
2025-06-27 20:31:59,439 - root - INFO -    Signal Date: 2025-06-20 (generated at 00:00 UTC)
2025-06-27 20:31:59,439 - root - INFO -    AUTOMATIC EXECUTION: 2025-06-21 00:00 UTC (immediate)
2025-06-27 20:31:59,439 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:31:59,439 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-27 20:31:59,439 - root - INFO -    Buying: ['TRX/USDT']
2025-06-27 20:31:59,439 - root - INFO -    TRX/USDT buy price: $0.2710 (close price)
2025-06-27 20:31:59,447 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,455 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,463 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,471 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,479 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 20:31:59,479 - root - INFO - ASSET CHANGE DETECTED on 2025-06-26:
2025-06-27 20:31:59,479 - root - INFO -    Signal Date: 2025-06-25 (generated at 00:00 UTC)
2025-06-27 20:31:59,479 - root - INFO -    AUTOMATIC EXECUTION: 2025-06-26 00:00 UTC (immediate)
2025-06-27 20:31:59,479 - root - INFO -    Execution Delay: 0 hours
2025-06-27 20:31:59,479 - root - INFO -    Selling: ['TRX/USDT']
2025-06-27 20:31:59,479 - root - INFO -    Buying: ['BTC/USDT']
2025-06-27 20:31:59,479 - root - INFO -    BTC/USDT buy price: $106947.0600 (close price)
2025-06-27 20:31:59,511 - root - INFO - Entry trade at 2025-02-11 00:00:00+00:00: TRX/USDT
2025-06-27 20:31:59,511 - root - INFO - Swap trade at 2025-02-13 00:00:00+00:00: TRX/USDT -> BNB/USDT
2025-06-27 20:31:59,511 - root - INFO - Swap trade at 2025-02-19 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-27 20:31:59,511 - root - INFO - Swap trade at 2025-02-23 00:00:00+00:00: TRX/USDT -> BNB/USDT
2025-06-27 20:31:59,520 - root - INFO - Swap trade at 2025-02-24 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-27 20:31:59,520 - root - INFO - Swap trade at 2025-03-03 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-06-27 20:31:59,520 - root - INFO - Swap trade at 2025-03-11 00:00:00+00:00: ADA/USDT -> TRX/USDT
2025-06-27 20:31:59,520 - root - INFO - Swap trade at 2025-03-15 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-06-27 20:31:59,520 - root - INFO - Swap trade at 2025-03-17 00:00:00+00:00: ADA/USDT -> BNB/USDT
2025-06-27 20:31:59,520 - root - INFO - Swap trade at 2025-03-20 00:00:00+00:00: BNB/USDT -> XRP/USDT
2025-06-27 20:31:59,520 - root - INFO - Swap trade at 2025-03-22 00:00:00+00:00: XRP/USDT -> BNB/USDT
2025-06-27 20:31:59,520 - root - INFO - Swap trade at 2025-03-26 00:00:00+00:00: BNB/USDT -> AVAX/USDT
2025-06-27 20:31:59,520 - root - INFO - Swap trade at 2025-03-27 00:00:00+00:00: AVAX/USDT -> PEPE/USDT
2025-06-27 20:31:59,520 - root - INFO - Swap trade at 2025-03-31 00:00:00+00:00: PEPE/USDT -> BNB/USDT
2025-06-27 20:31:59,520 - root - INFO - Swap trade at 2025-04-01 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-27 20:31:59,520 - root - INFO - Swap trade at 2025-04-19 00:00:00+00:00: TRX/USDT -> SOL/USDT
2025-06-27 20:31:59,520 - root - INFO - Swap trade at 2025-04-24 00:00:00+00:00: SOL/USDT -> SUI/USDT
2025-06-27 20:31:59,520 - root - INFO - Swap trade at 2025-05-10 00:00:00+00:00: SUI/USDT -> PEPE/USDT
2025-06-27 20:31:59,520 - root - INFO - Swap trade at 2025-05-21 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-27 20:31:59,520 - root - INFO - Swap trade at 2025-05-23 00:00:00+00:00: AAVE/USDT -> PEPE/USDT
2025-06-27 20:31:59,520 - root - INFO - Swap trade at 2025-05-25 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-27 20:31:59,520 - root - INFO - Swap trade at 2025-06-21 00:00:00+00:00: AAVE/USDT -> TRX/USDT
2025-06-27 20:31:59,520 - root - INFO - Swap trade at 2025-06-26 00:00:00+00:00: TRX/USDT -> BTC/USDT
2025-06-27 20:31:59,520 - root - INFO - Total trades: 23 (Entries: 1, Exits: 0, Swaps: 22)
2025-06-27 20:31:59,520 - root - INFO - Strategy execution completed in 1s
2025-06-27 20:31:59,520 - root - INFO - DEBUG: self.elapsed_time = 1.1637251377105713 seconds
2025-06-27 20:31:59,528 - root - INFO - Saved allocation history to allocation_history_1d_1d_no_mtpi_no_rebal_independent_imcumbent_2025-02-10.csv
2025-06-27 20:31:59,528 - root - INFO - Strategy equity curve starts at: 2025-02-10
2025-06-27 20:31:59,528 - root - INFO - Assets included in buy-and-hold comparison:
2025-06-27 20:31:59,534 - root - INFO -   ETH/USDT: First available date 2024-12-12
2025-06-27 20:31:59,534 - root - INFO -   BTC/USDT: First available date 2024-12-12
2025-06-27 20:31:59,534 - root - INFO -   SOL/USDT: First available date 2024-12-12
2025-06-27 20:31:59,534 - root - INFO -   SUI/USDT: First available date 2024-12-12
2025-06-27 20:31:59,534 - root - INFO -   XRP/USDT: First available date 2024-12-12
2025-06-27 20:31:59,534 - root - INFO -   AAVE/USDT: First available date 2024-12-12
2025-06-27 20:31:59,534 - root - INFO -   AVAX/USDT: First available date 2024-12-12
2025-06-27 20:31:59,534 - root - INFO -   ADA/USDT: First available date 2024-12-12
2025-06-27 20:31:59,536 - root - INFO -   LINK/USDT: First available date 2024-12-12
2025-06-27 20:31:59,536 - root - INFO -   TRX/USDT: First available date 2024-12-12
2025-06-27 20:31:59,536 - root - INFO -   PEPE/USDT: First available date 2024-12-12
2025-06-27 20:31:59,536 - root - INFO -   DOGE/USDT: First available date 2024-12-12
2025-06-27 20:31:59,536 - root - INFO -   BNB/USDT: First available date 2024-12-12
2025-06-27 20:31:59,536 - root - INFO -   DOT/USDT: First available date 2024-12-12
2025-06-27 20:31:59,536 - root - INFO - Using provided start date for normalization: 2025-02-10 00:00:00+00:00
2025-06-27 20:31:59,536 - root - INFO - Normalized ETH/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 20:31:59,536 - root - INFO - Normalized BTC/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 20:31:59,536 - root - INFO - Normalized SOL/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 20:31:59,544 - root - INFO - Normalized SUI/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 20:31:59,544 - root - INFO - Normalized XRP/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 20:31:59,544 - root - INFO - Normalized AAVE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 20:31:59,544 - root - INFO - Normalized AVAX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 20:31:59,544 - root - INFO - Normalized ADA/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 20:31:59,552 - root - INFO - Normalized LINK/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 20:31:59,552 - root - INFO - Normalized TRX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 20:31:59,552 - root - INFO - Normalized PEPE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 20:31:59,552 - root - INFO - Normalized DOGE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 20:31:59,552 - root - INFO - Normalized BNB/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 20:31:59,560 - root - INFO - Normalized DOT/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 20:31:59,560 - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 197 points
2025-06-27 20:31:59,560 - root - INFO - ETH/USDT B&H total return: -9.22%
2025-06-27 20:31:59,560 - root - INFO - Added equity_curve to bnh_metrics for BTC/USDT with 197 points
2025-06-27 20:31:59,560 - root - INFO - BTC/USDT B&H total return: 9.77%
2025-06-27 20:31:59,567 - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 197 points
2025-06-27 20:31:59,567 - root - INFO - SOL/USDT B&H total return: -30.63%
2025-06-27 20:31:59,569 - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 197 points
2025-06-27 20:31:59,569 - root - INFO - SUI/USDT B&H total return: -19.11%
2025-06-27 20:31:59,569 - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 197 points
2025-06-27 20:31:59,569 - root - INFO - XRP/USDT B&H total return: -13.13%
2025-06-27 20:31:59,569 - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 197 points
2025-06-27 20:31:59,569 - root - INFO - AAVE/USDT B&H total return: -0.91%
2025-06-27 20:31:59,569 - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 197 points
2025-06-27 20:31:59,577 - root - INFO - AVAX/USDT B&H total return: -32.88%
2025-06-27 20:31:59,577 - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 197 points
2025-06-27 20:31:59,577 - root - INFO - ADA/USDT B&H total return: -22.16%
2025-06-27 20:31:59,577 - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 197 points
2025-06-27 20:31:59,577 - root - INFO - LINK/USDT B&H total return: -31.21%
2025-06-27 20:31:59,583 - root - INFO - Added equity_curve to bnh_metrics for TRX/USDT with 197 points
2025-06-27 20:31:59,583 - root - INFO - TRX/USDT B&H total return: 10.07%
2025-06-27 20:31:59,586 - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 197 points
2025-06-27 20:31:59,586 - root - INFO - PEPE/USDT B&H total return: -4.48%
2025-06-27 20:31:59,589 - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 197 points
2025-06-27 20:31:59,589 - root - INFO - DOGE/USDT B&H total return: -37.35%
2025-06-27 20:31:59,589 - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 197 points
2025-06-27 20:31:59,589 - root - INFO - BNB/USDT B&H total return: 3.82%
2025-06-27 20:31:59,589 - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 197 points
2025-06-27 20:31:59,593 - root - INFO - DOT/USDT B&H total return: -31.91%
2025-06-27 20:31:59,593 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-27 20:31:59,602 - root - INFO - Configuration loaded successfully.
2025-06-27 20:31:59,620 - root - INFO - Using colored segments for single-asset strategy visualization
2025-06-27 20:31:59,733 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-27 20:31:59,748 - root - INFO - Configuration loaded successfully.
2025-06-27 20:32:01,890 - root - INFO - Added ETH/USDT buy-and-hold curve with 197 points
2025-06-27 20:32:01,890 - root - INFO - Added BTC/USDT buy-and-hold curve with 197 points
2025-06-27 20:32:01,890 - root - INFO - Added SOL/USDT buy-and-hold curve with 197 points
2025-06-27 20:32:01,890 - root - INFO - Added SUI/USDT buy-and-hold curve with 197 points
2025-06-27 20:32:01,890 - root - INFO - Added XRP/USDT buy-and-hold curve with 197 points
2025-06-27 20:32:01,890 - root - INFO - Added AAVE/USDT buy-and-hold curve with 197 points
2025-06-27 20:32:01,890 - root - INFO - Added AVAX/USDT buy-and-hold curve with 197 points
2025-06-27 20:32:01,890 - root - INFO - Added ADA/USDT buy-and-hold curve with 197 points
2025-06-27 20:32:01,890 - root - INFO - Added LINK/USDT buy-and-hold curve with 197 points
2025-06-27 20:32:01,890 - root - INFO - Added TRX/USDT buy-and-hold curve with 197 points
2025-06-27 20:32:01,890 - root - INFO - Added PEPE/USDT buy-and-hold curve with 197 points
2025-06-27 20:32:01,890 - root - INFO - Added DOGE/USDT buy-and-hold curve with 197 points
2025-06-27 20:32:01,890 - root - INFO - Added BNB/USDT buy-and-hold curve with 197 points
2025-06-27 20:32:01,890 - root - INFO - Added DOT/USDT buy-and-hold curve with 197 points
2025-06-27 20:32:01,898 - root - INFO - Added 14 buy-and-hold curves to results
2025-06-27 20:32:01,898 - root - INFO -   - ETH/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 20:32:01,898 - root - INFO -   - BTC/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 20:32:01,898 - root - INFO -   - SOL/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 20:32:01,898 - root - INFO -   - SUI/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 20:32:01,898 - root - INFO -   - XRP/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 20:32:01,898 - root - INFO -   - AAVE/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 20:32:01,898 - root - INFO -   - AVAX/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 20:32:01,898 - root - INFO -   - ADA/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 20:32:01,898 - root - INFO -   - LINK/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 20:32:01,898 - root - INFO -   - TRX/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 20:32:01,898 - root - INFO -   - PEPE/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 20:32:01,898 - root - INFO -   - DOGE/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 20:32:01,898 - root - INFO -   - BNB/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 20:32:01,898 - root - INFO -   - DOT/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 20:32:01,916 - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-06-27 20:32:01,916 - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-27 20:32:01,916 - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-06-27 20:32:01,916 - root - INFO - MTPI disabled - skipping MTPI signal calculation
2025-06-27 20:32:01,924 - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-06-27 20:32:01,924 - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 8.0, 'BTC/USDT': 13.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-27 20:32:01,924 - root - ERROR - [DEBUG] CONVERSION - Original USDT scores: {'ETH/USDT': 8.0, 'BTC/USDT': 13.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-27 20:32:01,924 - root - ERROR - [DEBUG] CONVERSION - Original USDT keys order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-27 20:32:01,924 - root - ERROR - [DEBUG] CONVERSION - Conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-27 20:32:01,924 - root - ERROR - [DEBUG] CONVERSION - ETH/USDT -> ETH/EUR (score: 8.0)
2025-06-27 20:32:01,924 - root - ERROR - [DEBUG] CONVERSION - BTC/USDT -> BTC/EUR (score: 13.0)
2025-06-27 20:32:01,924 - root - ERROR - [DEBUG] CONVERSION - SOL/USDT -> SOL/EUR (score: 6.0)
2025-06-27 20:32:01,924 - root - ERROR - [DEBUG] CONVERSION - SUI/USDT -> SUI/EUR (score: 2.0)
2025-06-27 20:32:01,924 - root - ERROR - [DEBUG] CONVERSION - XRP/USDT -> XRP/EUR (score: 9.0)
2025-06-27 20:32:01,927 - root - ERROR - [DEBUG] CONVERSION - AAVE/USDT -> AAVE/EUR (score: 9.0)
2025-06-27 20:32:01,927 - root - ERROR - [DEBUG] CONVERSION - AVAX/USDT -> AVAX/EUR (score: 3.0)
2025-06-27 20:32:01,927 - root - ERROR - [DEBUG] CONVERSION - ADA/USDT -> ADA/EUR (score: 3.0)
2025-06-27 20:32:01,927 - root - ERROR - [DEBUG] CONVERSION - LINK/USDT -> LINK/EUR (score: 7.0)
2025-06-27 20:32:01,927 - root - ERROR - [DEBUG] CONVERSION - TRX/USDT -> TRX/EUR (score: 12.0)
2025-06-27 20:32:01,927 - root - ERROR - [DEBUG] CONVERSION - PEPE/USDT -> PEPE/EUR (score: 0.0)
2025-06-27 20:32:01,927 - root - ERROR - [DEBUG] CONVERSION - DOGE/USDT -> DOGE/EUR (score: 4.0)
2025-06-27 20:32:01,927 - root - ERROR - [DEBUG] CONVERSION - BNB/USDT -> BNB/EUR (score: 11.0)
2025-06-27 20:32:01,927 - root - ERROR - [DEBUG] CONVERSION - DOT/USDT -> DOT/EUR (score: 1.0)
2025-06-27 20:32:01,927 - root - ERROR - [DEBUG] CONVERSION - Final EUR scores: {'ETH/EUR': 8.0, 'BTC/EUR': 13.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 7.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 4.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-27 20:32:01,927 - root - ERROR - [DEBUG] CONVERSION - Final EUR keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-27 20:32:01,927 - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 8.0, 'BTC/EUR': 13.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 7.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 4.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-27 20:32:01,933 - root - INFO - Saved metrics to new file: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250620_run_********_203127.csv
2025-06-27 20:32:01,933 - root - INFO - Saved performance metrics to CSV: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250620_run_********_203127.csv
2025-06-27 20:32:01,933 - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-06-27 20:32:01,933 - root - INFO - Results type: <class 'dict'>
2025-06-27 20:32:01,933 - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-06-27 20:32:01,933 - root - INFO - Success flag set to: True
2025-06-27 20:32:01,933 - root - INFO - Message set to: Strategy calculation completed successfully
2025-06-27 20:32:01,933 - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 197 entries
2025-06-27 20:32:01,933 - root - INFO -   - buy_hold_curves: dict with 14 entries
2025-06-27 20:32:01,933 - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 197 entries
2025-06-27 20:32:01,933 - root - INFO -   - mtpi_signals: <class 'NoneType'>
2025-06-27 20:32:01,939 - root - INFO -   - mtpi_score: <class 'NoneType'>
2025-06-27 20:32:01,939 - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 197 entries
2025-06-27 20:32:01,939 - root - INFO -   - performance_metrics: dict with 3 entries
2025-06-27 20:32:01,939 - root - INFO -   - metrics_file: <class 'str'>
2025-06-27 20:32:01,939 - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-06-27 20:32:01,939 - root - INFO -   - success: <class 'bool'>
2025-06-27 20:32:01,939 - root - INFO -   - message: <class 'str'>
2025-06-27 20:32:01,939 - root - INFO - MTPI disabled - using default bullish signal (1) for trading execution
2025-06-27 20:32:01,939 - root - ERROR - [DEBUG] STRATEGY RESULTS - Available keys: ['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message']
2025-06-27 20:32:01,939 - root - INFO - [DEBUG] ASSET SELECTION - Extracted best_asset from best_asset_series: BTC/EUR
2025-06-27 20:32:01,939 - root - INFO - [DEBUG] ASSET SELECTION - Last 5 entries in best_asset_series: 2025-06-22 00:00:00+00:00    TRX/EUR
2025-06-23 00:00:00+00:00    TRX/EUR
2025-06-24 00:00:00+00:00    TRX/EUR
2025-06-25 00:00:00+00:00    TRX/EUR
2025-06-26 00:00:00+00:00    BTC/EUR
dtype: object
2025-06-27 20:32:01,939 - root - ERROR - [DEBUG] ASSET SELECTION - Latest scores available: {'ETH/EUR': 8.0, 'BTC/EUR': 13.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 7.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 4.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-27 20:32:01,939 - root - ERROR - [DEBUG] ASSET SELECTION - Scores dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-27 20:32:01,939 - root - INFO - [DEBUG] TIE-BREAKING - Strategy: imcumbent
2025-06-27 20:32:01,939 - root - ERROR - [DEBUG] ASSET SELECTION - Maximum score: 13.0
2025-06-27 20:32:01,939 - root - ERROR - [DEBUG] ASSET SELECTION - Assets with max score: ['BTC/EUR']
2025-06-27 20:32:01,939 - root - ERROR - [DEBUG] NO TIE - Single winner: BTC/EUR
2025-06-27 20:32:01,964 - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
       'BNB/EUR', 'DOT/EUR'],
      dtype='object')
2025-06-27 20:32:01,964 - root - INFO - Last row columns: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-27 20:32:01,964 - root - INFO - Single asset strategy with best asset: BTC/EUR
2025-06-27 20:32:01,964 - root - INFO - [DEBUG] FINAL ASSET SELECTION SUMMARY:
2025-06-27 20:32:01,964 - root - INFO - [DEBUG]   - Best asset selected: BTC/EUR
2025-06-27 20:32:01,964 - root - INFO - [DEBUG]   - Assets held: {'BTC/EUR': 1.0}
2025-06-27 20:32:01,964 - root - INFO - [DEBUG]   - MTPI signal: 1
2025-06-27 20:32:01,964 - root - INFO - [DEBUG]   - Use MTPI signal: False
2025-06-27 20:32:01,973 - root - INFO - Executing single-asset strategy with best asset: BTC/EUR
2025-06-27 20:32:01,973 - root - INFO - Executing strategy signal: best_asset=BTC/EUR, mtpi_signal=1, mode=paper
2025-06-27 20:32:01,974 - root - INFO - Incremented daily trade counter for BTC/EUR: 1/5
2025-06-27 20:32:01,974 - root - INFO - ASSET SELECTION - Attempting to enter position for selected asset: BTC/EUR
2025-06-27 20:32:01,974 - root - INFO - Attempting to enter position for BTC/EUR in paper mode
2025-06-27 20:32:01,974 - root - INFO - [DEBUG] TRADE - BTC/EUR: Starting enter_position attempt
2025-06-27 20:32:01,974 - root - INFO - [DEBUG] TRADE - BTC/EUR: Trading mode: paper
2025-06-27 20:32:01,974 - root - INFO - TRADE ATTEMPT - BTC/EUR: Getting current market price...
2025-06-27 20:32:01,974 - root - INFO - [DEBUG] PRICE - BTC/EUR: Starting get_current_price
2025-06-27 20:32:01,974 - root - INFO - [DEBUG] PRICE - BTC/EUR: Exchange ID: bitvavo
2025-06-27 20:32:01,974 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Initializing exchange bitvavo
2025-06-27 20:32:01,980 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Exchange initialized successfully
2025-06-27 20:32:01,980 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Exchange markets not loaded, loading now...
2025-06-27 20:32:02,291 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Symbol found after loading markets
2025-06-27 20:32:02,291 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Attempting to fetch ticker...
2025-06-27 20:32:02,336 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Ticker fetched successfully
2025-06-27 20:32:02,338 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Ticker data: {'symbol': 'BTC/EUR', 'timestamp': 1751049117282, 'datetime': '2025-06-27T18:31:57.282Z', 'high': 92535.0, 'low': 90764.0, 'bid': 91273.0, 'bidVolume': 0.04627261, 'ask': 91281.0, 'askVolume': 0.0257467, 'vwap': 91516.8092139245, 'open': 91527.0, 'close': 91280.0, 'last': 91280.0, 'previousClose': None, 'change': -247.0, 'percentage': -0.2698657226829241, 'average': 91403.5, 'baseVolume': 321.57095003, 'quoteVolume': 29429147.28263596, 'info': {'market': 'BTC-EUR', 'startTimestamp': 1750962717282, 'timestamp': 1751049117282, 'open': '91527', 'openTimestamp': 1750962730414, 'high': '92535', 'low': '90764', 'last': '9.128E+4', 'closeTimestamp': 1751049113383, 'bid': '91273', 'bidSize': '0.04627261', 'ask': '91281', 'askSize': '0.02574670', 'volume': '321.57095003', 'volumeQuote': '29429147.28263596'}, 'indexPrice': None, 'markPrice': None}
2025-06-27 20:32:02,338 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Last price: 91280.0
2025-06-27 20:32:02,338 - root - INFO - [DEBUG] TRADE - BTC/EUR: get_current_price returned: 91280.0
2025-06-27 20:32:02,338 - root - INFO - [DEBUG] TRADE - BTC/EUR: Price type: <class 'float'>
2025-06-27 20:32:02,338 - root - INFO - [DEBUG] TRADE - BTC/EUR: Price evaluation - not price: False
2025-06-27 20:32:02,338 - root - INFO - [DEBUG] TRADE - BTC/EUR: Price evaluation - price <= 0: False
2025-06-27 20:32:02,338 - root - INFO - TRADE ATTEMPT - BTC/EUR: Current price: 91280.********
2025-06-27 20:32:02,338 - root - INFO - Available balance for EUR: 100.********
2025-06-27 20:32:02,361 - root - INFO - Loaded market info for 176 trading pairs
2025-06-27 20:32:02,363 - root - INFO - Calculated position size for BTC/EUR: 0.******** (using 10% of 100, accounting for fees)
2025-06-27 20:32:02,363 - root - INFO - Calculated position size: 0.******** BTC
2025-06-27 20:32:02,363 - root - INFO - Entering position for BTC/EUR: 0.******** units at 91280.******** (value: 9.******** EUR)
2025-06-27 20:32:02,363 - root - INFO - Executing paper market buy order for BTC/EUR
2025-06-27 20:32:02,363 - root - INFO - Paper trading buy for BTC/EUR: original=0.********, adjusted=0.********, after_fee=0.********
2025-06-27 20:32:02,366 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-27 20:32:02,366 - root - INFO - Created paper market buy order: BTC/EUR, amount: 0.********177202015778, price: 91280.0
2025-06-27 20:32:02,366 - root - INFO - Filled amount: 0.******** BTC
2025-06-27 20:32:02,366 - root - INFO - Order fee: 0.******** EUR
2025-06-27 20:32:02,366 - root - INFO - Successfully entered position: BTC/EUR, amount: 0.********, price: 91280.********
2025-06-27 20:32:02,368 - root - INFO - Trade executed: BUY BTC/EUR, amount=0.********, price=91280.********, filled=0.********
2025-06-27 20:32:02,368 - root - INFO -   Fee: 0.******** EUR
2025-06-27 20:32:02,368 - root - INFO - TRADE SUCCESS - BTC/EUR: Successfully updated current asset
2025-06-27 20:32:02,368 - root - INFO - Trade executed: BUY BTC/EUR, amount=0.********, price=91280.********, filled=0.********
2025-06-27 20:32:02,368 - root - INFO -   Fee: 0.******** EUR
2025-06-27 20:32:02,373 - root - INFO - Single-asset trade result logged to trade log file
2025-06-27 20:32:02,373 - root - INFO - Trade executed: {'success': True, 'symbol': 'BTC/EUR', 'side': 'buy', 'amount': 0.00010900525854513585, 'price': 91280.0, 'order': {'id': 'paper-1751049122-BTC/EUR-buy-0.********177202015778', 'symbol': 'BTC/EUR', 'side': 'buy', 'type': 'market', 'amount': 0.********177202015778, 'price': 91280.0, 'cost': 9.90025********02, 'fee': {'cost': 0.****************01, 'currency': 'EUR', 'rate': 0.001}, 'status': 'closed', 'filled': 0.********177202015778, 'remaining': 0, 'timestamp': 1751049122365, 'datetime': '2025-06-27T20:32:02.365331', 'trades': [], 'average': 91280.0, 'average_price': 91280.0}, 'filled_amount': 0.********177202015778, 'fee': {'cost': 0.****************01, 'currency': 'EUR', 'rate': 0.001}, 'quote_currency': 'EUR', 'base_currency': 'BTC', 'timestamp': '2025-06-27T20:32:02.368066'}
2025-06-27 20:32:02,602 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-27 20:32:02,614 - root - INFO - Asset selection logged: 1 assets selected with single_asset allocation
2025-06-27 20:32:02,615 - root - INFO - Asset scores (sorted by score):
2025-06-27 20:32:02,617 - root - INFO -   BTC/EUR: score=13.0, status=SELECTED, weight=1.00
2025-06-27 20:32:02,617 - root - INFO -   TRX/EUR: score=12.0, status=NOT SELECTED, weight=0.00
2025-06-27 20:32:02,617 - root - INFO -   BNB/EUR: score=11.0, status=NOT SELECTED, weight=0.00
2025-06-27 20:32:02,618 - root - INFO -   XRP/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-27 20:32:02,618 - root - INFO -   AAVE/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-27 20:32:02,618 - root - INFO -   ETH/EUR: score=8.0, status=NOT SELECTED, weight=0.00
2025-06-27 20:32:02,618 - root - INFO -   LINK/EUR: score=7.0, status=NOT SELECTED, weight=0.00
2025-06-27 20:32:02,618 - root - INFO -   SOL/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-27 20:32:02,619 - root - INFO -   DOGE/EUR: score=4.0, status=NOT SELECTED, weight=0.00
2025-06-27 20:32:02,619 - root - INFO -   AVAX/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-27 20:32:02,619 - root - INFO -   ADA/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-27 20:32:02,619 - root - INFO -   SUI/EUR: score=2.0, status=NOT SELECTED, weight=0.00
2025-06-27 20:32:02,620 - root - INFO -   DOT/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-06-27 20:32:02,620 - root - INFO -   PEPE/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-06-27 20:32:02,620 - root - INFO - Asset selection logged with 14 assets scored and 1 assets selected
2025-06-27 20:32:02,620 - root - INFO - MTPI disabled - skipping MTPI score extraction
2025-06-27 20:32:02,620 - root - INFO - Extracted asset scores: {'ETH/EUR': 8.0, 'BTC/EUR': 13.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 7.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 4.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-27 20:32:02,724 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-27 20:32:02,726 - root - INFO - Strategy execution completed successfully in 34.72 seconds
2025-06-27 20:32:02,729 - root - INFO - Saved recovery state to data/state\recovery_state.json
2025-06-27 20:32:07,726 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:32:17,755 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:32:27,776 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:32:37,833 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:32:47,866 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:32:57,900 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:33:08,038 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:33:18,050 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:33:28,069 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:33:38,083 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:33:48,094 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:33:58,105 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:34:08,127 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:34:18,138 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:34:28,160 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:34:38,172 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:34:48,189 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:34:58,206 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:35:08,219 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:35:18,290 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:35:28,307 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:35:38,426 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:35:48,500 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:35:58,536 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:36:08,673 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:36:18,703 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:36:28,728 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:36:38,747 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:36:48,813 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:36:58,847 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:37:08,863 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:37:18,920 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:37:28,955 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:37:38,991 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:37:49,025 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:37:59,062 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:38:09,095 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:38:19,112 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:38:29,166 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:38:39,178 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:38:49,195 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:38:59,208 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:39:09,228 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:39:19,245 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:39:29,253 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:39:39,311 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:39:49,328 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:39:59,378 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:40:09,411 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:40:19,450 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:40:29,487 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:40:39,517 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:40:49,552 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:40:59,592 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:41:09,626 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:41:19,660 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:41:29,698 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:41:39,730 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:41:49,768 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:41:59,784 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:42:09,838 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:42:19,868 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:42:29,894 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:42:39,943 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:42:49,978 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:43:00,018 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:43:10,046 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:43:20,083 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:43:30,113 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:43:40,147 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:43:50,185 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:44:00,325 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:44:10,343 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:44:20,367 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:44:30,946 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:44:40,979 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:44:51,015 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:45:01,032 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:45:04,500 - root - INFO - Received signal 2, shutting down...
2025-06-27 20:45:11,049 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 20:45:14,569 - root - INFO - Network watchdog stopped
2025-06-27 20:45:14,603 - root - INFO - Network watchdog stopped
2025-06-27 20:45:14,603 - root - INFO - Background service stopped
2025-06-27 20:45:14,696 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
