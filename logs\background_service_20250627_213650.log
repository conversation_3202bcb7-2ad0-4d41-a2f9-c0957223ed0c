2025-06-27 21:36:50,232 - root - INFO - Loaded environment variables from .env file
2025-06-27 21:36:51,568 - root - INFO - Loaded 4 trade records from logs/trades\trade_log_********.json
2025-06-27 21:36:51,569 - root - INFO - Loaded 2 asset selection records from logs/trades\asset_selection_********.json
2025-06-27 21:36:51,569 - root - INFO - Trade logger initialized with log directory: logs/trades
2025-06-27 21:36:53,613 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 21:36:53,635 - root - INFO - Configuration loaded successfully.
2025-06-27 21:36:53,635 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 21:36:53,648 - root - INFO - Configuration loaded successfully.
2025-06-27 21:36:53,648 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 21:36:53,663 - root - INFO - Configuration loaded successfully.
2025-06-27 21:36:53,663 - root - INFO - Loading notification configuration from config/notifications_bitvavo.json...
2025-06-27 21:36:53,663 - root - INFO - Notification configuration loaded successfully.
2025-06-27 21:36:54,881 - root - INFO - Telegram command handlers registered
2025-06-27 21:36:54,881 - root - INFO - Telegram bot polling started
2025-06-27 21:36:54,881 - root - INFO - Telegram notifier initialized with notification level: standard
2025-06-27 21:36:54,883 - root - INFO - Telegram notification channel initialized
2025-06-27 21:36:54,886 - root - INFO - Successfully loaded templates using utf-8 encoding
2025-06-27 21:36:54,886 - root - INFO - Loaded 24 templates from file
2025-06-27 21:36:54,886 - root - INFO - Notification manager initialized with 1 channels
2025-06-27 21:36:54,886 - root - INFO - Notification manager initialized
2025-06-27 21:36:54,886 - root - INFO - Added critical time window: 23:55 for 10 minutes - Before daily candle close (1d)
2025-06-27 21:36:54,887 - root - INFO - Added critical time window: 00:00 for 10 minutes - After daily candle close (1d)
2025-06-27 21:36:54,887 - root - INFO - Set up critical time windows for 1d timeframe
2025-06-27 21:36:54,887 - root - INFO - Network watchdog initialized with 10s check interval
2025-06-27 21:36:54,888 - root - INFO - Loaded recovery state from data/state\recovery_state.json
2025-06-27 21:36:54,888 - root - INFO - No state file found at C:\Users\<USER>\OneDrive\Stalinis kompiuteris\Asset_Screener_Python\data\state\data/state\background_service_20250626_114325.json.json
2025-06-27 21:36:54,889 - root - INFO - Recovery manager initialized
2025-06-27 21:36:54,889 - root - INFO - [DEBUG] TRADING EXECUTOR - Initializing with exchange: bitvavo
2025-06-27 21:36:54,889 - root - INFO - [DEBUG] TRADING EXECUTOR - Trading config: {'enabled': True, 'exchange': 'bitvavo', 'initial_capital': 100, 'max_slippage_pct': 0.5, 'min_order_values': {'default': 5.0}, 'mode': 'paper', 'order_type': 'market', 'position_size_pct': 10, 'risk_management': {'max_daily_trades': 5, 'max_open_positions': 10}, 'transaction_fee_rate': 0.0025}
2025-06-27 21:36:54,889 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 21:36:54,897 - root - INFO - Configuration loaded successfully.
2025-06-27 21:36:54,902 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-27 21:36:54,902 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-27 21:36:54,902 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-27 21:36:54,902 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-27 21:36:54,902 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-27 21:36:54,902 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-27 21:36:54,902 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-27 21:36:54,902 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-27 21:36:54,902 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 21:36:54,911 - root - INFO - Configuration loaded successfully.
2025-06-27 21:36:54,959 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getMe "HTTP/1.1 200 OK"
2025-06-27 21:36:54,969 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/deleteWebhook "HTTP/1.1 200 OK"
2025-06-27 21:36:54,969 - telegram.ext.Application - INFO - Application started
2025-06-27 21:36:55,462 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-27 21:36:55,462 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-27 21:36:55,462 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-27 21:36:55,462 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-27 21:36:55,462 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-27 21:36:55,462 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 21:36:55,476 - root - INFO - Configuration loaded successfully.
2025-06-27 21:36:55,476 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 21:36:55,483 - root - INFO - Configuration loaded successfully.
2025-06-27 21:36:55,483 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-27 21:36:55,494 - root - INFO - Configuration loaded successfully.
2025-06-27 21:36:55,494 - root - INFO - Loaded paper trading history from paper_trading_history.json
2025-06-27 21:36:55,494 - root - INFO - Paper trading initialized with EUR as quote currency
2025-06-27 21:36:55,494 - root - INFO - Trading executor initialized for bitvavo
2025-06-27 21:36:55,494 - root - INFO - Trading mode: paper
2025-06-27 21:36:55,494 - root - INFO - Trading enabled: True
2025-06-27 21:36:55,494 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-27 21:36:55,494 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-27 21:36:55,494 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-27 21:36:55,502 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-27 21:36:55,502 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 21:36:55,511 - root - INFO - Configuration loaded successfully.
2025-06-27 21:36:55,761 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-27 21:36:55,761 - root - INFO - Trading enabled in paper mode
2025-06-27 21:36:55,761 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-27 21:36:55,761 - root - INFO - Reset paper trading account to initial balance: 100 EUR
2025-06-27 21:36:55,761 - root - INFO - Reset paper trading account to initial balance
2025-06-27 21:36:55,761 - root - INFO - Generated run ID: ********_213655
2025-06-27 21:36:55,761 - root - INFO - Ensured metrics directory exists: Performance_Metrics
2025-06-27 21:36:55,761 - root - INFO - Background service initialized
2025-06-27 21:36:55,769 - root - INFO - Network watchdog started
2025-06-27 21:36:55,769 - root - INFO - Network watchdog started
2025-06-27 21:36:55,769 - root - INFO - Schedule set up for 1d timeframe
2025-06-27 21:36:55,769 - root - INFO - Background service started
2025-06-27 21:36:55,774 - root - INFO - Executing strategy (run #1)...
2025-06-27 21:36:55,774 - root - INFO - Resetting daily trade counters for this strategy execution
2025-06-27 21:36:55,775 - root - INFO - No trades recorded today (Max: 5)
2025-06-27 21:36:55,776 - root - INFO - Initialized daily trades counter for 2025-06-27
2025-06-27 21:36:55,777 - root - INFO - Creating snapshot for candle timestamp: ********
2025-06-27 21:36:55,918 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-27 21:37:01,786 - root - WARNING - Failed to send with Markdown formatting: Timed out
2025-06-27 21:37:02,094 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-27 21:37:02,098 - root - INFO - Using trend method 'PGO For Loop' for strategy execution
2025-06-27 21:37:02,099 - root - INFO - Using configured start date for 1d timeframe: 2025-02-10
2025-06-27 21:37:02,100 - root - INFO - Using recent date for performance tracking: 2025-06-20
2025-06-27 21:37:02,102 - root - INFO - Using real-time data fetching - only fetching new data since last cached timestamp
2025-06-27 21:37:02,238 - root - INFO - Loaded 2138 rows of ETH/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:37:02,239 - root - INFO - Last timestamp in cache for ETH/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:37:02,240 - root - INFO - Expected last timestamp for ETH/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:37:02,241 - root - INFO - Data is up to date for ETH/USDT
2025-06-27 21:37:02,244 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:37:02,315 - root - INFO - Loaded 2138 rows of BTC/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:37:02,316 - root - INFO - Last timestamp in cache for BTC/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:37:02,317 - root - INFO - Expected last timestamp for BTC/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:37:02,318 - root - INFO - Data is up to date for BTC/USDT
2025-06-27 21:37:02,322 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:37:02,375 - root - INFO - Loaded 1781 rows of SOL/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:37:02,376 - root - INFO - Last timestamp in cache for SOL/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:37:02,377 - root - INFO - Expected last timestamp for SOL/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:37:02,377 - root - INFO - Data is up to date for SOL/USDT
2025-06-27 21:37:02,380 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:37:02,405 - root - INFO - Loaded 786 rows of SUI/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:37:02,409 - root - INFO - Last timestamp in cache for SUI/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:37:02,411 - root - INFO - Expected last timestamp for SUI/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:37:02,411 - root - INFO - Data is up to date for SUI/USDT
2025-06-27 21:37:02,411 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:37:02,479 - root - INFO - Loaded 2138 rows of XRP/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:37:02,481 - root - INFO - Last timestamp in cache for XRP/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:37:02,483 - root - INFO - Expected last timestamp for XRP/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:37:02,483 - root - INFO - Data is up to date for XRP/USDT
2025-06-27 21:37:02,483 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:37:02,542 - root - INFO - Loaded 1716 rows of AAVE/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:37:02,542 - root - INFO - Last timestamp in cache for AAVE/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:37:02,542 - root - INFO - Expected last timestamp for AAVE/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:37:02,542 - root - INFO - Data is up to date for AAVE/USDT
2025-06-27 21:37:02,542 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:37:02,595 - root - INFO - Loaded 1739 rows of AVAX/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:37:02,595 - root - INFO - Last timestamp in cache for AVAX/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:37:02,598 - root - INFO - Expected last timestamp for AVAX/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:37:02,598 - root - INFO - Data is up to date for AVAX/USDT
2025-06-27 21:37:02,600 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:37:02,659 - root - INFO - Loaded 2138 rows of ADA/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:37:02,659 - root - INFO - Last timestamp in cache for ADA/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:37:02,659 - root - INFO - Expected last timestamp for ADA/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:37:02,659 - root - INFO - Data is up to date for ADA/USDT
2025-06-27 21:37:02,666 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:37:02,728 - root - INFO - Loaded 2138 rows of LINK/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:37:02,729 - root - INFO - Last timestamp in cache for LINK/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:37:02,729 - root - INFO - Expected last timestamp for LINK/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:37:02,729 - root - INFO - Data is up to date for LINK/USDT
2025-06-27 21:37:02,734 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:37:02,803 - root - INFO - Loaded 2138 rows of TRX/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:37:02,803 - root - INFO - Last timestamp in cache for TRX/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:37:02,803 - root - INFO - Expected last timestamp for TRX/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:37:02,803 - root - INFO - Data is up to date for TRX/USDT
2025-06-27 21:37:02,810 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:37:02,831 - root - INFO - Loaded 784 rows of PEPE/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:37:02,840 - root - INFO - Last timestamp in cache for PEPE/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:37:02,841 - root - INFO - Expected last timestamp for PEPE/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:37:02,841 - root - INFO - Data is up to date for PEPE/USDT
2025-06-27 21:37:02,843 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:37:02,913 - root - INFO - Loaded 2138 rows of DOGE/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:37:02,913 - root - INFO - Last timestamp in cache for DOGE/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:37:02,915 - root - INFO - Expected last timestamp for DOGE/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:37:02,915 - root - INFO - Data is up to date for DOGE/USDT
2025-06-27 21:37:02,916 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:37:02,980 - root - INFO - Loaded 2138 rows of BNB/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:37:02,980 - root - INFO - Last timestamp in cache for BNB/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:37:02,982 - root - INFO - Expected last timestamp for BNB/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:37:02,982 - root - INFO - Data is up to date for BNB/USDT
2025-06-27 21:37:02,986 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:37:03,041 - root - INFO - Loaded 1774 rows of DOT/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:37:03,042 - root - INFO - Last timestamp in cache for DOT/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:37:03,042 - root - INFO - Expected last timestamp for DOT/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:37:03,042 - root - INFO - Data is up to date for DOT/USDT
2025-06-27 21:37:03,042 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:37:03,053 - root - INFO - Using 14 trend assets (USDT) for analysis and 14 trading assets (EUR) for execution
2025-06-27 21:37:03,053 - root - INFO - MTPI Multi-Indicator Configuration:
2025-06-27 21:37:03,053 - root - INFO -   - Number of indicators: 8
2025-06-27 21:37:03,053 - root - INFO -   - Enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-27 21:37:03,057 - root - INFO -   - Combination method: consensus
2025-06-27 21:37:03,058 - root - INFO -   - Long threshold: 0.1
2025-06-27 21:37:03,058 - root - INFO -   - Short threshold: -0.1
2025-06-27 21:37:03,060 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trend_assets order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-27 21:37:03,060 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trading_assets order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-27 21:37:03,061 - root - ERROR - [DEBUG] BACKGROUND SERVICE - BTC position in trend_assets: 2
2025-06-27 21:37:03,061 - root - ERROR - [DEBUG] BACKGROUND SERVICE - TRX position in trend_assets: 10
2025-06-27 21:37:03,061 - root - INFO - === RUNNING STRATEGY FOR WEB USING TEST_ALLOCATION ===
2025-06-27 21:37:03,061 - root - INFO - Parameters: use_mtpi_signal=False, mtpi_indicator_type=PGO
2025-06-27 21:37:03,063 - root - INFO - mtpi_timeframe=1d, mtpi_pgo_length=35
2025-06-27 21:37:03,063 - root - INFO - mtpi_upper_threshold=1.1, mtpi_lower_threshold=-0.58
2025-06-27 21:37:03,065 - root - INFO - timeframe=1d, analysis_start_date='2025-02-10'
2025-06-27 21:37:03,066 - root - INFO - n_assets=1, use_weighted_allocation=False
2025-06-27 21:37:03,066 - root - INFO - Using provided trend method: PGO For Loop
2025-06-27 21:37:03,066 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 21:37:03,097 - root - INFO - Configuration loaded successfully.
2025-06-27 21:37:03,097 - root - INFO - Saving configuration to config/settings_bitvavo_eur.yaml...
2025-06-27 21:37:03,120 - root - INFO - Configuration saved successfully.
2025-06-27 21:37:03,120 - root - INFO - Trend detection assets (USDT): ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-27 21:37:03,120 - root - INFO - Number of trend detection assets: 14
2025-06-27 21:37:03,122 - root - INFO - Selected assets type: <class 'list'>
2025-06-27 21:37:03,122 - root - INFO - Trading execution assets (EUR): ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-27 21:37:03,122 - root - INFO - Number of trading assets: 14
2025-06-27 21:37:03,122 - root - INFO - Trading assets type: <class 'list'>
2025-06-27 21:37:03,983 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-27 21:37:04,030 - root - INFO - Configuration loaded successfully.
2025-06-27 21:37:04,077 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 21:37:04,111 - root - INFO - Configuration loaded successfully.
2025-06-27 21:37:04,111 - root - INFO - Execution context: backtesting
2025-06-27 21:37:04,111 - root - INFO - Execution timing: candle_close
2025-06-27 21:37:04,111 - root - INFO - Ratio calculation method: manual_inversion
2025-06-27 21:37:04,111 - root - INFO - Tie-breaking strategy: imcumbent
2025-06-27 21:37:04,111 - root - INFO - Asset trend PGO parameters: length=None, upper_threshold=None, lower_threshold=None
2025-06-27 21:37:04,111 - root - INFO - MTPI indicators override: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-27 21:37:04,111 - root - INFO - MTPI combination method override: consensus
2025-06-27 21:37:04,111 - root - INFO - MTPI long threshold override: 0.1
2025-06-27 21:37:04,111 - root - INFO - MTPI short threshold override: -0.1
2025-06-27 21:37:04,111 - root - INFO - Using standard warmup period of 60 days for equal allocation
2025-06-27 21:37:04,111 - root - INFO - Fetching data from 2024-12-12 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-27 21:37:04,121 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:37:04,121 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:37:04,125 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:37:04,125 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:37:04,135 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:37:04,137 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:37:04,137 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:37:04,139 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:37:04,142 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:37:04,142 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:37:04,142 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:37:04,142 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:37:04,147 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:37:04,147 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:37:04,149 - root - INFO - Checking cache for 14 symbols (1d)...
2025-06-27 21:37:04,221 - root - INFO - Loaded 197 rows of ETH/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:37:04,221 - root - INFO - Metadata for ETH/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:37:04,221 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:37:04,221 - root - INFO - Loaded 197 rows of ETH/USDT data from cache (after filtering).
2025-06-27 21:37:04,282 - root - INFO - Loaded 197 rows of BTC/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:37:04,292 - root - INFO - Metadata for BTC/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:37:04,292 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:37:04,292 - root - INFO - Loaded 197 rows of BTC/USDT data from cache (after filtering).
2025-06-27 21:37:04,348 - root - INFO - Loaded 197 rows of SOL/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:37:04,348 - root - INFO - Metadata for SOL/USDT shows data starting from 2020-08-11, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:37:04,359 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:37:04,359 - root - INFO - Loaded 197 rows of SOL/USDT data from cache (after filtering).
2025-06-27 21:37:04,391 - root - INFO - Loaded 197 rows of SUI/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:37:04,397 - root - INFO - Metadata for SUI/USDT shows data starting from 2023-05-03, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:37:04,399 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:37:04,399 - root - INFO - Loaded 197 rows of SUI/USDT data from cache (after filtering).
2025-06-27 21:37:04,462 - root - INFO - Loaded 197 rows of XRP/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:37:04,470 - root - INFO - Metadata for XRP/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:37:04,470 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:37:04,470 - root - INFO - Loaded 197 rows of XRP/USDT data from cache (after filtering).
2025-06-27 21:37:04,530 - root - INFO - Loaded 197 rows of AAVE/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:37:04,534 - root - INFO - Metadata for AAVE/USDT shows data starting from 2020-10-15, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:37:04,536 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:37:04,538 - root - INFO - Loaded 197 rows of AAVE/USDT data from cache (after filtering).
2025-06-27 21:37:04,594 - root - INFO - Loaded 197 rows of AVAX/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:37:04,595 - root - INFO - Metadata for AVAX/USDT shows data starting from 2020-09-22, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:37:04,595 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:37:04,595 - root - INFO - Loaded 197 rows of AVAX/USDT data from cache (after filtering).
2025-06-27 21:37:04,648 - root - INFO - Loaded 197 rows of ADA/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:37:04,660 - root - INFO - Metadata for ADA/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:37:04,660 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:37:04,660 - root - INFO - Loaded 197 rows of ADA/USDT data from cache (after filtering).
2025-06-27 21:37:04,725 - root - INFO - Loaded 197 rows of LINK/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:37:04,730 - root - INFO - Metadata for LINK/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:37:04,730 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:37:04,732 - root - INFO - Loaded 197 rows of LINK/USDT data from cache (after filtering).
2025-06-27 21:37:04,794 - root - INFO - Loaded 197 rows of TRX/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:37:04,797 - root - INFO - Metadata for TRX/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:37:04,799 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:37:04,799 - root - INFO - Loaded 197 rows of TRX/USDT data from cache (after filtering).
2025-06-27 21:37:04,830 - root - INFO - Loaded 197 rows of PEPE/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:37:04,830 - root - INFO - Metadata for PEPE/USDT shows data starting from 2023-05-05, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:37:04,830 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:37:04,830 - root - INFO - Loaded 197 rows of PEPE/USDT data from cache (after filtering).
2025-06-27 21:37:04,909 - root - INFO - Loaded 197 rows of DOGE/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:37:04,909 - root - INFO - Metadata for DOGE/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:37:04,909 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:37:04,909 - root - INFO - Loaded 197 rows of DOGE/USDT data from cache (after filtering).
2025-06-27 21:37:04,966 - root - INFO - Loaded 197 rows of BNB/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:37:04,976 - root - INFO - Metadata for BNB/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:37:04,976 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:37:04,976 - root - INFO - Loaded 197 rows of BNB/USDT data from cache (after filtering).
2025-06-27 21:37:05,022 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:37:05,034 - root - INFO - Loaded 197 rows of DOT/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:37:05,042 - root - INFO - Metadata for DOT/USDT shows data starting from 2020-08-18, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:37:05,042 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:37:05,042 - root - INFO - Loaded 197 rows of DOT/USDT data from cache (after filtering).
2025-06-27 21:37:05,042 - root - INFO - All 14 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-27 21:37:05,042 - root - INFO - Asset ETH/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:37:05,048 - root - INFO - Asset BTC/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:37:05,048 - root - INFO - Asset SOL/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:37:05,048 - root - INFO - Asset SUI/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:37:05,048 - root - INFO - Asset XRP/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:37:05,048 - root - INFO - Asset AAVE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:37:05,048 - root - INFO - Asset AVAX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:37:05,048 - root - INFO - Asset ADA/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:37:05,048 - root - INFO - Asset LINK/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:37:05,048 - root - INFO - Asset TRX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:37:05,055 - root - INFO - Asset PEPE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:37:05,055 - root - INFO - Asset DOGE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:37:05,055 - root - INFO - Asset BNB/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:37:05,055 - root - INFO - Asset DOT/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:37:05,125 - root - INFO - Using standard MTPI warmup period of 120 days
2025-06-27 21:37:05,125 - root - INFO - Fetching MTPI signals from 2024-10-13 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-27 21:37:05,125 - root - INFO - Fetching MTPI signals using trend method: PGO For Loop
2025-06-27 21:37:05,125 - root - INFO - Using multi-indicator MTPI with config override: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-27 21:37:05,125 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-27 21:37:05,150 - root - INFO - Configuration loaded successfully.
2025-06-27 21:37:05,158 - root - INFO - Loaded MTPI multi-indicator configuration with 8 enabled indicators
2025-06-27 21:37:05,158 - root - INFO - Applying configuration overrides: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-27 21:37:05,160 - root - INFO - Override: enabled_indicators = ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-27 21:37:05,160 - root - INFO - Override: combination_method = consensus
2025-06-27 21:37:05,160 - root - INFO - Override: long_threshold = 0.1
2025-06-27 21:37:05,160 - root - INFO - Override: short_threshold = -0.1
2025-06-27 21:37:05,162 - root - INFO - Using MTPI configuration: indicators=['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], method=consensus, thresholds=(-0.1, 0.1)
2025-06-27 21:37:05,163 - root - INFO - Calculated appropriate limit for 1d timeframe: 500 candles (minimum 180.0 candles needed for 60 length indicator)
2025-06-27 21:37:05,163 - root - INFO - Using adjusted limit: 500 for max indicator length: 60
2025-06-27 21:37:05,163 - root - INFO - Checking cache for 1 symbols (1d)...
2025-06-27 21:37:05,220 - root - INFO - Loaded 257 rows of BTC/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:37:05,222 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:37:05,222 - root - INFO - Loaded 257 rows of BTC/USDT data from cache (after filtering).
2025-06-27 21:37:05,222 - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-27 21:37:05,225 - root - INFO - Fetched BTC data: 257 candles from 2024-10-13 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:37:05,226 - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-06-27 21:37:05,343 - root - INFO - Generated PGO Score signals: {-1: 105, 0: 34, 1: 118}
2025-06-27 21:37:05,349 - root - INFO - Generated pgo signals: 257 values
2025-06-27 21:37:05,349 - root - INFO - Generating Bollinger Band signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False, heikin_src=close
2025-06-27 21:37:05,349 - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-06-27 21:37:05,383 - root - INFO - Generated BB Score signals: {-1: 107, 0: 32, 1: 118}
2025-06-27 21:37:05,383 - root - INFO - Generated Bollinger Band signals: 257 values
2025-06-27 21:37:05,383 - root - INFO - Generated bollinger_bands signals: 257 values
2025-06-27 21:37:07,053 - root - INFO - Generated DWMA signals using Weighted SD method
2025-06-27 21:37:07,054 - root - INFO - Generated dwma_score signals: 257 values
2025-06-27 21:37:07,225 - root - INFO - Generated DEMA Supertrend signals
2025-06-27 21:37:07,227 - root - INFO - Signal distribution: {-1: 143, 0: 1, 1: 113}
2025-06-27 21:37:07,227 - root - INFO - Generated DEMA Super Score signals
2025-06-27 21:37:07,227 - root - INFO - Generated dema_super_score signals: 257 values
2025-06-27 21:37:07,559 - root - INFO - Generated DPSD signals
2025-06-27 21:37:07,559 - root - INFO - Signal distribution: {-1: 99, 0: 87, 1: 71}
2025-06-27 21:37:07,561 - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-06-27 21:37:07,561 - root - INFO - Generated dpsd_score signals: 257 values
2025-06-27 21:37:07,609 - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-06-27 21:37:07,610 - root - INFO - Generated AAD Score signals using SMA method
2025-06-27 21:37:07,612 - root - INFO - Generated aad_score signals: 257 values
2025-06-27 21:37:07,903 - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-06-27 21:37:07,903 - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-06-27 21:37:07,904 - root - INFO - Generated dynamic_ema_score signals: 257 values
2025-06-27 21:37:08,371 - root - INFO - Generated quantile_dema_score signals: 257 values
2025-06-27 21:37:08,409 - root - INFO - Combined 8 signals using arithmetic mean aggregation
2025-06-27 21:37:08,409 - root - INFO - Signal distribution: {1: 145, -1: 111, 0: 1}
2025-06-27 21:37:08,411 - root - INFO - Generated combined MTPI signals: 257 values using consensus method
2025-06-27 21:37:08,411 - root - INFO - Signal distribution: {1: 145, -1: 111, 0: 1}
2025-06-27 21:37:08,411 - root - INFO - Calculating daily scores using trend method: PGO For Loop...
2025-06-27 21:37:08,421 - root - INFO - Saving configuration to config/settings.yaml...
2025-06-27 21:37:08,450 - root - INFO - Configuration saved successfully.
2025-06-27 21:37:08,450 - root - INFO - Updated config with trend method: PGO For Loop and PGO parameters
2025-06-27 21:37:08,451 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-27 21:37:08,498 - root - INFO - Configuration loaded successfully.
2025-06-27 21:37:08,500 - root - INFO - Using ratio-based approach with PGO (PGO For Loop)...
2025-06-27 21:37:08,500 - root - INFO - Calculating ratio PGO signals for 14 assets with PGO(35) using full OHLCV data
2025-06-27 21:37:08,502 - root - INFO - Using ratio calculation method: manual_inversion
2025-06-27 21:37:08,596 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:08,707 - root - INFO - Calculated ratio PGO signal for ETH/USDT/BTC/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:08,793 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 21:37:08,793 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:08,871 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 21:37:08,896 - root - INFO - Calculated ratio PGO signal for ETH/USDT/SOL/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:08,977 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-27 21:37:08,977 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:09,060 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-27 21:37:09,087 - root - INFO - Calculated ratio PGO signal for ETH/USDT/SUI/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:09,177 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 21:37:09,177 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:09,259 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 21:37:09,293 - root - INFO - Calculated ratio PGO signal for ETH/USDT/XRP/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:09,375 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 21:37:09,375 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:09,449 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 21:37:09,480 - root - INFO - Calculated ratio PGO signal for ETH/USDT/AAVE/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:09,561 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-27 21:37:09,563 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:09,615 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-27 21:37:09,649 - root - INFO - Calculated ratio PGO signal for ETH/USDT/AVAX/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:09,737 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 21:37:09,737 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:09,816 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 21:37:09,845 - root - INFO - Calculated ratio PGO signal for ETH/USDT/ADA/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:09,929 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-27 21:37:09,929 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:10,006 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-27 21:37:10,030 - root - INFO - Calculated ratio PGO signal for ETH/USDT/LINK/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:10,127 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-27 21:37:10,129 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:10,216 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-27 21:37:10,245 - root - INFO - Calculated ratio PGO signal for ETH/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:10,327 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 21:37:10,327 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:10,403 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 21:37:10,430 - root - INFO - Calculated ratio PGO signal for ETH/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:10,514 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:37:10,514 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:10,594 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:37:10,619 - root - INFO - Calculated ratio PGO signal for ETH/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:10,708 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:10,808 - root - INFO - Calculated ratio PGO signal for ETH/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:10,888 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-27 21:37:10,888 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:10,959 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-27 21:37:10,994 - root - INFO - Calculated ratio PGO signal for ETH/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:11,059 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:11,173 - root - INFO - Calculated ratio PGO signal for BTC/USDT/SOL/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:11,259 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 21:37:11,261 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:11,332 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 21:37:11,361 - root - INFO - Calculated ratio PGO signal for BTC/USDT/SUI/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:11,438 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 21:37:11,438 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:11,521 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 21:37:11,547 - root - INFO - Calculated ratio PGO signal for BTC/USDT/XRP/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:11,626 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 21:37:11,633 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:11,710 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 21:37:11,742 - root - INFO - Calculated ratio PGO signal for BTC/USDT/AAVE/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:11,825 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-27 21:37:11,825 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:11,892 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-27 21:37:11,916 - root - INFO - Calculated ratio PGO signal for BTC/USDT/AVAX/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:11,999 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 21:37:11,999 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:12,078 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 21:37:12,103 - root - INFO - Calculated ratio PGO signal for BTC/USDT/ADA/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:12,183 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 21:37:12,183 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:12,261 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 21:37:12,283 - root - INFO - Calculated ratio PGO signal for BTC/USDT/LINK/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:12,356 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:37:12,357 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:12,425 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:37:12,450 - root - INFO - Calculated ratio PGO signal for BTC/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:12,520 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 21:37:12,521 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:12,586 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 21:37:12,610 - root - INFO - Calculated ratio PGO signal for BTC/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:12,686 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:12,760 - root - INFO - Calculated ratio PGO signal for BTC/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:12,835 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:12,917 - root - INFO - Calculated ratio PGO signal for BTC/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:12,987 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-27 21:37:12,992 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:13,057 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-27 21:37:13,079 - root - INFO - Calculated ratio PGO signal for BTC/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:13,150 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:37:13,150 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:13,210 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:37:13,236 - root - INFO - Calculated ratio PGO signal for SOL/USDT/SUI/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:13,312 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 21:37:13,312 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:13,375 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 21:37:13,397 - root - INFO - Calculated ratio PGO signal for SOL/USDT/XRP/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:13,461 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-27 21:37:13,469 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:13,536 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-27 21:37:13,560 - root - INFO - Calculated ratio PGO signal for SOL/USDT/AAVE/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:13,629 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-27 21:37:13,629 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:13,694 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-27 21:37:13,710 - root - INFO - Calculated ratio PGO signal for SOL/USDT/AVAX/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:13,784 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 21:37:13,784 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:13,848 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 21:37:13,868 - root - INFO - Calculated ratio PGO signal for SOL/USDT/ADA/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:13,942 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 21:37:13,942 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:14,007 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 21:37:14,030 - root - INFO - Calculated ratio PGO signal for SOL/USDT/LINK/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:14,100 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-27 21:37:14,100 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:14,164 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-27 21:37:14,190 - root - INFO - Calculated ratio PGO signal for SOL/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:14,260 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-27 21:37:14,260 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:14,329 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-27 21:37:14,348 - root - INFO - Calculated ratio PGO signal for SOL/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:14,414 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:37:14,414 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:14,475 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:37:14,500 - root - INFO - Calculated ratio PGO signal for SOL/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:14,577 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:37:14,577 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:14,645 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:37:14,670 - root - INFO - Calculated ratio PGO signal for SOL/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:14,744 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 21:37:14,744 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:14,800 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 21:37:14,827 - root - INFO - Calculated ratio PGO signal for SOL/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:14,897 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:14,977 - root - INFO - Calculated ratio PGO signal for SUI/USDT/XRP/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:15,049 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-27 21:37:15,049 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:15,111 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-27 21:37:15,111 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:37:15,143 - root - INFO - Calculated ratio PGO signal for SUI/USDT/AAVE/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:15,217 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:15,300 - root - INFO - Calculated ratio PGO signal for SUI/USDT/AVAX/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:15,369 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:15,452 - root - INFO - Calculated ratio PGO signal for SUI/USDT/ADA/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:15,517 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:37:15,517 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:15,580 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:37:15,607 - root - INFO - Calculated ratio PGO signal for SUI/USDT/LINK/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:15,679 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:15,760 - root - INFO - Calculated ratio PGO signal for SUI/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:15,825 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:15,904 - root - INFO - Calculated ratio PGO signal for SUI/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:15,965 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:37:15,965 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:16,045 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:37:16,065 - root - INFO - Calculated ratio PGO signal for SUI/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:16,136 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:16,233 - root - INFO - Calculated ratio PGO signal for SUI/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:16,298 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:16,387 - root - INFO - Calculated ratio PGO signal for SUI/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:16,449 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:37:16,449 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:16,511 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:37:16,548 - root - INFO - Calculated ratio PGO signal for XRP/USDT/AAVE/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:16,613 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-27 21:37:16,613 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:16,685 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-27 21:37:16,708 - root - INFO - Calculated ratio PGO signal for XRP/USDT/AVAX/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:16,777 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-27 21:37:16,777 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:16,846 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-27 21:37:16,867 - root - INFO - Calculated ratio PGO signal for XRP/USDT/ADA/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:16,941 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:37:16,941 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:17,014 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:37:17,032 - root - INFO - Calculated ratio PGO signal for XRP/USDT/LINK/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:17,110 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 21:37:17,110 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:17,175 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 21:37:17,195 - root - INFO - Calculated ratio PGO signal for XRP/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:17,266 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:37:17,266 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:17,328 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:37:17,359 - root - INFO - Calculated ratio PGO signal for XRP/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:17,429 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:17,501 - root - INFO - Calculated ratio PGO signal for XRP/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:17,579 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:17,659 - root - INFO - Calculated ratio PGO signal for XRP/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:17,727 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-27 21:37:17,727 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:17,802 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-27 21:37:17,828 - root - INFO - Calculated ratio PGO signal for XRP/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:17,892 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 21:37:17,892 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:17,954 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 21:37:17,979 - root - INFO - Calculated ratio PGO signal for AAVE/USDT/AVAX/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:18,053 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:37:18,053 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:18,111 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:37:18,142 - root - INFO - Calculated ratio PGO signal for AAVE/USDT/ADA/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:18,209 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 21:37:18,209 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:18,277 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 21:37:18,300 - root - INFO - Calculated ratio PGO signal for AAVE/USDT/LINK/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:18,366 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:18,448 - root - INFO - Calculated ratio PGO signal for AAVE/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:18,521 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:18,602 - root - INFO - Calculated ratio PGO signal for AAVE/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:18,676 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-27 21:37:18,676 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:18,735 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-27 21:37:18,761 - root - INFO - Calculated ratio PGO signal for AAVE/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:18,832 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:18,919 - root - INFO - Calculated ratio PGO signal for AAVE/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:18,984 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:37:18,984 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:19,049 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:37:19,072 - root - INFO - Calculated ratio PGO signal for AAVE/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:19,143 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:19,225 - root - INFO - Calculated ratio PGO signal for AVAX/USDT/ADA/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:19,292 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-27 21:37:19,299 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:19,361 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-27 21:37:19,380 - root - INFO - Calculated ratio PGO signal for AVAX/USDT/LINK/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:19,450 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:19,538 - root - INFO - Calculated ratio PGO signal for AVAX/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:19,599 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:19,683 - root - INFO - Calculated ratio PGO signal for AVAX/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:19,753 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:19,833 - root - INFO - Calculated ratio PGO signal for AVAX/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:19,905 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:19,985 - root - INFO - Calculated ratio PGO signal for AVAX/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:20,060 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:20,137 - root - INFO - Calculated ratio PGO signal for AVAX/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:20,203 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-27 21:37:20,203 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:20,269 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-27 21:37:20,294 - root - INFO - Calculated ratio PGO signal for ADA/USDT/LINK/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:20,366 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:20,452 - root - INFO - Calculated ratio PGO signal for ADA/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:20,515 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 21:37:20,524 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:20,587 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 21:37:20,609 - root - INFO - Calculated ratio PGO signal for ADA/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:20,677 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:37:20,683 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:20,747 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:37:20,767 - root - INFO - Calculated ratio PGO signal for ADA/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:20,833 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:20,921 - root - INFO - Calculated ratio PGO signal for ADA/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:20,992 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:37:20,992 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:21,053 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:37:21,067 - root - INFO - Calculated ratio PGO signal for ADA/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:21,122 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:21,185 - root - INFO - Calculated ratio PGO signal for LINK/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:21,225 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:37:21,225 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:21,292 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:37:21,310 - root - INFO - Calculated ratio PGO signal for LINK/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:21,387 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:21,480 - root - INFO - Calculated ratio PGO signal for LINK/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:21,557 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:21,650 - root - INFO - Calculated ratio PGO signal for LINK/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:21,729 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:21,828 - root - INFO - Calculated ratio PGO signal for LINK/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:21,900 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 21:37:21,900 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:21,975 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 21:37:22,004 - root - INFO - Calculated ratio PGO signal for TRX/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:22,084 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:22,193 - root - INFO - Calculated ratio PGO signal for TRX/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:22,276 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:22,380 - root - INFO - Calculated ratio PGO signal for TRX/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:22,460 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:22,572 - root - INFO - Calculated ratio PGO signal for TRX/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:22,658 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:22,757 - root - INFO - Calculated ratio PGO signal for PEPE/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:22,833 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:22,932 - root - INFO - Calculated ratio PGO signal for PEPE/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:23,026 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:23,132 - root - INFO - Calculated ratio PGO signal for PEPE/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:23,219 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:23,312 - root - INFO - Calculated ratio PGO signal for DOGE/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:23,399 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 21:37:23,399 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:23,475 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 21:37:23,501 - root - INFO - Calculated ratio PGO signal for DOGE/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:23,595 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:37:23,679 - root - INFO - Calculated ratio PGO signal for BNB/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-27 21:37:25,286 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:37:31,362 - root - INFO - Successfully calculated daily scores using ratio-based comparisons.
2025-06-27 21:37:31,362 - root - INFO - Finished calculating daily scores. DataFrame shape: (197, 14)
2025-06-27 21:37:31,362 - root - WARNING - Running strategy with n_assets=1, equal allocation, MTPI filtering DISABLED
2025-06-27 21:37:31,384 - root - INFO - Date ranges for each asset:
2025-06-27 21:37:31,384 - root - INFO -   ETH/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:37:31,386 - root - INFO -   BTC/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:37:31,386 - root - INFO -   SOL/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:37:31,387 - root - INFO -   SUI/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:37:31,387 - root - INFO -   XRP/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:37:31,387 - root - INFO -   AAVE/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:37:31,390 - root - INFO -   AVAX/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:37:31,392 - root - INFO -   ADA/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:37:31,392 - root - INFO -   LINK/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:37:31,393 - root - INFO -   TRX/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:37:31,393 - root - INFO -   PEPE/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:37:31,395 - root - INFO -   DOGE/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:37:31,395 - root - INFO -   BNB/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:37:31,397 - root - INFO -   DOT/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:37:31,397 - root - INFO - Common dates range: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:37:31,397 - root - INFO - Analysis will run from: 2025-02-10 to 2025-06-26 (137 candles)
2025-06-27 21:37:31,410 - root - INFO - EXECUTION TIMING VERIFICATION:
2025-06-27 21:37:31,412 - root - INFO -    Execution Method: candle_close
2025-06-27 21:37:31,412 - root - INFO -    Automatic execution at 00:00 UTC (candle close)
2025-06-27 21:37:31,412 - root - INFO -    Signal generated and executed immediately
2025-06-27 21:37:31,451 - root - INFO - Including ETH/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,451 - root - INFO - Including BTC/USDT on 2025-02-10 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,451 - root - INFO - Including SOL/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,451 - root - INFO - Including SUI/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,451 - root - INFO - Including XRP/USDT on 2025-02-10 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,451 - root - INFO - Including AAVE/USDT on 2025-02-10 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,459 - root - INFO - Including AVAX/USDT on 2025-02-10 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,459 - root - INFO - Including ADA/USDT on 2025-02-10 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,461 - root - INFO - Including LINK/USDT on 2025-02-10 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,461 - root - INFO - Including TRX/USDT on 2025-02-10 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,461 - root - INFO - Including PEPE/USDT on 2025-02-10 00:00:00+00:00 with score 0.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,461 - root - INFO - Including DOGE/USDT on 2025-02-10 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,463 - root - INFO - Including BNB/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,463 - root - INFO - Including DOT/USDT on 2025-02-10 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,479 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:37:31,479 - root - INFO - ASSET CHANGE DETECTED on 2025-02-11:
2025-06-27 21:37:31,479 - root - INFO -    Signal Date: 2025-02-10 (generated at 00:00 UTC)
2025-06-27 21:37:31,479 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-11 00:00 UTC (immediate)
2025-06-27 21:37:31,479 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:37:31,487 - root - INFO -    Buying: ['TRX/USDT']
2025-06-27 21:37:31,488 - root - INFO -    TRX/USDT buy price: $0.2410 (close price)
2025-06-27 21:37:31,496 - root - INFO - Including ETH/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,496 - root - INFO - Including BTC/USDT on 2025-02-11 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,496 - root - INFO - Including SOL/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,496 - root - INFO - Including SUI/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,496 - root - INFO - Including XRP/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,496 - root - INFO - Including AAVE/USDT on 2025-02-11 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,496 - root - INFO - Including AVAX/USDT on 2025-02-11 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,496 - root - INFO - Including ADA/USDT on 2025-02-11 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,496 - root - INFO - Including LINK/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,496 - root - INFO - Including TRX/USDT on 2025-02-11 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,496 - root - INFO - Including PEPE/USDT on 2025-02-11 00:00:00+00:00 with score 0.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,496 - root - INFO - Including DOGE/USDT on 2025-02-11 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,496 - root - INFO - Including BNB/USDT on 2025-02-11 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,505 - root - INFO - Including DOT/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,525 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:31,533 - root - INFO - Including ETH/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,536 - root - INFO - Including BTC/USDT on 2025-02-12 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,536 - root - INFO - Including SOL/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,538 - root - INFO - Including SUI/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,538 - root - INFO - Including XRP/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,538 - root - INFO - Including AAVE/USDT on 2025-02-12 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,538 - root - INFO - Including AVAX/USDT on 2025-02-12 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,538 - root - INFO - Including ADA/USDT on 2025-02-12 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,538 - root - INFO - Including LINK/USDT on 2025-02-12 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,542 - root - INFO - Including TRX/USDT on 2025-02-12 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,542 - root - INFO - Including PEPE/USDT on 2025-02-12 00:00:00+00:00 with score 0.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,542 - root - INFO - Including DOGE/USDT on 2025-02-12 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,542 - root - INFO - Including BNB/USDT on 2025-02-12 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,542 - root - INFO - Including DOT/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,558 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:31,558 - root - INFO - ASSET CHANGE DETECTED on 2025-02-13:
2025-06-27 21:37:31,558 - root - INFO -    Signal Date: 2025-02-12 (generated at 00:00 UTC)
2025-06-27 21:37:31,564 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-13 00:00 UTC (immediate)
2025-06-27 21:37:31,564 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:37:31,564 - root - INFO -    Selling: ['TRX/USDT']
2025-06-27 21:37:31,564 - root - INFO -    Buying: ['BNB/USDT']
2025-06-27 21:37:31,564 - root - INFO -    BNB/USDT buy price: $664.7200 (close price)
2025-06-27 21:37:31,575 - root - INFO - Including ETH/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,577 - root - INFO - Including BTC/USDT on 2025-02-13 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,577 - root - INFO - Including SOL/USDT on 2025-02-13 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,577 - root - INFO - Including SUI/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,577 - root - INFO - Including XRP/USDT on 2025-02-13 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,579 - root - INFO - Including AAVE/USDT on 2025-02-13 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,579 - root - INFO - Including AVAX/USDT on 2025-02-13 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,579 - root - INFO - Including ADA/USDT on 2025-02-13 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,579 - root - INFO - Including LINK/USDT on 2025-02-13 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,579 - root - INFO - Including TRX/USDT on 2025-02-13 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,579 - root - INFO - Including PEPE/USDT on 2025-02-13 00:00:00+00:00 with score 0.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,579 - root - INFO - Including DOGE/USDT on 2025-02-13 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,579 - root - INFO - Including BNB/USDT on 2025-02-13 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,579 - root - INFO - Including DOT/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,596 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:31,608 - root - INFO - Including ETH/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,610 - root - INFO - Including BTC/USDT on 2025-02-14 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,610 - root - INFO - Including SOL/USDT on 2025-02-14 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,610 - root - INFO - Including SUI/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,610 - root - INFO - Including XRP/USDT on 2025-02-14 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,610 - root - INFO - Including AAVE/USDT on 2025-02-14 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,614 - root - INFO - Including AVAX/USDT on 2025-02-14 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,614 - root - INFO - Including ADA/USDT on 2025-02-14 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,614 - root - INFO - Including LINK/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,614 - root - INFO - Including TRX/USDT on 2025-02-14 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,614 - root - INFO - Including PEPE/USDT on 2025-02-14 00:00:00+00:00 with score 0.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,614 - root - INFO - Including DOGE/USDT on 2025-02-14 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,614 - root - INFO - Including BNB/USDT on 2025-02-14 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,614 - root - INFO - Including DOT/USDT on 2025-02-14 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:37:31,633 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:31,658 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:31,679 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:31,702 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:31,729 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:31,755 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:31,785 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:31,810 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:31,837 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:31,865 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:31,882 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:31,892 - root - INFO - ASSET CHANGE DETECTED on 2025-02-25:
2025-06-27 21:37:31,892 - root - INFO -    Signal Date: 2025-02-24 (generated at 00:00 UTC)
2025-06-27 21:37:31,892 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-25 00:00 UTC (immediate)
2025-06-27 21:37:31,892 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:37:31,892 - root - INFO -    Selling: ['BNB/USDT']
2025-06-27 21:37:31,892 - root - INFO -    Buying: ['TRX/USDT']
2025-06-27 21:37:31,892 - root - INFO -    TRX/USDT buy price: $0.2309 (close price)
2025-06-27 21:37:31,920 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:31,946 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:31,968 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:31,992 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,016 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,044 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,046 - root - INFO - ASSET CHANGE DETECTED on 2025-03-03:
2025-06-27 21:37:32,046 - root - INFO -    Signal Date: 2025-03-02 (generated at 00:00 UTC)
2025-06-27 21:37:32,046 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-03 00:00 UTC (immediate)
2025-06-27 21:37:32,046 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:37:32,046 - root - INFO -    Selling: ['TRX/USDT']
2025-06-27 21:37:32,046 - root - INFO -    Buying: ['ADA/USDT']
2025-06-27 21:37:32,050 - root - INFO -    ADA/USDT buy price: $0.8578 (close price)
2025-06-27 21:37:32,073 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,096 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,115 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,144 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,162 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,192 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,213 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,213 - root - INFO - ASSET CHANGE DETECTED on 2025-03-10:
2025-06-27 21:37:32,213 - root - INFO -    Signal Date: 2025-03-09 (generated at 00:00 UTC)
2025-06-27 21:37:32,213 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-10 00:00 UTC (immediate)
2025-06-27 21:37:32,220 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:37:32,221 - root - INFO -    Selling: ['ADA/USDT']
2025-06-27 21:37:32,221 - root - INFO -    Buying: ['TRX/USDT']
2025-06-27 21:37:32,221 - root - INFO -    TRX/USDT buy price: $0.2292 (close price)
2025-06-27 21:37:32,246 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,273 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,299 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,322 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,349 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,375 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,377 - root - INFO - ASSET CHANGE DETECTED on 2025-03-16:
2025-06-27 21:37:32,377 - root - INFO -    Signal Date: 2025-03-15 (generated at 00:00 UTC)
2025-06-27 21:37:32,377 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-16 00:00 UTC (immediate)
2025-06-27 21:37:32,377 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:37:32,377 - root - INFO -    Selling: ['TRX/USDT']
2025-06-27 21:37:32,379 - root - INFO -    Buying: ['ADA/USDT']
2025-06-27 21:37:32,379 - root - INFO -    ADA/USDT buy price: $0.7049 (close price)
2025-06-27 21:37:32,396 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,405 - root - INFO - ASSET CHANGE DETECTED on 2025-03-17:
2025-06-27 21:37:32,405 - root - INFO -    Signal Date: 2025-03-16 (generated at 00:00 UTC)
2025-06-27 21:37:32,405 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-17 00:00 UTC (immediate)
2025-06-27 21:37:32,408 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:37:32,408 - root - INFO -    Selling: ['ADA/USDT']
2025-06-27 21:37:32,408 - root - INFO -    Buying: ['BNB/USDT']
2025-06-27 21:37:32,408 - root - INFO -    BNB/USDT buy price: $631.6900 (close price)
2025-06-27 21:37:32,435 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,458 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,482 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,510 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,544 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,569 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,592 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,621 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,648 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,675 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,677 - root - INFO - ASSET CHANGE DETECTED on 2025-03-27:
2025-06-27 21:37:32,677 - root - INFO -    Signal Date: 2025-03-26 (generated at 00:00 UTC)
2025-06-27 21:37:32,679 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-27 00:00 UTC (immediate)
2025-06-27 21:37:32,680 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:37:32,683 - root - INFO -    Selling: ['BNB/USDT']
2025-06-27 21:37:32,683 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-27 21:37:32,683 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-27 21:37:32,710 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,735 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,762 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,783 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,783 - root - INFO - ASSET CHANGE DETECTED on 2025-03-31:
2025-06-27 21:37:32,783 - root - INFO -    Signal Date: 2025-03-30 (generated at 00:00 UTC)
2025-06-27 21:37:32,792 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-31 00:00 UTC (immediate)
2025-06-27 21:37:32,792 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:37:32,792 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-27 21:37:32,792 - root - INFO -    Buying: ['BNB/USDT']
2025-06-27 21:37:32,794 - root - INFO -    BNB/USDT buy price: $604.8100 (close price)
2025-06-27 21:37:32,812 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,847 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,875 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,900 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,900 - root - INFO - ASSET CHANGE DETECTED on 2025-04-04:
2025-06-27 21:37:32,900 - root - INFO -    Signal Date: 2025-04-03 (generated at 00:00 UTC)
2025-06-27 21:37:32,900 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-04 00:00 UTC (immediate)
2025-06-27 21:37:32,900 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:37:32,900 - root - INFO -    Selling: ['BNB/USDT']
2025-06-27 21:37:32,900 - root - INFO -    Buying: ['TRX/USDT']
2025-06-27 21:37:32,900 - root - INFO -    TRX/USDT buy price: $0.2390 (close price)
2025-06-27 21:37:32,930 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,959 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:32,987 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,016 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,044 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,071 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,095 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,120 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,145 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,168 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,192 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,216 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,246 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,275 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,300 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,329 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,331 - root - INFO - ASSET CHANGE DETECTED on 2025-04-20:
2025-06-27 21:37:33,332 - root - INFO -    Signal Date: 2025-04-19 (generated at 00:00 UTC)
2025-06-27 21:37:33,332 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-20 00:00 UTC (immediate)
2025-06-27 21:37:33,334 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:37:33,334 - root - INFO -    Selling: ['TRX/USDT']
2025-06-27 21:37:33,334 - root - INFO -    Buying: ['SOL/USDT']
2025-06-27 21:37:33,334 - root - INFO -    SOL/USDT buy price: $137.8600 (close price)
2025-06-27 21:37:33,359 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,382 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,406 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,406 - root - INFO - ASSET CHANGE DETECTED on 2025-04-23:
2025-06-27 21:37:33,410 - root - INFO -    Signal Date: 2025-04-22 (generated at 00:00 UTC)
2025-06-27 21:37:33,410 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-23 00:00 UTC (immediate)
2025-06-27 21:37:33,412 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:37:33,412 - root - INFO -    Selling: ['SOL/USDT']
2025-06-27 21:37:33,412 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-27 21:37:33,412 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-27 21:37:33,431 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,439 - root - INFO - ASSET CHANGE DETECTED on 2025-04-24:
2025-06-27 21:37:33,439 - root - INFO -    Signal Date: 2025-04-23 (generated at 00:00 UTC)
2025-06-27 21:37:33,439 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-24 00:00 UTC (immediate)
2025-06-27 21:37:33,439 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:37:33,439 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-27 21:37:33,439 - root - INFO -    Buying: ['SUI/USDT']
2025-06-27 21:37:33,443 - root - INFO -    SUI/USDT buy price: $3.3437 (close price)
2025-06-27 21:37:33,463 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,492 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,519 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,543 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,574 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,596 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,619 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,644 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,675 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,702 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,724 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,746 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,775 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,798 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,825 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,854 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,854 - root - INFO - ASSET CHANGE DETECTED on 2025-05-10:
2025-06-27 21:37:33,854 - root - INFO -    Signal Date: 2025-05-09 (generated at 00:00 UTC)
2025-06-27 21:37:33,854 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-10 00:00 UTC (immediate)
2025-06-27 21:37:33,857 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:37:33,857 - root - INFO -    Selling: ['SUI/USDT']
2025-06-27 21:37:33,857 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-27 21:37:33,857 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-27 21:37:33,879 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,905 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,930 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,949 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:33,983 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,001 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,034 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,060 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,085 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,108 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,133 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,133 - root - INFO - ASSET CHANGE DETECTED on 2025-05-21:
2025-06-27 21:37:34,133 - root - INFO -    Signal Date: 2025-05-20 (generated at 00:00 UTC)
2025-06-27 21:37:34,133 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-21 00:00 UTC (immediate)
2025-06-27 21:37:34,133 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:37:34,133 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-27 21:37:34,133 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-27 21:37:34,142 - root - INFO -    AAVE/USDT buy price: $247.5400 (close price)
2025-06-27 21:37:34,163 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,163 - root - INFO - ASSET CHANGE DETECTED on 2025-05-22:
2025-06-27 21:37:34,163 - root - INFO -    Signal Date: 2025-05-21 (generated at 00:00 UTC)
2025-06-27 21:37:34,163 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-22 00:00 UTC (immediate)
2025-06-27 21:37:34,163 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:37:34,163 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-27 21:37:34,170 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-27 21:37:34,170 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-27 21:37:34,196 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,219 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,246 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,273 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,273 - root - INFO - ASSET CHANGE DETECTED on 2025-05-26:
2025-06-27 21:37:34,276 - root - INFO -    Signal Date: 2025-05-25 (generated at 00:00 UTC)
2025-06-27 21:37:34,276 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-26 00:00 UTC (immediate)
2025-06-27 21:37:34,276 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:37:34,276 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-27 21:37:34,276 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-27 21:37:34,276 - root - INFO -    AAVE/USDT buy price: $267.5200 (close price)
2025-06-27 21:37:34,304 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,329 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,352 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,382 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,400 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,432 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,454 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,478 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,498 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,527 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,553 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,584 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,608 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,643 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,669 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,690 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,715 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,748 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,770 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,784 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,816 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,838 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,883 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,909 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,938 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,980 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:34,980 - root - INFO - ASSET CHANGE DETECTED on 2025-06-21:
2025-06-27 21:37:34,980 - root - INFO -    Signal Date: 2025-06-20 (generated at 00:00 UTC)
2025-06-27 21:37:34,980 - root - INFO -    AUTOMATIC EXECUTION: 2025-06-21 00:00 UTC (immediate)
2025-06-27 21:37:34,980 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:37:34,980 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-27 21:37:34,980 - root - INFO -    Buying: ['TRX/USDT']
2025-06-27 21:37:34,980 - root - INFO -    TRX/USDT buy price: $0.2710 (close price)
2025-06-27 21:37:35,019 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:35,045 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:35,075 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:35,092 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:35,115 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:37:35,313 - root - INFO - Entry trade at 2025-02-11 00:00:00+00:00: TRX/USDT
2025-06-27 21:37:35,313 - root - INFO - Swap trade at 2025-02-13 00:00:00+00:00: TRX/USDT -> BNB/USDT
2025-06-27 21:37:35,313 - root - INFO - Swap trade at 2025-02-25 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-27 21:37:35,313 - root - INFO - Swap trade at 2025-03-03 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-06-27 21:37:35,313 - root - INFO - Swap trade at 2025-03-10 00:00:00+00:00: ADA/USDT -> TRX/USDT
2025-06-27 21:37:35,313 - root - INFO - Swap trade at 2025-03-16 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-06-27 21:37:35,313 - root - INFO - Swap trade at 2025-03-17 00:00:00+00:00: ADA/USDT -> BNB/USDT
2025-06-27 21:37:35,313 - root - INFO - Swap trade at 2025-03-27 00:00:00+00:00: BNB/USDT -> PEPE/USDT
2025-06-27 21:37:35,319 - root - INFO - Swap trade at 2025-03-31 00:00:00+00:00: PEPE/USDT -> BNB/USDT
2025-06-27 21:37:35,319 - root - INFO - Swap trade at 2025-04-04 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-27 21:37:35,319 - root - INFO - Swap trade at 2025-04-20 00:00:00+00:00: TRX/USDT -> SOL/USDT
2025-06-27 21:37:35,319 - root - INFO - Swap trade at 2025-04-23 00:00:00+00:00: SOL/USDT -> PEPE/USDT
2025-06-27 21:37:35,319 - root - INFO - Swap trade at 2025-04-24 00:00:00+00:00: PEPE/USDT -> SUI/USDT
2025-06-27 21:37:35,319 - root - INFO - Swap trade at 2025-05-10 00:00:00+00:00: SUI/USDT -> PEPE/USDT
2025-06-27 21:37:35,325 - root - INFO - Swap trade at 2025-05-21 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-27 21:37:35,325 - root - INFO - Swap trade at 2025-05-22 00:00:00+00:00: AAVE/USDT -> PEPE/USDT
2025-06-27 21:37:35,325 - root - INFO - Swap trade at 2025-05-26 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-27 21:37:35,327 - root - INFO - Swap trade at 2025-06-21 00:00:00+00:00: AAVE/USDT -> TRX/USDT
2025-06-27 21:37:35,327 - root - INFO - Total trades: 18 (Entries: 1, Exits: 0, Swaps: 17)
2025-06-27 21:37:35,332 - root - INFO - Strategy execution completed in 3s
2025-06-27 21:37:35,332 - root - INFO - DEBUG: self.elapsed_time = 3.969233751296997 seconds
2025-06-27 21:37:35,372 - root - INFO - Saved allocation history to allocation_history_1d_1d_no_mtpi_no_rebal_manual_inversion_imcumbent_2025-02-10.csv
2025-06-27 21:37:35,374 - root - INFO - Strategy equity curve starts at: 2025-02-10
2025-06-27 21:37:35,374 - root - INFO - Assets included in buy-and-hold comparison:
2025-06-27 21:37:35,376 - root - INFO -   ETH/USDT: First available date 2024-12-12
2025-06-27 21:37:35,376 - root - INFO -   BTC/USDT: First available date 2024-12-12
2025-06-27 21:37:35,378 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:37:35,378 - root - INFO -   SOL/USDT: First available date 2024-12-12
2025-06-27 21:37:35,382 - root - INFO -   SUI/USDT: First available date 2024-12-12
2025-06-27 21:37:35,382 - root - INFO -   XRP/USDT: First available date 2024-12-12
2025-06-27 21:37:35,382 - root - INFO -   AAVE/USDT: First available date 2024-12-12
2025-06-27 21:37:35,382 - root - INFO -   AVAX/USDT: First available date 2024-12-12
2025-06-27 21:37:35,382 - root - INFO -   ADA/USDT: First available date 2024-12-12
2025-06-27 21:37:35,389 - root - INFO -   LINK/USDT: First available date 2024-12-12
2025-06-27 21:37:35,390 - root - INFO -   TRX/USDT: First available date 2024-12-12
2025-06-27 21:37:35,391 - root - INFO -   PEPE/USDT: First available date 2024-12-12
2025-06-27 21:37:35,392 - root - INFO -   DOGE/USDT: First available date 2024-12-12
2025-06-27 21:37:35,393 - root - INFO -   BNB/USDT: First available date 2024-12-12
2025-06-27 21:37:35,393 - root - INFO -   DOT/USDT: First available date 2024-12-12
2025-06-27 21:37:35,395 - root - INFO - Using provided start date for normalization: 2025-02-10 00:00:00+00:00
2025-06-27 21:37:35,398 - root - INFO - Normalized ETH/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:37:35,398 - root - INFO - Normalized BTC/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:37:35,410 - root - INFO - Normalized SOL/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:37:35,417 - root - INFO - Normalized SUI/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:37:35,418 - root - INFO - Normalized XRP/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:37:35,426 - root - INFO - Normalized AAVE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:37:35,426 - root - INFO - Normalized AVAX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:37:35,435 - root - INFO - Normalized ADA/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:37:35,445 - root - INFO - Normalized LINK/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:37:35,451 - root - INFO - Normalized TRX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:37:35,458 - root - INFO - Normalized PEPE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:37:35,459 - root - INFO - Normalized DOGE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:37:35,466 - root - INFO - Normalized BNB/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:37:35,475 - root - INFO - Normalized DOT/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:37:35,477 - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 197 points
2025-06-27 21:37:35,485 - root - INFO - ETH/USDT B&H total return: -9.22%
2025-06-27 21:37:35,494 - root - INFO - Added equity_curve to bnh_metrics for BTC/USDT with 197 points
2025-06-27 21:37:35,496 - root - INFO - BTC/USDT B&H total return: 9.77%
2025-06-27 21:37:35,502 - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 197 points
2025-06-27 21:37:35,504 - root - INFO - SOL/USDT B&H total return: -30.63%
2025-06-27 21:37:35,510 - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 197 points
2025-06-27 21:37:35,512 - root - INFO - SUI/USDT B&H total return: -19.11%
2025-06-27 21:37:35,518 - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 197 points
2025-06-27 21:37:35,518 - root - INFO - XRP/USDT B&H total return: -13.13%
2025-06-27 21:37:35,527 - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 197 points
2025-06-27 21:37:35,529 - root - INFO - AAVE/USDT B&H total return: -0.91%
2025-06-27 21:37:35,535 - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 197 points
2025-06-27 21:37:35,535 - root - INFO - AVAX/USDT B&H total return: -32.88%
2025-06-27 21:37:35,543 - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 197 points
2025-06-27 21:37:35,543 - root - INFO - ADA/USDT B&H total return: -22.16%
2025-06-27 21:37:35,551 - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 197 points
2025-06-27 21:37:35,551 - root - INFO - LINK/USDT B&H total return: -31.21%
2025-06-27 21:37:35,562 - root - INFO - Added equity_curve to bnh_metrics for TRX/USDT with 197 points
2025-06-27 21:37:35,562 - root - INFO - TRX/USDT B&H total return: 10.07%
2025-06-27 21:37:35,568 - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 197 points
2025-06-27 21:37:35,568 - root - INFO - PEPE/USDT B&H total return: -4.48%
2025-06-27 21:37:35,580 - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 197 points
2025-06-27 21:37:35,580 - root - INFO - DOGE/USDT B&H total return: -37.35%
2025-06-27 21:37:35,588 - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 197 points
2025-06-27 21:37:35,588 - root - INFO - BNB/USDT B&H total return: 3.82%
2025-06-27 21:37:35,592 - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 197 points
2025-06-27 21:37:35,597 - root - INFO - DOT/USDT B&H total return: -31.91%
2025-06-27 21:37:35,598 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-27 21:37:35,633 - root - INFO - Configuration loaded successfully.
2025-06-27 21:37:35,685 - root - INFO - Using colored segments for single-asset strategy visualization
2025-06-27 21:37:36,072 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-27 21:37:36,096 - root - INFO - Configuration loaded successfully.
2025-06-27 21:37:40,660 - root - INFO - Added ETH/USDT buy-and-hold curve with 197 points
2025-06-27 21:37:40,660 - root - INFO - Added BTC/USDT buy-and-hold curve with 197 points
2025-06-27 21:37:40,668 - root - INFO - Added SOL/USDT buy-and-hold curve with 197 points
2025-06-27 21:37:40,668 - root - INFO - Added SUI/USDT buy-and-hold curve with 197 points
2025-06-27 21:37:40,668 - root - INFO - Added XRP/USDT buy-and-hold curve with 197 points
2025-06-27 21:37:40,668 - root - INFO - Added AAVE/USDT buy-and-hold curve with 197 points
2025-06-27 21:37:40,668 - root - INFO - Added AVAX/USDT buy-and-hold curve with 197 points
2025-06-27 21:37:40,668 - root - INFO - Added ADA/USDT buy-and-hold curve with 197 points
2025-06-27 21:37:40,668 - root - INFO - Added LINK/USDT buy-and-hold curve with 197 points
2025-06-27 21:37:40,668 - root - INFO - Added TRX/USDT buy-and-hold curve with 197 points
2025-06-27 21:37:40,668 - root - INFO - Added PEPE/USDT buy-and-hold curve with 197 points
2025-06-27 21:37:40,668 - root - INFO - Added DOGE/USDT buy-and-hold curve with 197 points
2025-06-27 21:37:40,668 - root - INFO - Added BNB/USDT buy-and-hold curve with 197 points
2025-06-27 21:37:40,668 - root - INFO - Added DOT/USDT buy-and-hold curve with 197 points
2025-06-27 21:37:40,668 - root - INFO - Added 14 buy-and-hold curves to results
2025-06-27 21:37:40,668 - root - INFO -   - ETH/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:37:40,668 - root - INFO -   - BTC/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:37:40,668 - root - INFO -   - SOL/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:37:40,668 - root - INFO -   - SUI/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:37:40,668 - root - INFO -   - XRP/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:37:40,668 - root - INFO -   - AAVE/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:37:40,668 - root - INFO -   - AVAX/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:37:40,668 - root - INFO -   - ADA/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:37:40,668 - root - INFO -   - LINK/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:37:40,675 - root - INFO -   - TRX/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:37:40,675 - root - INFO -   - PEPE/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:37:40,675 - root - INFO -   - DOGE/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:37:40,675 - root - INFO -   - BNB/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:37:40,675 - root - INFO -   - DOT/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:37:40,687 - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-06-27 21:37:40,687 - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-27 21:37:40,693 - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-06-27 21:37:40,693 - root - INFO - MTPI disabled - skipping MTPI signal calculation
2025-06-27 21:37:40,695 - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-06-27 21:37:40,695 - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 8.0, 'BTC/USDT': 13.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-27 21:37:40,695 - root - ERROR - [DEBUG] CONVERSION - Original USDT scores: {'ETH/USDT': 8.0, 'BTC/USDT': 13.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-27 21:37:40,695 - root - ERROR - [DEBUG] CONVERSION - Original USDT keys order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-27 21:37:40,695 - root - ERROR - [DEBUG] CONVERSION - Conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-27 21:37:40,695 - root - ERROR - [DEBUG] CONVERSION - ETH/USDT -> ETH/EUR (score: 8.0)
2025-06-27 21:37:40,695 - root - ERROR - [DEBUG] CONVERSION - BTC/USDT -> BTC/EUR (score: 13.0)
2025-06-27 21:37:40,695 - root - ERROR - [DEBUG] CONVERSION - SOL/USDT -> SOL/EUR (score: 6.0)
2025-06-27 21:37:40,695 - root - ERROR - [DEBUG] CONVERSION - SUI/USDT -> SUI/EUR (score: 2.0)
2025-06-27 21:37:40,695 - root - ERROR - [DEBUG] CONVERSION - XRP/USDT -> XRP/EUR (score: 9.0)
2025-06-27 21:37:40,695 - root - ERROR - [DEBUG] CONVERSION - AAVE/USDT -> AAVE/EUR (score: 10.0)
2025-06-27 21:37:40,695 - root - ERROR - [DEBUG] CONVERSION - AVAX/USDT -> AVAX/EUR (score: 3.0)
2025-06-27 21:37:40,695 - root - ERROR - [DEBUG] CONVERSION - ADA/USDT -> ADA/EUR (score: 4.0)
2025-06-27 21:37:40,695 - root - ERROR - [DEBUG] CONVERSION - LINK/USDT -> LINK/EUR (score: 7.0)
2025-06-27 21:37:40,695 - root - ERROR - [DEBUG] CONVERSION - TRX/USDT -> TRX/EUR (score: 12.0)
2025-06-27 21:37:40,695 - root - ERROR - [DEBUG] CONVERSION - PEPE/USDT -> PEPE/EUR (score: 0.0)
2025-06-27 21:37:40,695 - root - ERROR - [DEBUG] CONVERSION - DOGE/USDT -> DOGE/EUR (score: 5.0)
2025-06-27 21:37:40,695 - root - ERROR - [DEBUG] CONVERSION - BNB/USDT -> BNB/EUR (score: 11.0)
2025-06-27 21:37:40,695 - root - ERROR - [DEBUG] CONVERSION - DOT/USDT -> DOT/EUR (score: 1.0)
2025-06-27 21:37:40,695 - root - ERROR - [DEBUG] CONVERSION - Final EUR scores: {'ETH/EUR': 8.0, 'BTC/EUR': 13.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 4.0, 'LINK/EUR': 7.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-27 21:37:40,695 - root - ERROR - [DEBUG] CONVERSION - Final EUR keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-27 21:37:40,695 - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 8.0, 'BTC/EUR': 13.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 4.0, 'LINK/EUR': 7.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-27 21:37:40,708 - root - INFO - Saved metrics to new file: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250620_run_********_213655.csv
2025-06-27 21:37:40,708 - root - INFO - Saved performance metrics to CSV: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250620_run_********_213655.csv
2025-06-27 21:37:40,713 - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-06-27 21:37:40,713 - root - INFO - Results type: <class 'dict'>
2025-06-27 21:37:40,713 - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-06-27 21:37:40,713 - root - INFO - Success flag set to: True
2025-06-27 21:37:40,713 - root - INFO - Message set to: Strategy calculation completed successfully
2025-06-27 21:37:40,713 - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 197 entries
2025-06-27 21:37:40,713 - root - INFO -   - buy_hold_curves: dict with 14 entries
2025-06-27 21:37:40,713 - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 197 entries
2025-06-27 21:37:40,713 - root - INFO -   - mtpi_signals: <class 'NoneType'>
2025-06-27 21:37:40,713 - root - INFO -   - mtpi_score: <class 'NoneType'>
2025-06-27 21:37:40,713 - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 197 entries
2025-06-27 21:37:40,713 - root - INFO -   - performance_metrics: dict with 3 entries
2025-06-27 21:37:40,713 - root - INFO -   - metrics_file: <class 'str'>
2025-06-27 21:37:40,713 - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-06-27 21:37:40,713 - root - INFO -   - success: <class 'bool'>
2025-06-27 21:37:40,713 - root - INFO -   - message: <class 'str'>
2025-06-27 21:37:40,713 - root - INFO - MTPI disabled - using default bullish signal (1) for trading execution
2025-06-27 21:37:40,713 - root - ERROR - [DEBUG] STRATEGY RESULTS - Available keys: ['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message']
2025-06-27 21:37:40,713 - root - INFO - [DEBUG] ASSET SELECTION - Extracted best_asset from best_asset_series: TRX/EUR
2025-06-27 21:37:40,713 - root - INFO - [DEBUG] ASSET SELECTION - Last 5 entries in best_asset_series: 2025-06-22 00:00:00+00:00    TRX/EUR
2025-06-23 00:00:00+00:00    TRX/EUR
2025-06-24 00:00:00+00:00    TRX/EUR
2025-06-25 00:00:00+00:00    TRX/EUR
2025-06-26 00:00:00+00:00    TRX/EUR
dtype: object
2025-06-27 21:37:40,713 - root - ERROR - [DEBUG] ASSET SELECTION - Latest scores available: {'ETH/EUR': 8.0, 'BTC/EUR': 13.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 4.0, 'LINK/EUR': 7.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-27 21:37:40,713 - root - ERROR - [DEBUG] ASSET SELECTION - Scores dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-27 21:37:40,713 - root - INFO - [DEBUG] TIE-BREAKING - Strategy: imcumbent
2025-06-27 21:37:40,713 - root - WARNING - [DEBUG] ASSET SELECTION - find_best_asset_for_day() called with 14 assets
2025-06-27 21:37:40,713 - root - WARNING - [DEBUG] ASSET SELECTION - Input scores: {'ETH/EUR': 8.0, 'BTC/EUR': 13.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 4.0, 'LINK/EUR': 7.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-27 21:37:40,713 - root - WARNING - [DEBUG] ASSET SELECTION - Dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-27 21:37:40,713 - root - WARNING - [DEBUG] ASSET SELECTION - Maximum score found: 13.0
2025-06-27 21:37:40,713 - root - WARNING - [DEBUG] ASSET SELECTION - Assets with max score 13.0: ['BTC/EUR']
2025-06-27 21:37:40,713 - root - WARNING - [DEBUG] ASSET SELECTION - No tie detected, single winner: BTC/EUR
2025-06-27 21:37:40,713 - root - ERROR - [DEBUG] SELECTED BEST ASSET: BTC/EUR (score: 13.0)
2025-06-27 21:37:40,713 - root - ERROR - [DEBUG] ASSET SELECTION - Current day's best asset: BTC/EUR (MTPI signal: 1)
2025-06-27 21:37:40,713 - root - ERROR - [DEBUG] ASSET SELECTION - Maximum score: 13.0
2025-06-27 21:37:40,723 - root - ERROR - [DEBUG] ASSET SELECTION - Assets with max score: ['BTC/EUR']
2025-06-27 21:37:40,723 - root - ERROR - [DEBUG] NO TIE - UPDATING SELECTION: TRX/EUR -> BTC/EUR
2025-06-27 21:37:40,723 - root - ERROR - [DEBUG] NO TIE - Single winner: BTC/EUR
2025-06-27 21:37:40,746 - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
       'BNB/EUR', 'DOT/EUR'],
      dtype='object')
2025-06-27 21:37:40,746 - root - INFO - Last row columns: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-27 21:37:40,746 - root - INFO - Single asset strategy with best asset: BTC/EUR
2025-06-27 21:37:40,752 - root - INFO - [DEBUG] FINAL ASSET SELECTION SUMMARY:
2025-06-27 21:37:40,752 - root - INFO - [DEBUG]   - Best asset selected: BTC/EUR
2025-06-27 21:37:40,752 - root - INFO - [DEBUG]   - Assets held: {'BTC/EUR': 1.0}
2025-06-27 21:37:40,752 - root - INFO - [DEBUG]   - MTPI signal: 1
2025-06-27 21:37:40,752 - root - INFO - [DEBUG]   - Use MTPI signal: False
2025-06-27 21:37:40,752 - root - INFO - Executing single-asset strategy with best asset: BTC/EUR
2025-06-27 21:37:40,752 - root - INFO - Executing strategy signal: best_asset=BTC/EUR, mtpi_signal=1, mode=paper
2025-06-27 21:37:40,752 - root - INFO - Incremented daily trade counter for BTC/EUR: 1/5
2025-06-27 21:37:40,752 - root - INFO - ASSET SELECTION - Attempting to enter position for selected asset: BTC/EUR
2025-06-27 21:37:40,752 - root - INFO - Attempting to enter position for BTC/EUR in paper mode
2025-06-27 21:37:40,752 - root - INFO - [DEBUG] TRADE - BTC/EUR: Starting enter_position attempt
2025-06-27 21:37:40,752 - root - INFO - [DEBUG] TRADE - BTC/EUR: Trading mode: paper
2025-06-27 21:37:40,752 - root - INFO - TRADE ATTEMPT - BTC/EUR: Getting current market price...
2025-06-27 21:37:40,752 - root - INFO - [DEBUG] PRICE - BTC/EUR: Starting get_current_price
2025-06-27 21:37:40,752 - root - INFO - [DEBUG] PRICE - BTC/EUR: Exchange ID: bitvavo
2025-06-27 21:37:40,752 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Initializing exchange bitvavo
2025-06-27 21:37:40,760 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Exchange initialized successfully
2025-06-27 21:37:40,760 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Exchange markets not loaded, loading now...
2025-06-27 21:37:41,043 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Symbol found after loading markets
2025-06-27 21:37:41,043 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Attempting to fetch ticker...
2025-06-27 21:37:41,083 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Ticker fetched successfully
2025-06-27 21:37:41,083 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Ticker data: {'symbol': 'BTC/EUR', 'timestamp': 1751053057280, 'datetime': '2025-06-27T19:37:37.280Z', 'high': 92535.0, 'low': 90764.0, 'bid': 91261.0, 'bidVolume': 0.00468232, 'ask': 91271.0, 'askVolume': 0.04745786, 'vwap': 91499.92976540209, 'open': 91900.0, 'close': 91267.0, 'last': 91267.0, 'previousClose': None, 'change': -633.0, 'percentage': -0.6887921653971708, 'average': 91583.5, 'baseVolume': 314.95640392, 'quoteVolume': 28818488.83784361, 'info': {'market': 'BTC-EUR', 'startTimestamp': 1750966657280, 'timestamp': 1751053057280, 'open': '9.19E+4', 'openTimestamp': 1750966686601, 'high': '92535', 'low': '90764', 'last': '91267', 'closeTimestamp': 1751053035501, 'bid': '91261', 'bidSize': '0.00468232', 'ask': '91271', 'askSize': '0.04745786', 'volume': '314.95640392', 'volumeQuote': '28818488.83784361'}, 'indexPrice': None, 'markPrice': None}
2025-06-27 21:37:41,083 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Last price: 91267.0
2025-06-27 21:37:41,083 - root - INFO - [DEBUG] TRADE - BTC/EUR: get_current_price returned: 91267.0
2025-06-27 21:37:41,083 - root - INFO - [DEBUG] TRADE - BTC/EUR: Price type: <class 'float'>
2025-06-27 21:37:41,083 - root - INFO - [DEBUG] TRADE - BTC/EUR: Price evaluation - not price: False
2025-06-27 21:37:41,083 - root - INFO - [DEBUG] TRADE - BTC/EUR: Price evaluation - price <= 0: False
2025-06-27 21:37:41,083 - root - INFO - TRADE ATTEMPT - BTC/EUR: Current price: 91267.********
2025-06-27 21:37:41,083 - root - INFO - Available balance for EUR: 100.********
2025-06-27 21:37:41,088 - root - INFO - Loaded market info for 176 trading pairs
2025-06-27 21:37:41,088 - root - INFO - Calculated position size for BTC/EUR: 0.******** (using 10% of 100, accounting for fees)
2025-06-27 21:37:41,088 - root - INFO - Calculated position size: 0.******** BTC
2025-06-27 21:37:41,088 - root - INFO - Entering position for BTC/EUR: 0.******** units at 91267.******** (value: 9.******** EUR)
2025-06-27 21:37:41,088 - root - INFO - Executing paper market buy order for BTC/EUR
2025-06-27 21:37:41,091 - root - INFO - Paper trading buy for BTC/EUR: original=0.********, adjusted=0.********, after_fee=0.********
2025-06-27 21:37:41,091 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-27 21:37:41,091 - root - INFO - Created paper market buy order: BTC/EUR, amount: 0.00010836720556170358, price: 91267.0
2025-06-27 21:37:41,094 - root - INFO - Filled amount: 0.******** BTC
2025-06-27 21:37:41,094 - root - INFO - Order fee: 0.******** EUR
2025-06-27 21:37:41,094 - root - INFO - Successfully entered position: BTC/EUR, amount: 0.********, price: 91267.********
2025-06-27 21:37:41,096 - root - INFO - Trade executed: BUY BTC/EUR, amount=0.********, price=91267.********, filled=0.********
2025-06-27 21:37:41,096 - root - INFO -   Fee: 0.******** EUR
2025-06-27 21:37:41,096 - root - INFO - TRADE SUCCESS - BTC/EUR: Successfully updated current asset
2025-06-27 21:37:41,099 - root - INFO - Trade executed: BUY BTC/EUR, amount=0.********, price=91267.********, filled=0.********
2025-06-27 21:37:41,099 - root - INFO -   Fee: 0.******** EUR
2025-06-27 21:37:41,099 - root - INFO - Single-asset trade result logged to trade log file
2025-06-27 21:37:41,099 - root - INFO - Trade executed: {'success': True, 'symbol': 'BTC/EUR', 'side': 'buy', 'amount': 0.********078516879047, 'price': 91267.0, 'order': {'id': 'paper-1751053061-BTC/EUR-buy-0.00010836720556170358', 'symbol': 'BTC/EUR', 'side': 'buy', 'type': 'market', 'amount': 0.00010836720556170358, 'price': 91267.0, 'cost': 9.90025, 'fee': {'cost': 0.********, 'currency': 'EUR', 'rate': 0.001}, 'status': 'closed', 'filled': 0.00010836720556170358, 'remaining': 0, 'timestamp': 1751053061091, 'datetime': '2025-06-27T21:37:41.091943', 'trades': [], 'average': 91267.0, 'average_price': 91267.0}, 'filled_amount': 0.00010836720556170358, 'fee': {'cost': 0.********, 'currency': 'EUR', 'rate': 0.001}, 'quote_currency': 'EUR', 'base_currency': 'BTC', 'timestamp': '2025-06-27T21:37:41.094254'}
2025-06-27 21:37:41,330 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-27 21:37:41,330 - root - INFO - Asset selection logged: 1 assets selected with single_asset allocation
2025-06-27 21:37:41,330 - root - INFO - Asset scores (sorted by score):
2025-06-27 21:37:41,330 - root - INFO -   BTC/EUR: score=13.0, status=SELECTED, weight=1.00
2025-06-27 21:37:41,330 - root - INFO -   TRX/EUR: score=12.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:37:41,330 - root - INFO -   BNB/EUR: score=11.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:37:41,338 - root - INFO -   AAVE/EUR: score=10.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:37:41,338 - root - INFO -   XRP/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:37:41,338 - root - INFO -   ETH/EUR: score=8.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:37:41,338 - root - INFO -   LINK/EUR: score=7.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:37:41,338 - root - INFO -   SOL/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:37:41,338 - root - INFO -   DOGE/EUR: score=5.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:37:41,338 - root - INFO -   ADA/EUR: score=4.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:37:41,338 - root - INFO -   AVAX/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:37:41,338 - root - INFO -   SUI/EUR: score=2.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:37:41,338 - root - INFO -   DOT/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:37:41,338 - root - INFO -   PEPE/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:37:41,338 - root - INFO - Asset selection logged with 14 assets scored and 1 assets selected
2025-06-27 21:37:41,338 - root - INFO - MTPI disabled - skipping MTPI score extraction
2025-06-27 21:37:41,338 - root - INFO - Extracted asset scores: {'ETH/EUR': 8.0, 'BTC/EUR': 13.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 4.0, 'LINK/EUR': 7.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-27 21:37:41,626 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-27 21:37:41,628 - root - INFO - Strategy execution completed successfully in 45.85 seconds
2025-06-27 21:37:41,628 - root - INFO - Saved recovery state to data/state\recovery_state.json
2025-06-27 21:37:45,512 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:37:55,548 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:38:05,582 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:38:15,623 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:38:25,655 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:38:35,691 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:38:45,725 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:38:55,758 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:39:05,787 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:39:15,831 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:39:25,859 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:39:36,000 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:39:46,024 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:39:56,070 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:40:06,110 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:40:10,325 - root - INFO - Received signal 2, shutting down...
2025-06-27 21:40:13,758 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 409 Conflict"
2025-06-27 21:40:13,758 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_baserequest.py", line 355, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-06-27 21:40:15,490 - root - INFO - Network watchdog stopped
2025-06-27 21:40:15,490 - root - INFO - Network watchdog stopped
2025-06-27 21:40:15,491 - root - INFO - Background service stopped
2025-06-27 21:40:15,644 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
