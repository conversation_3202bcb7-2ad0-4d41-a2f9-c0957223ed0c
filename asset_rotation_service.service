[Unit]
Description=Asset Rotation Strategy Background Service
After=network.target

[Service]
Type=simple
User=admin
WorkingDirectory=/home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy
ExecStart=/home/<USER>/asset_rotation_screener/venv/bin/python /home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/background_service.py
Restart=always
RestartSec=5s
StandardOutput=append:/var/log/asset-rotation.log
StandardError=append:/var/log/asset-rotation-error.log

[Install]
WantedBy=multi-user.target
