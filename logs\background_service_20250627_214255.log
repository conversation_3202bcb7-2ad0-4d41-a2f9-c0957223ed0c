2025-06-27 21:42:55,397 - root - INFO - Loaded environment variables from .env file
2025-06-27 21:42:56,414 - root - INFO - Loaded 8 trade records from logs/trades\trade_log_********.json
2025-06-27 21:42:56,414 - root - INFO - Loaded 4 asset selection records from logs/trades\asset_selection_********.json
2025-06-27 21:42:56,414 - root - INFO - Trade logger initialized with log directory: logs/trades
2025-06-27 21:42:57,908 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 21:42:57,922 - root - INFO - Configuration loaded successfully.
2025-06-27 21:42:57,922 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 21:42:57,929 - root - INFO - Configuration loaded successfully.
2025-06-27 21:42:57,929 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 21:42:57,939 - root - INFO - Configuration loaded successfully.
2025-06-27 21:42:57,939 - root - INFO - Loading notification configuration from config/notifications_bitvavo.json...
2025-06-27 21:42:57,939 - root - INFO - Notification configuration loaded successfully.
2025-06-27 21:42:59,080 - root - INFO - Telegram command handlers registered
2025-06-27 21:42:59,080 - root - INFO - Telegram bot polling started
2025-06-27 21:42:59,080 - root - INFO - Telegram notifier initialized with notification level: standard
2025-06-27 21:42:59,080 - root - INFO - Telegram notification channel initialized
2025-06-27 21:42:59,091 - root - INFO - Successfully loaded templates using utf-8 encoding
2025-06-27 21:42:59,091 - root - INFO - Loaded 24 templates from file
2025-06-27 21:42:59,091 - root - INFO - Notification manager initialized with 1 channels
2025-06-27 21:42:59,091 - root - INFO - Notification manager initialized
2025-06-27 21:42:59,091 - root - INFO - Added critical time window: 23:55 for 10 minutes - Before daily candle close (1d)
2025-06-27 21:42:59,091 - root - INFO - Added critical time window: 00:00 for 10 minutes - After daily candle close (1d)
2025-06-27 21:42:59,091 - root - INFO - Set up critical time windows for 1d timeframe
2025-06-27 21:42:59,091 - root - INFO - Network watchdog initialized with 10s check interval
2025-06-27 21:42:59,099 - root - INFO - Loaded recovery state from data/state\recovery_state.json
2025-06-27 21:42:59,100 - root - INFO - No state file found at C:\Users\<USER>\OneDrive\Stalinis kompiuteris\Asset_Screener_Python\data\state\data/state\background_service_20250626_114325.json.json
2025-06-27 21:42:59,100 - root - INFO - Recovery manager initialized
2025-06-27 21:42:59,100 - root - INFO - [DEBUG] TRADING EXECUTOR - Initializing with exchange: bitvavo
2025-06-27 21:42:59,100 - root - INFO - [DEBUG] TRADING EXECUTOR - Trading config: {'enabled': True, 'exchange': 'bitvavo', 'initial_capital': 100, 'max_slippage_pct': 0.5, 'min_order_values': {'default': 5.0}, 'mode': 'paper', 'order_type': 'market', 'position_size_pct': 10, 'risk_management': {'max_daily_trades': 5, 'max_open_positions': 10}, 'transaction_fee_rate': 0.0025}
2025-06-27 21:42:59,100 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 21:42:59,108 - root - INFO - Configuration loaded successfully.
2025-06-27 21:42:59,108 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-27 21:42:59,108 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-27 21:42:59,108 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-27 21:42:59,108 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-27 21:42:59,108 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-27 21:42:59,108 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-27 21:42:59,108 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-27 21:42:59,108 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-27 21:42:59,108 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 21:42:59,122 - root - INFO - Configuration loaded successfully.
2025-06-27 21:42:59,149 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getMe "HTTP/1.1 200 OK"
2025-06-27 21:42:59,160 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/deleteWebhook "HTTP/1.1 200 OK"
2025-06-27 21:42:59,160 - telegram.ext.Application - INFO - Application started
2025-06-27 21:42:59,487 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-27 21:42:59,487 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-27 21:42:59,487 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-27 21:42:59,487 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-27 21:42:59,487 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-27 21:42:59,487 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 21:42:59,498 - root - INFO - Configuration loaded successfully.
2025-06-27 21:42:59,505 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 21:42:59,514 - root - INFO - Configuration loaded successfully.
2025-06-27 21:42:59,514 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-27 21:42:59,521 - root - INFO - Configuration loaded successfully.
2025-06-27 21:42:59,521 - root - INFO - Loaded paper trading history from paper_trading_history.json
2025-06-27 21:42:59,521 - root - INFO - Paper trading initialized with EUR as quote currency
2025-06-27 21:42:59,525 - root - INFO - Trading executor initialized for bitvavo
2025-06-27 21:42:59,525 - root - INFO - Trading mode: paper
2025-06-27 21:42:59,525 - root - INFO - Trading enabled: True
2025-06-27 21:42:59,525 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-27 21:42:59,525 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-27 21:42:59,525 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-27 21:42:59,525 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-27 21:42:59,525 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 21:42:59,533 - root - INFO - Configuration loaded successfully.
2025-06-27 21:42:59,800 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-27 21:42:59,800 - root - INFO - Trading enabled in paper mode
2025-06-27 21:42:59,811 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-27 21:42:59,811 - root - INFO - Reset paper trading account to initial balance: 100 EUR
2025-06-27 21:42:59,811 - root - INFO - Reset paper trading account to initial balance
2025-06-27 21:42:59,811 - root - INFO - Generated run ID: ********_214259
2025-06-27 21:42:59,811 - root - INFO - Ensured metrics directory exists: Performance_Metrics
2025-06-27 21:42:59,811 - root - INFO - Background service initialized
2025-06-27 21:42:59,811 - root - INFO - Network watchdog started
2025-06-27 21:42:59,811 - root - INFO - Network watchdog started
2025-06-27 21:42:59,811 - root - INFO - Schedule set up for 1d timeframe
2025-06-27 21:42:59,811 - root - INFO - Background service started
2025-06-27 21:42:59,816 - root - INFO - Executing strategy (run #1)...
2025-06-27 21:42:59,817 - root - INFO - Resetting daily trade counters for this strategy execution
2025-06-27 21:42:59,817 - root - INFO - No trades recorded today (Max: 5)
2025-06-27 21:42:59,819 - root - INFO - Initialized daily trades counter for 2025-06-27
2025-06-27 21:42:59,819 - root - INFO - Creating snapshot for candle timestamp: ********
2025-06-27 21:42:59,898 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-27 21:43:05,839 - root - WARNING - Failed to send with Markdown formatting: Timed out
2025-06-27 21:43:05,948 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-27 21:43:05,948 - root - INFO - Using trend method 'PGO For Loop' for strategy execution
2025-06-27 21:43:05,948 - root - INFO - Using configured start date for 1d timeframe: 2025-02-10
2025-06-27 21:43:05,948 - root - INFO - Using recent date for performance tracking: 2025-06-20
2025-06-27 21:43:05,948 - root - INFO - Using real-time data fetching - only fetching new data since last cached timestamp
2025-06-27 21:43:05,992 - root - INFO - Loaded 2138 rows of ETH/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:43:05,993 - root - INFO - Last timestamp in cache for ETH/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:43:05,993 - root - INFO - Expected last timestamp for ETH/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:43:05,994 - root - INFO - Data is up to date for ETH/USDT
2025-06-27 21:43:05,995 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:43:06,013 - root - INFO - Loaded 2138 rows of BTC/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:43:06,013 - root - INFO - Last timestamp in cache for BTC/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:43:06,014 - root - INFO - Expected last timestamp for BTC/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:43:06,014 - root - INFO - Data is up to date for BTC/USDT
2025-06-27 21:43:06,015 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:43:06,029 - root - INFO - Loaded 1781 rows of SOL/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:43:06,030 - root - INFO - Last timestamp in cache for SOL/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:43:06,031 - root - INFO - Expected last timestamp for SOL/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:43:06,031 - root - INFO - Data is up to date for SOL/USDT
2025-06-27 21:43:06,032 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:43:06,045 - root - INFO - Loaded 786 rows of SUI/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:43:06,045 - root - INFO - Last timestamp in cache for SUI/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:43:06,046 - root - INFO - Expected last timestamp for SUI/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:43:06,046 - root - INFO - Data is up to date for SUI/USDT
2025-06-27 21:43:06,047 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:43:06,067 - root - INFO - Loaded 2138 rows of XRP/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:43:06,068 - root - INFO - Last timestamp in cache for XRP/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:43:06,068 - root - INFO - Expected last timestamp for XRP/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:43:06,068 - root - INFO - Data is up to date for XRP/USDT
2025-06-27 21:43:06,069 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:43:06,090 - root - INFO - Loaded 1716 rows of AAVE/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:43:06,090 - root - INFO - Last timestamp in cache for AAVE/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:43:06,091 - root - INFO - Expected last timestamp for AAVE/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:43:06,091 - root - INFO - Data is up to date for AAVE/USDT
2025-06-27 21:43:06,092 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:43:06,105 - root - INFO - Loaded 1739 rows of AVAX/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:43:06,105 - root - INFO - Last timestamp in cache for AVAX/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:43:06,106 - root - INFO - Expected last timestamp for AVAX/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:43:06,106 - root - INFO - Data is up to date for AVAX/USDT
2025-06-27 21:43:06,106 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:43:06,121 - root - INFO - Loaded 2138 rows of ADA/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:43:06,121 - root - INFO - Last timestamp in cache for ADA/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:43:06,122 - root - INFO - Expected last timestamp for ADA/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:43:06,122 - root - INFO - Data is up to date for ADA/USDT
2025-06-27 21:43:06,123 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:43:06,142 - root - INFO - Loaded 2138 rows of LINK/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:43:06,143 - root - INFO - Last timestamp in cache for LINK/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:43:06,143 - root - INFO - Expected last timestamp for LINK/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:43:06,143 - root - INFO - Data is up to date for LINK/USDT
2025-06-27 21:43:06,144 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:43:06,164 - root - INFO - Loaded 2138 rows of TRX/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:43:06,164 - root - INFO - Last timestamp in cache for TRX/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:43:06,165 - root - INFO - Expected last timestamp for TRX/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:43:06,165 - root - INFO - Data is up to date for TRX/USDT
2025-06-27 21:43:06,166 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:43:06,180 - root - INFO - Loaded 784 rows of PEPE/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:43:06,180 - root - INFO - Last timestamp in cache for PEPE/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:43:06,181 - root - INFO - Expected last timestamp for PEPE/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:43:06,181 - root - INFO - Data is up to date for PEPE/USDT
2025-06-27 21:43:06,182 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:43:06,200 - root - INFO - Loaded 2138 rows of DOGE/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:43:06,200 - root - INFO - Last timestamp in cache for DOGE/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:43:06,201 - root - INFO - Expected last timestamp for DOGE/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:43:06,201 - root - INFO - Data is up to date for DOGE/USDT
2025-06-27 21:43:06,202 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:43:06,219 - root - INFO - Loaded 2138 rows of BNB/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:43:06,219 - root - INFO - Last timestamp in cache for BNB/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:43:06,220 - root - INFO - Expected last timestamp for BNB/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:43:06,220 - root - INFO - Data is up to date for BNB/USDT
2025-06-27 21:43:06,221 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:43:06,237 - root - INFO - Loaded 1774 rows of DOT/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:43:06,237 - root - INFO - Last timestamp in cache for DOT/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:43:06,237 - root - INFO - Expected last timestamp for DOT/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:43:06,238 - root - INFO - Data is up to date for DOT/USDT
2025-06-27 21:43:06,239 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:43:06,241 - root - INFO - Using 14 trend assets (USDT) for analysis and 14 trading assets (EUR) for execution
2025-06-27 21:43:06,242 - root - INFO - MTPI Multi-Indicator Configuration:
2025-06-27 21:43:06,242 - root - INFO -   - Number of indicators: 8
2025-06-27 21:43:06,242 - root - INFO -   - Enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-27 21:43:06,242 - root - INFO -   - Combination method: consensus
2025-06-27 21:43:06,242 - root - INFO -   - Long threshold: 0.1
2025-06-27 21:43:06,242 - root - INFO -   - Short threshold: -0.1
2025-06-27 21:43:06,243 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trend_assets order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-27 21:43:06,243 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trading_assets order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-27 21:43:06,243 - root - ERROR - [DEBUG] BACKGROUND SERVICE - BTC position in trend_assets: 2
2025-06-27 21:43:06,243 - root - ERROR - [DEBUG] BACKGROUND SERVICE - TRX position in trend_assets: 10
2025-06-27 21:43:06,244 - root - INFO - === RUNNING STRATEGY FOR WEB USING TEST_ALLOCATION ===
2025-06-27 21:43:06,244 - root - INFO - Parameters: use_mtpi_signal=True, mtpi_indicator_type=PGO
2025-06-27 21:43:06,244 - root - INFO - mtpi_timeframe=1d, mtpi_pgo_length=35
2025-06-27 21:43:06,244 - root - INFO - mtpi_upper_threshold=1.1, mtpi_lower_threshold=-0.58
2025-06-27 21:43:06,244 - root - INFO - timeframe=1d, analysis_start_date='2025-02-10'
2025-06-27 21:43:06,244 - root - INFO - n_assets=1, use_weighted_allocation=False
2025-06-27 21:43:06,244 - root - INFO - Using provided trend method: PGO For Loop
2025-06-27 21:43:06,244 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 21:43:06,252 - root - INFO - Configuration loaded successfully.
2025-06-27 21:43:06,252 - root - INFO - Saving configuration to config/settings_bitvavo_eur.yaml...
2025-06-27 21:43:06,260 - root - INFO - Configuration saved successfully.
2025-06-27 21:43:06,260 - root - INFO - Trend detection assets (USDT): ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-27 21:43:06,260 - root - INFO - Number of trend detection assets: 14
2025-06-27 21:43:06,260 - root - INFO - Selected assets type: <class 'list'>
2025-06-27 21:43:06,261 - root - INFO - Trading execution assets (EUR): ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-27 21:43:06,261 - root - INFO - Number of trading assets: 14
2025-06-27 21:43:06,261 - root - INFO - Trading assets type: <class 'list'>
2025-06-27 21:43:06,492 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-27 21:43:06,501 - root - INFO - Configuration loaded successfully.
2025-06-27 21:43:06,511 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 21:43:06,520 - root - INFO - Configuration loaded successfully.
2025-06-27 21:43:06,521 - root - INFO - Execution context: backtesting
2025-06-27 21:43:06,521 - root - INFO - Execution timing: candle_close
2025-06-27 21:43:06,521 - root - INFO - Ratio calculation method: independent
2025-06-27 21:43:06,521 - root - INFO - Tie-breaking strategy: imcumbent
2025-06-27 21:43:06,522 - root - INFO - Asset trend PGO parameters: length=None, upper_threshold=None, lower_threshold=None
2025-06-27 21:43:06,522 - root - INFO - MTPI indicators override: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-27 21:43:06,522 - root - INFO - MTPI combination method override: consensus
2025-06-27 21:43:06,522 - root - INFO - MTPI long threshold override: 0.1
2025-06-27 21:43:06,522 - root - INFO - MTPI short threshold override: -0.1
2025-06-27 21:43:06,523 - root - INFO - Using standard warmup period of 60 days for equal allocation
2025-06-27 21:43:06,523 - root - INFO - Fetching data from 2024-12-12 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-27 21:43:06,524 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:43:06,525 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:43:06,526 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:43:06,526 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:43:06,526 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:43:06,528 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:43:06,528 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:43:06,529 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:43:06,529 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:43:06,529 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:43:06,530 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:43:06,530 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:43:06,530 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:43:06,531 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:43:06,531 - root - INFO - Checking cache for 14 symbols (1d)...
2025-06-27 21:43:06,553 - root - INFO - Loaded 197 rows of ETH/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:43:06,555 - root - INFO - Metadata for ETH/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:43:06,556 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:43:06,556 - root - INFO - Loaded 197 rows of ETH/USDT data from cache (after filtering).
2025-06-27 21:43:06,573 - root - INFO - Loaded 197 rows of BTC/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:43:06,575 - root - INFO - Metadata for BTC/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:43:06,576 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:43:06,576 - root - INFO - Loaded 197 rows of BTC/USDT data from cache (after filtering).
2025-06-27 21:43:06,593 - root - INFO - Loaded 197 rows of SOL/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:43:06,595 - root - INFO - Metadata for SOL/USDT shows data starting from 2020-08-11, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:43:06,596 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:43:06,596 - root - INFO - Loaded 197 rows of SOL/USDT data from cache (after filtering).
2025-06-27 21:43:06,606 - root - INFO - Loaded 197 rows of SUI/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:43:06,607 - root - INFO - Metadata for SUI/USDT shows data starting from 2023-05-03, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:43:06,607 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:43:06,608 - root - INFO - Loaded 197 rows of SUI/USDT data from cache (after filtering).
2025-06-27 21:43:06,626 - root - INFO - Loaded 197 rows of XRP/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:43:06,627 - root - INFO - Metadata for XRP/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:43:06,627 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:43:06,628 - root - INFO - Loaded 197 rows of XRP/USDT data from cache (after filtering).
2025-06-27 21:43:06,643 - root - INFO - Loaded 197 rows of AAVE/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:43:06,645 - root - INFO - Metadata for AAVE/USDT shows data starting from 2020-10-15, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:43:06,645 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:43:06,646 - root - INFO - Loaded 197 rows of AAVE/USDT data from cache (after filtering).
2025-06-27 21:43:06,660 - root - INFO - Loaded 197 rows of AVAX/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:43:06,661 - root - INFO - Metadata for AVAX/USDT shows data starting from 2020-09-22, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:43:06,662 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:43:06,662 - root - INFO - Loaded 197 rows of AVAX/USDT data from cache (after filtering).
2025-06-27 21:43:06,681 - root - INFO - Loaded 197 rows of ADA/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:43:06,682 - root - INFO - Metadata for ADA/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:43:06,683 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:43:06,683 - root - INFO - Loaded 197 rows of ADA/USDT data from cache (after filtering).
2025-06-27 21:43:06,701 - root - INFO - Loaded 197 rows of LINK/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:43:06,702 - root - INFO - Metadata for LINK/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:43:06,703 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:43:06,703 - root - INFO - Loaded 197 rows of LINK/USDT data from cache (after filtering).
2025-06-27 21:43:06,723 - root - INFO - Loaded 197 rows of TRX/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:43:06,725 - root - INFO - Metadata for TRX/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:43:06,726 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:43:06,726 - root - INFO - Loaded 197 rows of TRX/USDT data from cache (after filtering).
2025-06-27 21:43:06,736 - root - INFO - Loaded 197 rows of PEPE/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:43:06,737 - root - INFO - Metadata for PEPE/USDT shows data starting from 2023-05-05, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:43:06,738 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:43:06,738 - root - INFO - Loaded 197 rows of PEPE/USDT data from cache (after filtering).
2025-06-27 21:43:06,761 - root - INFO - Loaded 197 rows of DOGE/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:43:06,762 - root - INFO - Metadata for DOGE/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:43:06,763 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:43:06,763 - root - INFO - Loaded 197 rows of DOGE/USDT data from cache (after filtering).
2025-06-27 21:43:06,790 - root - INFO - Loaded 197 rows of BNB/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:43:06,792 - root - INFO - Metadata for BNB/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:43:06,793 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:43:06,793 - root - INFO - Loaded 197 rows of BNB/USDT data from cache (after filtering).
2025-06-27 21:43:06,814 - root - INFO - Loaded 197 rows of DOT/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:43:06,816 - root - INFO - Metadata for DOT/USDT shows data starting from 2020-08-18, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:43:06,816 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:43:06,816 - root - INFO - Loaded 197 rows of DOT/USDT data from cache (after filtering).
2025-06-27 21:43:06,816 - root - INFO - All 14 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-27 21:43:06,818 - root - INFO - Asset ETH/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:43:06,818 - root - INFO - Asset BTC/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:43:06,818 - root - INFO - Asset SOL/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:43:06,819 - root - INFO - Asset SUI/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:43:06,820 - root - INFO - Asset XRP/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:43:06,820 - root - INFO - Asset AAVE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:43:06,820 - root - INFO - Asset AVAX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:43:06,821 - root - INFO - Asset ADA/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:43:06,821 - root - INFO - Asset LINK/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:43:06,821 - root - INFO - Asset TRX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:43:06,821 - root - INFO - Asset PEPE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:43:06,822 - root - INFO - Asset DOGE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:43:06,822 - root - INFO - Asset BNB/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:43:06,822 - root - INFO - Asset DOT/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:43:06,843 - root - INFO - Using standard MTPI warmup period of 120 days
2025-06-27 21:43:06,843 - root - INFO - Fetching MTPI signals from 2024-10-13 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-27 21:43:06,843 - root - INFO - Fetching MTPI signals using trend method: PGO For Loop
2025-06-27 21:43:06,844 - root - INFO - Using multi-indicator MTPI with config override: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-27 21:43:06,844 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-27 21:43:06,853 - root - INFO - Configuration loaded successfully.
2025-06-27 21:43:06,853 - root - INFO - Loaded MTPI multi-indicator configuration with 8 enabled indicators
2025-06-27 21:43:06,853 - root - INFO - Applying configuration overrides: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-27 21:43:06,853 - root - INFO - Override: enabled_indicators = ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-27 21:43:06,854 - root - INFO - Override: combination_method = consensus
2025-06-27 21:43:06,854 - root - INFO - Override: long_threshold = 0.1
2025-06-27 21:43:06,854 - root - INFO - Override: short_threshold = -0.1
2025-06-27 21:43:06,854 - root - INFO - Using MTPI configuration: indicators=['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], method=consensus, thresholds=(-0.1, 0.1)
2025-06-27 21:43:06,854 - root - INFO - Calculated appropriate limit for 1d timeframe: 500 candles (minimum 180.0 candles needed for 60 length indicator)
2025-06-27 21:43:06,854 - root - INFO - Using adjusted limit: 500 for max indicator length: 60
2025-06-27 21:43:06,856 - root - INFO - Checking cache for 1 symbols (1d)...
2025-06-27 21:43:06,875 - root - INFO - Loaded 257 rows of BTC/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:43:06,876 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:43:06,876 - root - INFO - Loaded 257 rows of BTC/USDT data from cache (after filtering).
2025-06-27 21:43:06,876 - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-27 21:43:06,876 - root - INFO - Fetched BTC data: 257 candles from 2024-10-13 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:43:06,876 - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-06-27 21:43:06,909 - root - INFO - Generated PGO Score signals: {-1: 105, 0: 34, 1: 118}
2025-06-27 21:43:06,910 - root - INFO - Generated pgo signals: 257 values
2025-06-27 21:43:06,910 - root - INFO - Generating Bollinger Band signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False, heikin_src=close
2025-06-27 21:43:06,910 - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-06-27 21:43:06,920 - root - INFO - Generated BB Score signals: {-1: 107, 0: 32, 1: 118}
2025-06-27 21:43:06,920 - root - INFO - Generated Bollinger Band signals: 257 values
2025-06-27 21:43:06,921 - root - INFO - Generated bollinger_bands signals: 257 values
2025-06-27 21:43:07,478 - root - INFO - Generated DWMA signals using Weighted SD method
2025-06-27 21:43:07,478 - root - INFO - Generated dwma_score signals: 257 values
2025-06-27 21:43:07,552 - root - INFO - Generated DEMA Supertrend signals
2025-06-27 21:43:07,553 - root - INFO - Signal distribution: {-1: 143, 0: 1, 1: 113}
2025-06-27 21:43:07,553 - root - INFO - Generated DEMA Super Score signals
2025-06-27 21:43:07,553 - root - INFO - Generated dema_super_score signals: 257 values
2025-06-27 21:43:07,654 - root - INFO - Generated DPSD signals
2025-06-27 21:43:07,655 - root - INFO - Signal distribution: {-1: 99, 0: 87, 1: 71}
2025-06-27 21:43:07,655 - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-06-27 21:43:07,655 - root - INFO - Generated dpsd_score signals: 257 values
2025-06-27 21:43:07,662 - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-06-27 21:43:07,667 - root - INFO - Generated AAD Score signals using SMA method
2025-06-27 21:43:07,667 - root - INFO - Generated aad_score signals: 257 values
2025-06-27 21:43:07,738 - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-06-27 21:43:07,738 - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-06-27 21:43:07,738 - root - INFO - Generated dynamic_ema_score signals: 257 values
2025-06-27 21:43:07,853 - root - INFO - Generated quantile_dema_score signals: 257 values
2025-06-27 21:43:07,860 - root - INFO - Combined 8 signals using arithmetic mean aggregation
2025-06-27 21:43:07,860 - root - INFO - Signal distribution: {1: 145, -1: 111, 0: 1}
2025-06-27 21:43:07,863 - root - INFO - Generated combined MTPI signals: 257 values using consensus method
2025-06-27 21:43:07,864 - root - INFO - Signal distribution: {1: 145, -1: 111, 0: 1}
2025-06-27 21:43:07,864 - root - INFO - Calculating daily scores using trend method: PGO For Loop...
2025-06-27 21:43:07,868 - root - INFO - Saving configuration to config/settings.yaml...
2025-06-27 21:43:07,876 - root - INFO - Configuration saved successfully.
2025-06-27 21:43:07,876 - root - INFO - Updated config with trend method: PGO For Loop and PGO parameters
2025-06-27 21:43:07,876 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-27 21:43:07,894 - root - INFO - Configuration loaded successfully.
2025-06-27 21:43:07,896 - root - INFO - Using ratio-based approach with PGO (PGO For Loop)...
2025-06-27 21:43:07,896 - root - INFO - Calculating ratio PGO signals for 14 assets with PGO(35) using full OHLCV data
2025-06-27 21:43:07,896 - root - INFO - Using ratio calculation method: independent
2025-06-27 21:43:07,920 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:07,951 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BTC/USDT using full OHLCV data
2025-06-27 21:43:07,985 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 21:43:07,987 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:08,010 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 21:43:08,018 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SOL/USDT using full OHLCV data
2025-06-27 21:43:08,043 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-27 21:43:08,045 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:08,058 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-27 21:43:08,072 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SUI/USDT using full OHLCV data
2025-06-27 21:43:08,099 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 21:43:08,099 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:08,115 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 21:43:08,127 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/XRP/USDT using full OHLCV data
2025-06-27 21:43:08,148 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 21:43:08,148 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:08,165 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 21:43:08,173 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AAVE/USDT using full OHLCV data
2025-06-27 21:43:08,201 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-27 21:43:08,201 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:08,220 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-27 21:43:08,225 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AVAX/USDT using full OHLCV data
2025-06-27 21:43:08,246 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 21:43:08,246 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:08,263 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 21:43:08,279 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/ADA/USDT using full OHLCV data
2025-06-27 21:43:08,304 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-27 21:43:08,305 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:08,329 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-27 21:43:08,338 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/LINK/USDT using full OHLCV data
2025-06-27 21:43:08,377 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-27 21:43:08,377 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:08,402 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-27 21:43:08,410 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/TRX/USDT using full OHLCV data
2025-06-27 21:43:08,445 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 21:43:08,445 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:08,473 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 21:43:08,481 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/PEPE/USDT using full OHLCV data
2025-06-27 21:43:08,505 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:43:08,505 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:08,529 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:43:08,537 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOGE/USDT using full OHLCV data
2025-06-27 21:43:08,557 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:08,579 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BNB/USDT using full OHLCV data
2025-06-27 21:43:08,605 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-27 21:43:08,605 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:08,628 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-27 21:43:08,638 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOT/USDT using full OHLCV data
2025-06-27 21:43:08,657 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 21:43:08,657 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:08,678 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 21:43:08,689 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ETH/USDT using full OHLCV data
2025-06-27 21:43:08,714 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:08,743 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SOL/USDT using full OHLCV data
2025-06-27 21:43:08,765 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 21:43:08,765 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:08,780 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 21:43:08,788 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SUI/USDT using full OHLCV data
2025-06-27 21:43:08,805 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 21:43:08,805 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:08,830 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 21:43:08,831 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/XRP/USDT using full OHLCV data
2025-06-27 21:43:08,857 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 21:43:08,857 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:08,871 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 21:43:08,885 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AAVE/USDT using full OHLCV data
2025-06-27 21:43:08,904 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-27 21:43:08,904 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:08,922 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-27 21:43:08,929 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AVAX/USDT using full OHLCV data
2025-06-27 21:43:08,947 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 21:43:08,947 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:08,963 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 21:43:08,972 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ADA/USDT using full OHLCV data
2025-06-27 21:43:08,997 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 21:43:08,997 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:09,015 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 21:43:09,021 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/LINK/USDT using full OHLCV data
2025-06-27 21:43:09,046 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:43:09,046 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:09,062 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:43:09,072 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/TRX/USDT using full OHLCV data
2025-06-27 21:43:09,105 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 21:43:09,105 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:09,121 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 21:43:09,128 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/PEPE/USDT using full OHLCV data
2025-06-27 21:43:09,145 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:09,178 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOGE/USDT using full OHLCV data
2025-06-27 21:43:09,203 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:09,230 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/BNB/USDT using full OHLCV data
2025-06-27 21:43:09,250 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-27 21:43:09,250 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:09,266 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-27 21:43:09,273 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOT/USDT using full OHLCV data
2025-06-27 21:43:09,305 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-27 21:43:09,305 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:09,306 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:43:09,326 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-27 21:43:09,330 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ETH/USDT using full OHLCV data
2025-06-27 21:43:09,358 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:09,379 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BTC/USDT using full OHLCV data
2025-06-27 21:43:09,404 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:43:09,404 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:09,428 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:43:09,428 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/SUI/USDT using full OHLCV data
2025-06-27 21:43:09,458 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 21:43:09,458 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:09,489 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 21:43:09,495 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/XRP/USDT using full OHLCV data
2025-06-27 21:43:09,517 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-27 21:43:09,517 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:09,534 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-27 21:43:09,542 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AAVE/USDT using full OHLCV data
2025-06-27 21:43:09,567 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-27 21:43:09,567 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:09,585 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-27 21:43:09,593 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AVAX/USDT using full OHLCV data
2025-06-27 21:43:09,626 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 21:43:09,626 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:09,646 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 21:43:09,650 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ADA/USDT using full OHLCV data
2025-06-27 21:43:09,675 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 21:43:09,675 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:09,693 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 21:43:09,705 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/LINK/USDT using full OHLCV data
2025-06-27 21:43:09,733 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-27 21:43:09,733 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:09,755 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-27 21:43:09,763 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/TRX/USDT using full OHLCV data
2025-06-27 21:43:09,793 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-27 21:43:09,795 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:09,823 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-27 21:43:09,829 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/PEPE/USDT using full OHLCV data
2025-06-27 21:43:09,866 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:43:09,866 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:09,888 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:43:09,895 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOGE/USDT using full OHLCV data
2025-06-27 21:43:09,925 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:43:09,928 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:09,955 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:43:09,960 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BNB/USDT using full OHLCV data
2025-06-27 21:43:09,977 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 21:43:09,977 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:10,010 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 21:43:10,027 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOT/USDT using full OHLCV data
2025-06-27 21:43:10,068 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:43:10,068 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:10,097 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:43:10,105 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ETH/USDT using full OHLCV data
2025-06-27 21:43:10,138 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:10,163 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BTC/USDT using full OHLCV data
2025-06-27 21:43:10,189 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:10,224 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/SOL/USDT using full OHLCV data
2025-06-27 21:43:10,261 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:10,284 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/XRP/USDT using full OHLCV data
2025-06-27 21:43:10,313 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-27 21:43:10,313 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:10,338 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-27 21:43:10,340 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AAVE/USDT using full OHLCV data
2025-06-27 21:43:10,361 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:10,388 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AVAX/USDT using full OHLCV data
2025-06-27 21:43:10,412 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:10,435 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ADA/USDT using full OHLCV data
2025-06-27 21:43:10,462 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:43:10,462 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:10,479 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:43:10,488 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/LINK/USDT using full OHLCV data
2025-06-27 21:43:10,505 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:10,529 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/TRX/USDT using full OHLCV data
2025-06-27 21:43:10,555 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:10,578 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/PEPE/USDT using full OHLCV data
2025-06-27 21:43:10,594 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:43:10,594 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:10,613 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:43:10,622 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOGE/USDT using full OHLCV data
2025-06-27 21:43:10,647 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:10,673 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BNB/USDT using full OHLCV data
2025-06-27 21:43:10,688 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:10,720 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOT/USDT using full OHLCV data
2025-06-27 21:43:10,738 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-27 21:43:10,738 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:10,762 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-27 21:43:10,768 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ETH/USDT using full OHLCV data
2025-06-27 21:43:10,786 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:10,814 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BTC/USDT using full OHLCV data
2025-06-27 21:43:10,830 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:10,872 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SOL/USDT using full OHLCV data
2025-06-27 21:43:10,889 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 21:43:10,889 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:10,912 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 21:43:10,922 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SUI/USDT using full OHLCV data
2025-06-27 21:43:10,945 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:43:10,945 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:10,962 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:43:10,977 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AAVE/USDT using full OHLCV data
2025-06-27 21:43:11,002 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-27 21:43:11,002 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:11,019 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-27 21:43:11,027 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AVAX/USDT using full OHLCV data
2025-06-27 21:43:11,044 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-27 21:43:11,044 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:11,062 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-27 21:43:11,072 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ADA/USDT using full OHLCV data
2025-06-27 21:43:11,095 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:43:11,095 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:11,111 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:43:11,118 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/LINK/USDT using full OHLCV data
2025-06-27 21:43:11,138 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 21:43:11,138 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:11,155 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 21:43:11,163 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/TRX/USDT using full OHLCV data
2025-06-27 21:43:11,177 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:43:11,184 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:11,205 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:43:11,210 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/PEPE/USDT using full OHLCV data
2025-06-27 21:43:11,235 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:11,263 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOGE/USDT using full OHLCV data
2025-06-27 21:43:11,288 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:11,317 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BNB/USDT using full OHLCV data
2025-06-27 21:43:11,339 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-27 21:43:11,339 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:11,364 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-27 21:43:11,372 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOT/USDT using full OHLCV data
2025-06-27 21:43:11,394 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:11,412 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ETH/USDT using full OHLCV data
2025-06-27 21:43:11,438 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:11,463 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BTC/USDT using full OHLCV data
2025-06-27 21:43:11,488 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:11,517 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SOL/USDT using full OHLCV data
2025-06-27 21:43:11,541 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:11,563 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SUI/USDT using full OHLCV data
2025-06-27 21:43:11,589 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:11,612 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/XRP/USDT using full OHLCV data
2025-06-27 21:43:11,638 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 21:43:11,638 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:11,655 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 21:43:11,664 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/AVAX/USDT using full OHLCV data
2025-06-27 21:43:11,679 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:43:11,679 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:11,704 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:43:11,711 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ADA/USDT using full OHLCV data
2025-06-27 21:43:11,728 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 21:43:11,728 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:11,744 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 21:43:11,764 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/LINK/USDT using full OHLCV data
2025-06-27 21:43:11,786 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:11,809 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/TRX/USDT using full OHLCV data
2025-06-27 21:43:11,838 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:11,864 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/PEPE/USDT using full OHLCV data
2025-06-27 21:43:11,889 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-27 21:43:11,889 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:11,906 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-27 21:43:11,914 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOGE/USDT using full OHLCV data
2025-06-27 21:43:11,940 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:11,964 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BNB/USDT using full OHLCV data
2025-06-27 21:43:11,981 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:43:11,981 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:12,005 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:43:12,012 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOT/USDT using full OHLCV data
2025-06-27 21:43:12,037 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:12,073 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ETH/USDT using full OHLCV data
2025-06-27 21:43:12,095 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:12,124 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BTC/USDT using full OHLCV data
2025-06-27 21:43:12,145 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:12,164 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SOL/USDT using full OHLCV data
2025-06-27 21:43:12,188 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:12,217 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SUI/USDT using full OHLCV data
2025-06-27 21:43:12,241 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:12,271 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/XRP/USDT using full OHLCV data
2025-06-27 21:43:12,288 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-27 21:43:12,288 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:12,309 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-27 21:43:12,312 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/AAVE/USDT using full OHLCV data
2025-06-27 21:43:12,340 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:12,364 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ADA/USDT using full OHLCV data
2025-06-27 21:43:12,385 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-27 21:43:12,385 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:12,394 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-27 21:43:12,409 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/LINK/USDT using full OHLCV data
2025-06-27 21:43:12,429 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:12,455 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/TRX/USDT using full OHLCV data
2025-06-27 21:43:12,472 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:12,495 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/PEPE/USDT using full OHLCV data
2025-06-27 21:43:12,520 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:12,549 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOGE/USDT using full OHLCV data
2025-06-27 21:43:12,580 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:12,609 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BNB/USDT using full OHLCV data
2025-06-27 21:43:12,639 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:12,678 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOT/USDT using full OHLCV data
2025-06-27 21:43:12,703 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:43:12,703 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:12,722 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:43:12,730 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/ETH/USDT using full OHLCV data
2025-06-27 21:43:12,761 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:12,796 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BTC/USDT using full OHLCV data
2025-06-27 21:43:12,814 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:12,839 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SOL/USDT using full OHLCV data
2025-06-27 21:43:12,859 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-27 21:43:12,859 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:12,881 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-27 21:43:12,886 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SUI/USDT using full OHLCV data
2025-06-27 21:43:12,927 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:12,967 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/XRP/USDT using full OHLCV data
2025-06-27 21:43:12,998 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-27 21:43:12,998 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:13,023 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-27 21:43:13,034 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AAVE/USDT using full OHLCV data
2025-06-27 21:43:13,067 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 21:43:13,068 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:13,094 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 21:43:13,107 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AVAX/USDT using full OHLCV data
2025-06-27 21:43:13,135 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-27 21:43:13,135 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:13,160 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-27 21:43:13,165 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/LINK/USDT using full OHLCV data
2025-06-27 21:43:13,184 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:13,229 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/TRX/USDT using full OHLCV data
2025-06-27 21:43:13,244 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 21:43:13,244 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:13,272 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 21:43:13,277 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/PEPE/USDT using full OHLCV data
2025-06-27 21:43:13,305 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:43:13,305 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:13,327 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:43:13,335 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOGE/USDT using full OHLCV data
2025-06-27 21:43:13,360 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:13,398 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BNB/USDT using full OHLCV data
2025-06-27 21:43:13,429 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:43:13,429 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:13,446 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:43:13,455 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOT/USDT using full OHLCV data
2025-06-27 21:43:13,472 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:13,514 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ETH/USDT using full OHLCV data
2025-06-27 21:43:13,555 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:13,588 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BTC/USDT using full OHLCV data
2025-06-27 21:43:13,612 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:13,646 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SOL/USDT using full OHLCV data
2025-06-27 21:43:13,671 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:13,705 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SUI/USDT using full OHLCV data
2025-06-27 21:43:13,722 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:13,753 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/XRP/USDT using full OHLCV data
2025-06-27 21:43:13,772 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-27 21:43:13,772 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:13,788 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-27 21:43:13,794 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AAVE/USDT using full OHLCV data
2025-06-27 21:43:13,821 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:13,844 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AVAX/USDT using full OHLCV data
2025-06-27 21:43:13,867 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:13,888 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ADA/USDT using full OHLCV data
2025-06-27 21:43:13,912 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:13,940 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/TRX/USDT using full OHLCV data
2025-06-27 21:43:13,955 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:43:13,961 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:13,984 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:43:13,988 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/PEPE/USDT using full OHLCV data
2025-06-27 21:43:14,013 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:14,047 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOGE/USDT using full OHLCV data
2025-06-27 21:43:14,067 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:14,097 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BNB/USDT using full OHLCV data
2025-06-27 21:43:14,121 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:14,147 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOT/USDT using full OHLCV data
2025-06-27 21:43:14,182 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:14,215 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ETH/USDT using full OHLCV data
2025-06-27 21:43:14,238 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:14,274 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BTC/USDT using full OHLCV data
2025-06-27 21:43:14,314 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:43:14,316 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:14,339 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:43:14,343 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SOL/USDT using full OHLCV data
2025-06-27 21:43:14,360 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:14,389 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SUI/USDT using full OHLCV data
2025-06-27 21:43:14,410 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:43:14,410 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:14,425 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:43:14,438 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/XRP/USDT using full OHLCV data
2025-06-27 21:43:14,455 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 21:43:14,458 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:14,475 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 21:43:14,482 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AAVE/USDT using full OHLCV data
2025-06-27 21:43:14,499 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 21:43:14,499 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:14,525 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 21:43:14,526 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AVAX/USDT using full OHLCV data
2025-06-27 21:43:14,550 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:14,576 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ADA/USDT using full OHLCV data
2025-06-27 21:43:14,608 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 21:43:14,608 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:14,645 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 21:43:14,647 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/LINK/USDT using full OHLCV data
2025-06-27 21:43:14,671 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 21:43:14,671 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:14,691 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 21:43:14,698 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/PEPE/USDT using full OHLCV data
2025-06-27 21:43:14,710 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:14,743 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOGE/USDT using full OHLCV data
2025-06-27 21:43:14,763 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:14,788 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BNB/USDT using full OHLCV data
2025-06-27 21:43:14,808 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:14,834 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOT/USDT using full OHLCV data
2025-06-27 21:43:14,858 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:14,888 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ETH/USDT using full OHLCV data
2025-06-27 21:43:14,905 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:14,928 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BTC/USDT using full OHLCV data
2025-06-27 21:43:14,955 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:14,985 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SOL/USDT using full OHLCV data
2025-06-27 21:43:15,005 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:15,032 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SUI/USDT using full OHLCV data
2025-06-27 21:43:15,046 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:15,072 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/XRP/USDT using full OHLCV data
2025-06-27 21:43:15,089 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-27 21:43:15,096 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:15,117 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-27 21:43:15,122 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AAVE/USDT using full OHLCV data
2025-06-27 21:43:15,139 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 21:43:15,139 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:15,163 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 21:43:15,171 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AVAX/USDT using full OHLCV data
2025-06-27 21:43:15,188 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:43:15,188 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:15,205 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:43:15,212 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ADA/USDT using full OHLCV data
2025-06-27 21:43:15,236 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-27 21:43:15,236 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:15,256 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-27 21:43:15,261 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/LINK/USDT using full OHLCV data
2025-06-27 21:43:15,277 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:15,305 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/TRX/USDT using full OHLCV data
2025-06-27 21:43:15,322 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:15,350 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOGE/USDT using full OHLCV data
2025-06-27 21:43:15,375 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:15,399 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BNB/USDT using full OHLCV data
2025-06-27 21:43:15,420 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:15,438 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOT/USDT using full OHLCV data
2025-06-27 21:43:15,464 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:15,486 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ETH/USDT using full OHLCV data
2025-06-27 21:43:15,505 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:15,544 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BTC/USDT using full OHLCV data
2025-06-27 21:43:15,569 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:15,589 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SOL/USDT using full OHLCV data
2025-06-27 21:43:15,623 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 21:43:15,623 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:15,655 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 21:43:15,660 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SUI/USDT using full OHLCV data
2025-06-27 21:43:15,684 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 21:43:15,684 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:15,701 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 21:43:15,710 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/XRP/USDT using full OHLCV data
2025-06-27 21:43:15,725 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 21:43:15,725 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:15,742 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 21:43:15,750 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AAVE/USDT using full OHLCV data
2025-06-27 21:43:15,775 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-27 21:43:15,775 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:15,790 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-27 21:43:15,799 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AVAX/USDT using full OHLCV data
2025-06-27 21:43:15,823 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-27 21:43:15,823 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:15,848 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-27 21:43:15,853 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ADA/USDT using full OHLCV data
2025-06-27 21:43:15,872 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 21:43:15,872 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:15,888 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 21:43:15,896 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/LINK/USDT using full OHLCV data
2025-06-27 21:43:15,921 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-27 21:43:15,921 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:15,939 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-27 21:43:15,945 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/TRX/USDT using full OHLCV data
2025-06-27 21:43:15,961 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-27 21:43:15,961 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:15,986 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-27 21:43:15,994 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/PEPE/USDT using full OHLCV data
2025-06-27 21:43:16,014 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:16,038 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BNB/USDT using full OHLCV data
2025-06-27 21:43:16,060 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 21:43:16,060 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:16,085 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 21:43:16,090 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/DOT/USDT using full OHLCV data
2025-06-27 21:43:16,109 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:16,136 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ETH/USDT using full OHLCV data
2025-06-27 21:43:16,156 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:16,173 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/BTC/USDT using full OHLCV data
2025-06-27 21:43:16,198 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:43:16,198 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:16,222 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:43:16,230 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SOL/USDT using full OHLCV data
2025-06-27 21:43:16,247 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 21:43:16,247 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:16,269 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 21:43:16,272 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SUI/USDT using full OHLCV data
2025-06-27 21:43:16,296 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 21:43:16,296 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:16,321 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 21:43:16,338 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/XRP/USDT using full OHLCV data
2025-06-27 21:43:16,358 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-27 21:43:16,358 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:16,378 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-27 21:43:16,379 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AAVE/USDT using full OHLCV data
2025-06-27 21:43:16,405 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-27 21:43:16,405 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:16,422 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-27 21:43:16,427 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AVAX/USDT using full OHLCV data
2025-06-27 21:43:16,447 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:43:16,447 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:16,463 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:43:16,477 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ADA/USDT using full OHLCV data
2025-06-27 21:43:16,495 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-27 21:43:16,495 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:16,520 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-27 21:43:16,527 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/LINK/USDT using full OHLCV data
2025-06-27 21:43:16,556 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 21:43:16,556 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:16,573 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 21:43:16,577 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/TRX/USDT using full OHLCV data
2025-06-27 21:43:16,612 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:43:16,612 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:16,628 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:43:16,639 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/PEPE/USDT using full OHLCV data
2025-06-27 21:43:16,664 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:16,688 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOGE/USDT using full OHLCV data
2025-06-27 21:43:16,714 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:16,746 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOT/USDT using full OHLCV data
2025-06-27 21:43:16,773 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:16,790 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ETH/USDT using full OHLCV data
2025-06-27 21:43:16,821 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:16,840 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BTC/USDT using full OHLCV data
2025-06-27 21:43:16,870 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:16,898 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SOL/USDT using full OHLCV data
2025-06-27 21:43:16,922 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 21:43:16,922 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:16,939 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 21:43:16,947 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SUI/USDT using full OHLCV data
2025-06-27 21:43:16,968 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 21:43:16,968 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:16,988 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 21:43:16,997 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/XRP/USDT using full OHLCV data
2025-06-27 21:43:17,021 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 21:43:17,021 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:17,042 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 21:43:17,042 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AAVE/USDT using full OHLCV data
2025-06-27 21:43:17,067 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-27 21:43:17,067 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:17,090 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-27 21:43:17,098 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AVAX/USDT using full OHLCV data
2025-06-27 21:43:17,125 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-27 21:43:17,125 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:17,146 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-27 21:43:17,148 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ADA/USDT using full OHLCV data
2025-06-27 21:43:17,172 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-27 21:43:17,172 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:17,196 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-27 21:43:17,202 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/LINK/USDT using full OHLCV data
2025-06-27 21:43:17,222 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:17,248 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/TRX/USDT using full OHLCV data
2025-06-27 21:43:17,272 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:43:17,272 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:17,292 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:43:17,298 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/PEPE/USDT using full OHLCV data
2025-06-27 21:43:17,315 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:17,342 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/DOGE/USDT using full OHLCV data
2025-06-27 21:43:17,364 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:43:17,388 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BNB/USDT using full OHLCV data
2025-06-27 21:43:19,370 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:43:19,802 - root - INFO - Successfully calculated daily scores using ratio-based comparisons.
2025-06-27 21:43:19,802 - root - INFO - Latest MTPI signal is -1
2025-06-27 21:43:19,803 - root - INFO - Latest MTPI signal is -1, will stay out of market during equity curve calculation
2025-06-27 21:43:19,803 - root - INFO - Finished calculating daily scores. DataFrame shape: (197, 14)
2025-06-27 21:43:19,803 - root - WARNING - Running strategy with n_assets=1, equal allocation, MTPI filtering ENABLED
2025-06-27 21:43:19,808 - root - INFO - Date ranges for each asset:
2025-06-27 21:43:19,808 - root - INFO -   ETH/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:43:19,809 - root - INFO -   BTC/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:43:19,809 - root - INFO -   SOL/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:43:19,809 - root - INFO -   SUI/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:43:19,809 - root - INFO -   XRP/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:43:19,810 - root - INFO -   AAVE/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:43:19,810 - root - INFO -   AVAX/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:43:19,810 - root - INFO -   ADA/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:43:19,810 - root - INFO -   LINK/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:43:19,811 - root - INFO -   TRX/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:43:19,811 - root - INFO -   PEPE/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:43:19,811 - root - INFO -   DOGE/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:43:19,811 - root - INFO -   BNB/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:43:19,812 - root - INFO -   DOT/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:43:19,813 - root - INFO - Common dates range: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:43:19,813 - root - INFO - Analysis will run from: 2025-02-10 to 2025-06-26 (137 candles)
2025-06-27 21:43:19,817 - root - INFO - EXECUTION TIMING VERIFICATION:
2025-06-27 21:43:19,818 - root - INFO -    Execution Method: candle_close
2025-06-27 21:43:19,818 - root - INFO -    Automatic execution at 00:00 UTC (candle close)
2025-06-27 21:43:19,818 - root - INFO -    Signal generated and executed immediately
2025-06-27 21:43:19,841 - root - INFO - Including ETH/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,841 - root - INFO - Including BTC/USDT on 2025-02-10 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,842 - root - INFO - Including SOL/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,842 - root - INFO - Including SUI/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,842 - root - INFO - Including XRP/USDT on 2025-02-10 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,842 - root - INFO - Including AAVE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,843 - root - INFO - Including AVAX/USDT on 2025-02-10 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,843 - root - INFO - Including ADA/USDT on 2025-02-10 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,843 - root - INFO - Including LINK/USDT on 2025-02-10 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,843 - root - INFO - Including TRX/USDT on 2025-02-10 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,844 - root - INFO - Including PEPE/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,844 - root - INFO - Including DOGE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,844 - root - INFO - Including BNB/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,845 - root - INFO - Including DOT/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,851 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:19,854 - root - INFO - Including ETH/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,854 - root - INFO - Including BTC/USDT on 2025-02-11 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,855 - root - INFO - Including SOL/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,855 - root - INFO - Including SUI/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,856 - root - INFO - Including XRP/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,856 - root - INFO - Including AAVE/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,856 - root - INFO - Including AVAX/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,856 - root - INFO - Including ADA/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,857 - root - INFO - Including LINK/USDT on 2025-02-11 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,857 - root - INFO - Including TRX/USDT on 2025-02-11 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,857 - root - INFO - Including PEPE/USDT on 2025-02-11 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,857 - root - INFO - Including DOGE/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,858 - root - INFO - Including BNB/USDT on 2025-02-11 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,858 - root - INFO - Including DOT/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,863 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:19,866 - root - INFO - Including ETH/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,866 - root - INFO - Including BTC/USDT on 2025-02-12 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,866 - root - INFO - Including SOL/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,866 - root - INFO - Including SUI/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,866 - root - INFO - Including XRP/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,866 - root - INFO - Including AAVE/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,867 - root - INFO - Including AVAX/USDT on 2025-02-12 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,867 - root - INFO - Including ADA/USDT on 2025-02-12 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,867 - root - INFO - Including LINK/USDT on 2025-02-12 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,867 - root - INFO - Including TRX/USDT on 2025-02-12 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,867 - root - INFO - Including PEPE/USDT on 2025-02-12 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,867 - root - INFO - Including DOGE/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,868 - root - INFO - Including BNB/USDT on 2025-02-12 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,868 - root - INFO - Including DOT/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,873 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:19,876 - root - INFO - Including ETH/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,876 - root - INFO - Including BTC/USDT on 2025-02-13 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,876 - root - INFO - Including SOL/USDT on 2025-02-13 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,876 - root - INFO - Including SUI/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,876 - root - INFO - Including XRP/USDT on 2025-02-13 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,877 - root - INFO - Including AAVE/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,877 - root - INFO - Including AVAX/USDT on 2025-02-13 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,877 - root - INFO - Including ADA/USDT on 2025-02-13 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,877 - root - INFO - Including LINK/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,877 - root - INFO - Including TRX/USDT on 2025-02-13 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,877 - root - INFO - Including PEPE/USDT on 2025-02-13 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,877 - root - INFO - Including DOGE/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,878 - root - INFO - Including BNB/USDT on 2025-02-13 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,878 - root - INFO - Including DOT/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,882 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:19,884 - root - INFO - Including ETH/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,884 - root - INFO - Including BTC/USDT on 2025-02-14 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,884 - root - INFO - Including SOL/USDT on 2025-02-14 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,884 - root - INFO - Including SUI/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,885 - root - INFO - Including XRP/USDT on 2025-02-14 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,885 - root - INFO - Including AAVE/USDT on 2025-02-14 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,885 - root - INFO - Including AVAX/USDT on 2025-02-14 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,885 - root - INFO - Including ADA/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,885 - root - INFO - Including LINK/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,885 - root - INFO - Including TRX/USDT on 2025-02-14 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,885 - root - INFO - Including PEPE/USDT on 2025-02-14 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,886 - root - INFO - Including DOGE/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,886 - root - INFO - Including BNB/USDT on 2025-02-14 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,886 - root - INFO - Including DOT/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:43:19,892 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:19,899 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:19,909 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:19,916 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:19,926 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:19,934 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:19,943 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:19,953 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:19,975 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:19,984 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:19,992 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:19,998 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,007 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,014 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,025 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,032 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,041 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,049 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,056 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,064 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,070 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,079 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,085 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,092 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,098 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,104 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,111 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,116 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,122 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,128 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,134 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,142 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,149 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,156 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,163 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,170 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,177 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,183 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,191 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,197 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,204 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,211 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,217 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,230 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,238 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,245 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,251 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,258 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,264 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,272 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,279 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,286 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,293 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,300 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,307 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,314 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,321 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,329 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,336 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,344 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,350 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,358 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,365 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,371 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,379 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,385 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,392 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,398 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,398 - root - INFO - ASSET CHANGE DETECTED on 2025-04-23:
2025-06-27 21:43:20,398 - root - INFO -    Signal Date: 2025-04-22 (generated at 00:00 UTC)
2025-06-27 21:43:20,398 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-23 00:00 UTC (immediate)
2025-06-27 21:43:20,399 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:43:20,399 - root - INFO -    Buying: ['SOL/USDT']
2025-06-27 21:43:20,399 - root - INFO -    SOL/USDT buy price: $151.1000 (close price)
2025-06-27 21:43:20,405 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,405 - root - INFO - ASSET CHANGE DETECTED on 2025-04-24:
2025-06-27 21:43:20,405 - root - INFO -    Signal Date: 2025-04-23 (generated at 00:00 UTC)
2025-06-27 21:43:20,406 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-24 00:00 UTC (immediate)
2025-06-27 21:43:20,406 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:43:20,406 - root - INFO -    Selling: ['SOL/USDT']
2025-06-27 21:43:20,406 - root - INFO -    Buying: ['SUI/USDT']
2025-06-27 21:43:20,406 - root - INFO -    SUI/USDT buy price: $3.3437 (close price)
2025-06-27 21:43:20,413 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,419 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,427 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,433 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,440 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,446 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,452 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,459 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,465 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,472 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,478 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,484 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,491 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,498 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,505 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,511 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,511 - root - INFO - ASSET CHANGE DETECTED on 2025-05-10:
2025-06-27 21:43:20,512 - root - INFO -    Signal Date: 2025-05-09 (generated at 00:00 UTC)
2025-06-27 21:43:20,512 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-10 00:00 UTC (immediate)
2025-06-27 21:43:20,512 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:43:20,512 - root - INFO -    Selling: ['SUI/USDT']
2025-06-27 21:43:20,512 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-27 21:43:20,513 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-27 21:43:20,523 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,530 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,536 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,544 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,550 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,557 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,563 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,570 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,577 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,583 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,590 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,590 - root - INFO - ASSET CHANGE DETECTED on 2025-05-21:
2025-06-27 21:43:20,590 - root - INFO -    Signal Date: 2025-05-20 (generated at 00:00 UTC)
2025-06-27 21:43:20,590 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-21 00:00 UTC (immediate)
2025-06-27 21:43:20,590 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:43:20,590 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-27 21:43:20,591 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-27 21:43:20,591 - root - INFO -    AAVE/USDT buy price: $247.5400 (close price)
2025-06-27 21:43:20,597 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,603 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,603 - root - INFO - ASSET CHANGE DETECTED on 2025-05-23:
2025-06-27 21:43:20,603 - root - INFO -    Signal Date: 2025-05-22 (generated at 00:00 UTC)
2025-06-27 21:43:20,604 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-23 00:00 UTC (immediate)
2025-06-27 21:43:20,604 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:43:20,604 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-27 21:43:20,604 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-27 21:43:20,605 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-27 21:43:20,612 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,619 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,619 - root - INFO - ASSET CHANGE DETECTED on 2025-05-25:
2025-06-27 21:43:20,619 - root - INFO -    Signal Date: 2025-05-24 (generated at 00:00 UTC)
2025-06-27 21:43:20,620 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-25 00:00 UTC (immediate)
2025-06-27 21:43:20,620 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:43:20,620 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-27 21:43:20,620 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-27 21:43:20,620 - root - INFO -    AAVE/USDT buy price: $269.2800 (close price)
2025-06-27 21:43:20,627 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,633 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,639 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,645 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,651 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,658 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,664 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,670 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,678 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,684 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,691 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,697 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,703 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,710 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,716 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,723 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,730 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,737 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,744 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,750 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,758 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,766 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,774 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,782 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,789 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,796 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,803 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:43:20,803 - root - INFO - ASSET CHANGE DETECTED on 2025-06-21:
2025-06-27 21:43:20,803 - root - INFO -    Signal Date: 2025-06-20 (generated at 00:00 UTC)
2025-06-27 21:43:20,803 - root - INFO -    AUTOMATIC EXECUTION: 2025-06-21 00:00 UTC (immediate)
2025-06-27 21:43:20,803 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:43:20,803 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-27 21:43:20,810 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,816 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,822 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,828 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,834 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:43:20,869 - root - INFO - Entry trade at 2025-04-23 00:00:00+00:00: SOL/USDT
2025-06-27 21:43:20,869 - root - INFO - Swap trade at 2025-04-24 00:00:00+00:00: SOL/USDT -> SUI/USDT
2025-06-27 21:43:20,869 - root - INFO - Swap trade at 2025-05-10 00:00:00+00:00: SUI/USDT -> PEPE/USDT
2025-06-27 21:43:20,870 - root - INFO - Swap trade at 2025-05-21 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-27 21:43:20,870 - root - INFO - Swap trade at 2025-05-23 00:00:00+00:00: AAVE/USDT -> PEPE/USDT
2025-06-27 21:43:20,870 - root - INFO - Swap trade at 2025-05-25 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-27 21:43:20,871 - root - INFO - Exit trade at 2025-06-21 00:00:00+00:00 from AAVE/USDT
2025-06-27 21:43:20,871 - root - INFO - Total trades: 7 (Entries: 1, Exits: 1, Swaps: 5)
2025-06-27 21:43:20,872 - root - INFO - Strategy execution completed in 1s
2025-06-27 21:43:20,872 - root - INFO - DEBUG: self.elapsed_time = 1.0690815448760986 seconds
2025-06-27 21:43:20,883 - root - INFO - Saved allocation history to allocation_history_1d_1d_with_mtpi_no_rebal_independent_imcumbent_2025-02-10.csv
2025-06-27 21:43:20,883 - root - INFO - Strategy equity curve starts at: 2025-02-10
2025-06-27 21:43:20,883 - root - INFO - Assets included in buy-and-hold comparison:
2025-06-27 21:43:20,883 - root - INFO -   ETH/USDT: First available date 2024-12-12
2025-06-27 21:43:20,884 - root - INFO -   BTC/USDT: First available date 2024-12-12
2025-06-27 21:43:20,884 - root - INFO -   SOL/USDT: First available date 2024-12-12
2025-06-27 21:43:20,884 - root - INFO -   SUI/USDT: First available date 2024-12-12
2025-06-27 21:43:20,884 - root - INFO -   XRP/USDT: First available date 2024-12-12
2025-06-27 21:43:20,884 - root - INFO -   AAVE/USDT: First available date 2024-12-12
2025-06-27 21:43:20,884 - root - INFO -   AVAX/USDT: First available date 2024-12-12
2025-06-27 21:43:20,885 - root - INFO -   ADA/USDT: First available date 2024-12-12
2025-06-27 21:43:20,885 - root - INFO -   LINK/USDT: First available date 2024-12-12
2025-06-27 21:43:20,885 - root - INFO -   TRX/USDT: First available date 2024-12-12
2025-06-27 21:43:20,885 - root - INFO -   PEPE/USDT: First available date 2024-12-12
2025-06-27 21:43:20,885 - root - INFO -   DOGE/USDT: First available date 2024-12-12
2025-06-27 21:43:20,886 - root - INFO -   BNB/USDT: First available date 2024-12-12
2025-06-27 21:43:20,886 - root - INFO -   DOT/USDT: First available date 2024-12-12
2025-06-27 21:43:20,886 - root - INFO - Using provided start date for normalization: 2025-02-10 00:00:00+00:00
2025-06-27 21:43:20,887 - root - INFO - Normalized ETH/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:43:20,889 - root - INFO - Normalized BTC/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:43:20,890 - root - INFO - Normalized SOL/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:43:20,892 - root - INFO - Normalized SUI/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:43:20,894 - root - INFO - Normalized XRP/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:43:20,895 - root - INFO - Normalized AAVE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:43:20,896 - root - INFO - Normalized AVAX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:43:20,897 - root - INFO - Normalized ADA/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:43:20,898 - root - INFO - Normalized LINK/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:43:20,900 - root - INFO - Normalized TRX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:43:20,901 - root - INFO - Normalized PEPE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:43:20,902 - root - INFO - Normalized DOGE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:43:20,904 - root - INFO - Normalized BNB/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:43:20,905 - root - INFO - Normalized DOT/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:43:20,908 - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 197 points
2025-06-27 21:43:20,908 - root - INFO - ETH/USDT B&H total return: -9.22%
2025-06-27 21:43:20,910 - root - INFO - Added equity_curve to bnh_metrics for BTC/USDT with 197 points
2025-06-27 21:43:20,911 - root - INFO - BTC/USDT B&H total return: 9.77%
2025-06-27 21:43:20,912 - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 197 points
2025-06-27 21:43:20,913 - root - INFO - SOL/USDT B&H total return: -30.63%
2025-06-27 21:43:20,914 - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 197 points
2025-06-27 21:43:20,915 - root - INFO - SUI/USDT B&H total return: -19.11%
2025-06-27 21:43:20,916 - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 197 points
2025-06-27 21:43:20,917 - root - INFO - XRP/USDT B&H total return: -13.13%
2025-06-27 21:43:20,918 - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 197 points
2025-06-27 21:43:20,919 - root - INFO - AAVE/USDT B&H total return: -0.91%
2025-06-27 21:43:20,920 - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 197 points
2025-06-27 21:43:20,921 - root - INFO - AVAX/USDT B&H total return: -32.88%
2025-06-27 21:43:20,923 - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 197 points
2025-06-27 21:43:20,924 - root - INFO - ADA/USDT B&H total return: -22.16%
2025-06-27 21:43:20,925 - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 197 points
2025-06-27 21:43:20,926 - root - INFO - LINK/USDT B&H total return: -31.21%
2025-06-27 21:43:20,928 - root - INFO - Added equity_curve to bnh_metrics for TRX/USDT with 197 points
2025-06-27 21:43:20,928 - root - INFO - TRX/USDT B&H total return: 10.07%
2025-06-27 21:43:20,930 - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 197 points
2025-06-27 21:43:20,930 - root - INFO - PEPE/USDT B&H total return: -4.48%
2025-06-27 21:43:20,932 - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 197 points
2025-06-27 21:43:20,932 - root - INFO - DOGE/USDT B&H total return: -37.35%
2025-06-27 21:43:20,934 - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 197 points
2025-06-27 21:43:20,934 - root - INFO - BNB/USDT B&H total return: 3.82%
2025-06-27 21:43:20,936 - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 197 points
2025-06-27 21:43:20,936 - root - INFO - DOT/USDT B&H total return: -31.91%
2025-06-27 21:43:20,937 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-27 21:43:20,947 - root - INFO - Configuration loaded successfully.
2025-06-27 21:43:20,959 - root - INFO - Using colored segments for single-asset strategy visualization
2025-06-27 21:43:21,075 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-27 21:43:21,084 - root - INFO - Configuration loaded successfully.
2025-06-27 21:43:23,061 - root - INFO - Added ETH/USDT buy-and-hold curve with 197 points
2025-06-27 21:43:23,061 - root - INFO - Added BTC/USDT buy-and-hold curve with 197 points
2025-06-27 21:43:23,062 - root - INFO - Added SOL/USDT buy-and-hold curve with 197 points
2025-06-27 21:43:23,062 - root - INFO - Added SUI/USDT buy-and-hold curve with 197 points
2025-06-27 21:43:23,062 - root - INFO - Added XRP/USDT buy-and-hold curve with 197 points
2025-06-27 21:43:23,062 - root - INFO - Added AAVE/USDT buy-and-hold curve with 197 points
2025-06-27 21:43:23,062 - root - INFO - Added AVAX/USDT buy-and-hold curve with 197 points
2025-06-27 21:43:23,062 - root - INFO - Added ADA/USDT buy-and-hold curve with 197 points
2025-06-27 21:43:23,063 - root - INFO - Added LINK/USDT buy-and-hold curve with 197 points
2025-06-27 21:43:23,063 - root - INFO - Added TRX/USDT buy-and-hold curve with 197 points
2025-06-27 21:43:23,063 - root - INFO - Added PEPE/USDT buy-and-hold curve with 197 points
2025-06-27 21:43:23,063 - root - INFO - Added DOGE/USDT buy-and-hold curve with 197 points
2025-06-27 21:43:23,063 - root - INFO - Added BNB/USDT buy-and-hold curve with 197 points
2025-06-27 21:43:23,063 - root - INFO - Added DOT/USDT buy-and-hold curve with 197 points
2025-06-27 21:43:23,063 - root - INFO - Added 14 buy-and-hold curves to results
2025-06-27 21:43:23,064 - root - INFO -   - ETH/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:43:23,064 - root - INFO -   - BTC/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:43:23,064 - root - INFO -   - SOL/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:43:23,064 - root - INFO -   - SUI/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:43:23,064 - root - INFO -   - XRP/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:43:23,065 - root - INFO -   - AAVE/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:43:23,065 - root - INFO -   - AVAX/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:43:23,065 - root - INFO -   - ADA/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:43:23,065 - root - INFO -   - LINK/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:43:23,065 - root - INFO -   - TRX/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:43:23,065 - root - INFO -   - PEPE/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:43:23,066 - root - INFO -   - DOGE/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:43:23,066 - root - INFO -   - BNB/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:43:23,066 - root - INFO -   - DOT/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:43:23,084 - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-06-27 21:43:23,084 - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-27 21:43:23,086 - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-06-27 21:43:23,086 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-27 21:43:23,099 - root - INFO - Configuration loaded successfully.
2025-06-27 21:43:23,099 - root - INFO - Loaded MTPI configuration with 8 enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-27 21:43:23,100 - root - INFO - Using MTPI indicators from config: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-27 21:43:23,100 - root - INFO - Combination method: consensus
2025-06-27 21:43:23,100 - root - INFO - Calculated appropriate limit for 1d timeframe: 216 candles (minimum 180.0 candles needed for 60 length indicator)
2025-06-27 21:43:23,100 - root - INFO - Checking cache for 1 symbols (1d)...
2025-06-27 21:43:23,121 - root - INFO - Loaded 2138 rows of BTC/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:43:23,123 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:43:23,124 - root - INFO - Loaded 2138 rows of BTC/USDT data from cache (after filtering).
2025-06-27 21:43:23,124 - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-27 21:43:23,124 - root - INFO - Fetched BTC data: 2138 candles from 2019-08-20 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:43:23,125 - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-06-27 21:43:23,382 - root - INFO - Generated PGO Score signals: {-1: 990, 0: 34, 1: 1114}
2025-06-27 21:43:23,382 - root - INFO - PGO signal: -1
2025-06-27 21:43:23,382 - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-06-27 21:43:23,489 - root - INFO - Generated BB Score signals: {-1: 991, 0: 33, 1: 1114}
2025-06-27 21:43:23,489 - root - INFO - Bollinger Bands signal: -1
2025-06-27 21:43:28,017 - root - INFO - Generated DWMA signals using Weighted SD method
2025-06-27 21:43:28,017 - root - INFO - DWMA Score signal: -1
2025-06-27 21:43:28,533 - root - INFO - Generated DEMA Supertrend signals
2025-06-27 21:43:28,533 - root - INFO - Signal distribution: {-1: 1255, 0: 1, 1: 882}
2025-06-27 21:43:28,533 - root - INFO - Generated DEMA Super Score signals
2025-06-27 21:43:28,533 - root - INFO - DEMA Super Score signal: -1
2025-06-27 21:43:29,508 - root - INFO - Generated DPSD signals
2025-06-27 21:43:29,508 - root - INFO - Signal distribution: {-1: 1081, 0: 87, 1: 970}
2025-06-27 21:43:29,508 - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-06-27 21:43:29,508 - root - INFO - DPSD Score signal: -1
2025-06-27 21:43:29,588 - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-06-27 21:43:29,588 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:43:29,588 - root - INFO - Generated AAD Score signals using SMA method
2025-06-27 21:43:29,588 - root - INFO - AAD Score signal: -1
2025-06-27 21:43:30,094 - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-06-27 21:43:30,094 - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-06-27 21:43:30,094 - root - INFO - Dynamic EMA Score signal: -1
2025-06-27 21:43:30,970 - root - INFO - Quantile DEMA Score signal: -1
2025-06-27 21:43:30,971 - root - INFO - Individual signals: {'pgo': -1, 'bollinger_bands': -1, 'dwma_score': -1, 'dema_super_score': -1, 'dpsd_score': -1, 'aad_score': -1, 'dynamic_ema_score': -1, 'quantile_dema_score': -1}
2025-06-27 21:43:30,971 - root - INFO - Combined MTPI signal (consensus): -1
2025-06-27 21:43:30,971 - root - INFO - MTPI Score: -1.000000
2025-06-27 21:43:30,971 - root - INFO - Added current MTPI score to results: -1.000000 (using 1d timeframe)
2025-06-27 21:43:30,971 - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-06-27 21:43:30,971 - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 8.0, 'BTC/USDT': 13.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-27 21:43:30,971 - root - ERROR - [DEBUG] CONVERSION - Original USDT scores: {'ETH/USDT': 8.0, 'BTC/USDT': 13.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-27 21:43:30,971 - root - ERROR - [DEBUG] CONVERSION - Original USDT keys order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-27 21:43:30,971 - root - ERROR - [DEBUG] CONVERSION - Conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-27 21:43:30,971 - root - ERROR - [DEBUG] CONVERSION - ETH/USDT -> ETH/EUR (score: 8.0)
2025-06-27 21:43:30,971 - root - ERROR - [DEBUG] CONVERSION - BTC/USDT -> BTC/EUR (score: 13.0)
2025-06-27 21:43:30,971 - root - ERROR - [DEBUG] CONVERSION - SOL/USDT -> SOL/EUR (score: 6.0)
2025-06-27 21:43:30,971 - root - ERROR - [DEBUG] CONVERSION - SUI/USDT -> SUI/EUR (score: 2.0)
2025-06-27 21:43:30,971 - root - ERROR - [DEBUG] CONVERSION - XRP/USDT -> XRP/EUR (score: 9.0)
2025-06-27 21:43:30,971 - root - ERROR - [DEBUG] CONVERSION - AAVE/USDT -> AAVE/EUR (score: 9.0)
2025-06-27 21:43:30,971 - root - ERROR - [DEBUG] CONVERSION - AVAX/USDT -> AVAX/EUR (score: 3.0)
2025-06-27 21:43:30,971 - root - ERROR - [DEBUG] CONVERSION - ADA/USDT -> ADA/EUR (score: 3.0)
2025-06-27 21:43:30,978 - root - ERROR - [DEBUG] CONVERSION - LINK/USDT -> LINK/EUR (score: 7.0)
2025-06-27 21:43:30,978 - root - ERROR - [DEBUG] CONVERSION - TRX/USDT -> TRX/EUR (score: 12.0)
2025-06-27 21:43:30,978 - root - ERROR - [DEBUG] CONVERSION - PEPE/USDT -> PEPE/EUR (score: 0.0)
2025-06-27 21:43:30,978 - root - ERROR - [DEBUG] CONVERSION - DOGE/USDT -> DOGE/EUR (score: 4.0)
2025-06-27 21:43:30,978 - root - ERROR - [DEBUG] CONVERSION - BNB/USDT -> BNB/EUR (score: 11.0)
2025-06-27 21:43:30,978 - root - ERROR - [DEBUG] CONVERSION - DOT/USDT -> DOT/EUR (score: 1.0)
2025-06-27 21:43:30,978 - root - ERROR - [DEBUG] CONVERSION - Final EUR scores: {'ETH/EUR': 8.0, 'BTC/EUR': 13.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 7.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 4.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-27 21:43:30,978 - root - ERROR - [DEBUG] CONVERSION - Final EUR keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-27 21:43:30,978 - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 8.0, 'BTC/EUR': 13.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 7.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 4.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-27 21:43:30,978 - root - INFO - Saved metrics to new file: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250620_run_********_214259.csv
2025-06-27 21:43:30,978 - root - INFO - Saved performance metrics to CSV: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250620_run_********_214259.csv
2025-06-27 21:43:30,988 - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-06-27 21:43:30,988 - root - INFO - Results type: <class 'dict'>
2025-06-27 21:43:30,988 - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-06-27 21:43:30,988 - root - INFO - Success flag set to: True
2025-06-27 21:43:30,988 - root - INFO - Message set to: Strategy calculation completed successfully
2025-06-27 21:43:30,988 - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 197 entries
2025-06-27 21:43:30,988 - root - INFO -   - buy_hold_curves: dict with 14 entries
2025-06-27 21:43:30,988 - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 197 entries
2025-06-27 21:43:30,988 - root - INFO -   - mtpi_signals: <class 'pandas.core.series.Series'> with 257 entries
2025-06-27 21:43:30,988 - root - INFO -   - mtpi_score: <class 'float'>
2025-06-27 21:43:30,988 - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 197 entries
2025-06-27 21:43:30,988 - root - INFO -   - performance_metrics: dict with 3 entries
2025-06-27 21:43:30,988 - root - INFO -   - metrics_file: <class 'str'>
2025-06-27 21:43:30,988 - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-06-27 21:43:30,988 - root - INFO -   - success: <class 'bool'>
2025-06-27 21:43:30,988 - root - INFO -   - message: <class 'str'>
2025-06-27 21:43:30,988 - root - ERROR - [DEBUG] STRATEGY RESULTS - Available keys: ['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message']
2025-06-27 21:43:30,988 - root - INFO - [DEBUG] ASSET SELECTION - Extracted best_asset from best_asset_series: 
2025-06-27 21:43:30,988 - root - INFO - [DEBUG] ASSET SELECTION - Last 5 entries in best_asset_series: 2025-06-22 00:00:00+00:00    
2025-06-23 00:00:00+00:00    
2025-06-24 00:00:00+00:00    
2025-06-25 00:00:00+00:00    
2025-06-26 00:00:00+00:00    
dtype: object
2025-06-27 21:43:30,988 - root - ERROR - [DEBUG] ASSET SELECTION - Latest scores available: {'ETH/EUR': 8.0, 'BTC/EUR': 13.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 7.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 4.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-27 21:43:30,988 - root - ERROR - [DEBUG] ASSET SELECTION - Scores dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-27 21:43:30,988 - root - INFO - [DEBUG] TIE-BREAKING - Strategy: imcumbent
2025-06-27 21:43:30,988 - root - WARNING - [DEBUG] ASSET SELECTION - find_best_asset_for_day() called with 14 assets
2025-06-27 21:43:30,988 - root - WARNING - [DEBUG] ASSET SELECTION - Input scores: {'ETH/EUR': 8.0, 'BTC/EUR': 13.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 7.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 4.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-27 21:43:30,988 - root - WARNING - [DEBUG] ASSET SELECTION - Dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-27 21:43:30,988 - root - INFO - MTPI signal is -1, staying out of the market
2025-06-27 21:43:30,988 - root - ERROR - [DEBUG] ASSET SELECTION - Current day's best asset:  (MTPI signal: -1)
2025-06-27 21:43:30,996 - root - ERROR - [DEBUG] ASSET SELECTION - Maximum score: 13.0
2025-06-27 21:43:30,996 - root - ERROR - [DEBUG] ASSET SELECTION - Assets with max score: ['BTC/EUR']
2025-06-27 21:43:30,997 - root - ERROR - [DEBUG] NO TIE - CONFIRMED SELECTION: 
2025-06-27 21:43:30,997 - root - ERROR - [DEBUG] NO TIE - Single winner: 
2025-06-27 21:43:31,013 - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
       'BNB/EUR', 'DOT/EUR'],
      dtype='object')
2025-06-27 21:43:31,013 - root - INFO - Last row columns: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-27 21:43:31,013 - root - INFO - [DEBUG] FINAL ASSET SELECTION SUMMARY:
2025-06-27 21:43:31,013 - root - INFO - [DEBUG]   - Best asset selected: 
2025-06-27 21:43:31,013 - root - INFO - [DEBUG]   - Assets held: {}
2025-06-27 21:43:31,016 - root - INFO - [DEBUG]   - MTPI signal: -1
2025-06-27 21:43:31,016 - root - INFO - [DEBUG]   - Use MTPI signal: True
2025-06-27 21:43:31,016 - root - INFO - MTPI signal is bearish (-1). Exiting all positions.
2025-06-27 21:43:31,016 - root - INFO - No open positions to exit.
2025-06-27 21:43:31,016 - root - INFO - Exit all positions result logged to trade log file
2025-06-27 21:43:31,016 - root - INFO - All positions exited successfully
2025-06-27 21:43:31,024 - root - INFO - Asset selection logged: 0 assets selected with single_asset allocation
2025-06-27 21:43:31,024 - root - INFO - Asset scores (sorted by score):
2025-06-27 21:43:31,024 - root - INFO -   BTC/EUR: score=13.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:43:31,024 - root - INFO -   TRX/EUR: score=12.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:43:31,024 - root - INFO -   BNB/EUR: score=11.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:43:31,024 - root - INFO -   XRP/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:43:31,025 - root - INFO -   AAVE/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:43:31,025 - root - INFO -   ETH/EUR: score=8.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:43:31,025 - root - INFO -   LINK/EUR: score=7.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:43:31,025 - root - INFO -   SOL/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:43:31,025 - root - INFO -   DOGE/EUR: score=4.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:43:31,025 - root - INFO -   AVAX/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:43:31,025 - root - INFO -   ADA/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:43:31,025 - root - INFO -   SUI/EUR: score=2.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:43:31,025 - root - INFO -   DOT/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:43:31,025 - root - INFO -   PEPE/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:43:31,025 - root - INFO - Rejected assets:
2025-06-27 21:43:31,025 - root - INFO -   BTC/EUR: reason=Failed to trade, score=13.0, rank=1
2025-06-27 21:43:31,025 - root - WARNING -   HIGH-SCORING ASSET REJECTED: BTC/EUR (rank 1, score 13.0) - Failed to trade
2025-06-27 21:43:31,025 - root - INFO - Asset selection logged with 14 assets scored and 0 assets selected
2025-06-27 21:43:31,025 - root - INFO - Extracted asset scores: {'ETH/EUR': 8.0, 'BTC/EUR': 13.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 7.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 4.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-27 21:43:31,145 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-27 21:43:31,146 - root - INFO - Strategy execution completed successfully in 31.33 seconds
2025-06-27 21:43:31,149 - root - INFO - Saved recovery state to data/state\recovery_state.json
2025-06-27 21:43:39,612 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:43:49,629 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:43:59,678 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:44:09,700 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:44:19,856 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:44:30,607 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:44:40,643 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:44:50,678 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:45:00,721 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:45:10,850 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:45:20,885 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:45:30,924 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:45:40,938 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:45:50,952 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:46:00,960 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:46:10,980 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:46:21,006 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:46:31,022 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:46:41,037 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:46:51,068 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:50:10,092 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 69, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 373, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection_pool.py", line 216, in handle_async_request
    raise exc from None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection_pool.py", line 196, in handle_async_request
    response = await connection.handle_async_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 99, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 76, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 122, in _connect
    stream = await self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_backends\auto.py", line 30, in connect_tcp
    return await self._backend.connect_tcp(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_backends\anyio.py", line 114, in connect_tcp
    with map_exceptions(exc_map):
  File "C:\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\telegram\request\_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1574, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1661, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1689, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1726, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1763, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 372, in handle_async_request
    with map_httpcore_exceptions():
  File "C:\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 86, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
                    ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_httpxrequest.py", line 297, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-06-27 21:50:11,012 - root - WARNING - Network connection lost at 2025-06-27 21:50:11
2025-06-27 21:50:12,337 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 69, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 373, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection_pool.py", line 216, in handle_async_request
    raise exc from None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection_pool.py", line 196, in handle_async_request
    response = await connection.handle_async_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 99, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 76, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 122, in _connect
    stream = await self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_backends\auto.py", line 30, in connect_tcp
    return await self._backend.connect_tcp(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_backends\anyio.py", line 114, in connect_tcp
    with map_exceptions(exc_map):
  File "C:\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\telegram\request\_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1574, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1661, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1689, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1726, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1763, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 372, in handle_async_request
    with map_httpcore_exceptions():
  File "C:\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 86, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
                    ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_httpxrequest.py", line 297, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-06-27 21:50:12,385 - root - INFO - Saving state due to network issues
2025-06-27 21:50:12,423 - root - WARNING - Non-serializable value in state: last_mtpi_signal=-1 (type: <class 'numpy.int32'>)
2025-06-27 21:50:12,438 - root - INFO - Saved state to C:\Users\<USER>\OneDrive\Stalinis kompiuteris\Asset_Screener_Python\data\state\background_service_********_214259.json
2025-06-27 21:50:12,472 - root - WARNING - Failed to send with Markdown formatting: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-06-27 21:50:12,486 - root - WARNING - Network error sending notification (attempt 1/3): httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-06-27 21:50:12,498 - root - INFO - Retrying in 5 seconds...
2025-06-27 21:50:13,846 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 69, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 373, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection_pool.py", line 216, in handle_async_request
    raise exc from None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection_pool.py", line 196, in handle_async_request
    response = await connection.handle_async_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 99, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 76, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 122, in _connect
    stream = await self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_backends\auto.py", line 30, in connect_tcp
    return await self._backend.connect_tcp(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_backends\anyio.py", line 114, in connect_tcp
    with map_exceptions(exc_map):
  File "C:\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\telegram\request\_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1574, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1661, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1689, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1726, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1763, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 372, in handle_async_request
    with map_httpcore_exceptions():
  File "C:\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 86, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
                    ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_httpxrequest.py", line 297, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-06-27 22:03:13,539 - root - WARNING - Failed to send with Markdown formatting: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-06-27 22:03:13,599 - root - WARNING - Network error sending notification (attempt 2/3): httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-06-27 22:03:13,652 - root - INFO - Retrying in 10 seconds...
2025-06-27 22:03:15,007 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 69, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 373, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection_pool.py", line 216, in handle_async_request
    raise exc from None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection_pool.py", line 196, in handle_async_request
    response = await connection.handle_async_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 99, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 76, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 122, in _connect
    stream = await self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_backends\auto.py", line 30, in connect_tcp
    return await self._backend.connect_tcp(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_backends\anyio.py", line 114, in connect_tcp
    with map_exceptions(exc_map):
  File "C:\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\telegram\request\_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1574, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1661, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1689, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1726, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1763, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 372, in handle_async_request
    with map_httpcore_exceptions():
  File "C:\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 86, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
                    ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_httpxrequest.py", line 297, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-06-27 22:03:18,449 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 69, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 373, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection_pool.py", line 216, in handle_async_request
    raise exc from None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection_pool.py", line 196, in handle_async_request
    response = await connection.handle_async_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 99, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 76, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 122, in _connect
    stream = await self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_backends\auto.py", line 30, in connect_tcp
    return await self._backend.connect_tcp(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_backends\anyio.py", line 114, in connect_tcp
    with map_exceptions(exc_map):
  File "C:\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\telegram\request\_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1574, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1661, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1689, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1726, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1763, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 372, in handle_async_request
    with map_httpcore_exceptions():
  File "C:\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 86, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
                    ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_httpxrequest.py", line 297, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-06-27 22:03:23,643 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 69, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 373, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection_pool.py", line 216, in handle_async_request
    raise exc from None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection_pool.py", line 196, in handle_async_request
    response = await connection.handle_async_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 99, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 76, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 122, in _connect
    stream = await self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_backends\auto.py", line 30, in connect_tcp
    return await self._backend.connect_tcp(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_backends\anyio.py", line 114, in connect_tcp
    with map_exceptions(exc_map):
  File "C:\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\telegram\request\_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1574, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1661, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1689, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1726, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1763, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 372, in handle_async_request
    with map_httpcore_exceptions():
  File "C:\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 86, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
                    ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_httpxrequest.py", line 297, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-06-27 22:03:23,741 - root - WARNING - Failed to send with Markdown formatting: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-06-27 22:03:23,889 - root - ERROR - Failed to send notification: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-06-27 22:03:24,151 - root - INFO - Successfully saved state after connection loss
2025-06-27 22:03:31,353 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 69, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 373, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection_pool.py", line 216, in handle_async_request
    raise exc from None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection_pool.py", line 196, in handle_async_request
    response = await connection.handle_async_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 99, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 76, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 122, in _connect
    stream = await self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_backends\auto.py", line 30, in connect_tcp
    return await self._backend.connect_tcp(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_backends\anyio.py", line 114, in connect_tcp
    with map_exceptions(exc_map):
  File "C:\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\telegram\request\_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1574, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1661, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1689, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1726, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1763, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 372, in handle_async_request
    with map_httpcore_exceptions():
  File "C:\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 86, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
                    ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_httpxrequest.py", line 297, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-06-27 22:03:42,793 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 69, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 373, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection_pool.py", line 216, in handle_async_request
    raise exc from None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection_pool.py", line 196, in handle_async_request
    response = await connection.handle_async_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 99, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 76, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 122, in _connect
    stream = await self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_backends\auto.py", line 30, in connect_tcp
    return await self._backend.connect_tcp(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_backends\anyio.py", line 114, in connect_tcp
    with map_exceptions(exc_map):
  File "C:\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\telegram\request\_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1574, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1661, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1689, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1726, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1763, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 372, in handle_async_request
    with map_httpcore_exceptions():
  File "C:\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 86, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
                    ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_httpxrequest.py", line 297, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-06-27 22:03:59,891 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 69, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 373, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection_pool.py", line 216, in handle_async_request
    raise exc from None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection_pool.py", line 196, in handle_async_request
    response = await connection.handle_async_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 99, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 76, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 122, in _connect
    stream = await self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_backends\auto.py", line 30, in connect_tcp
    return await self._backend.connect_tcp(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_backends\anyio.py", line 114, in connect_tcp
    with map_exceptions(exc_map):
  File "C:\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\telegram\request\_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1574, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1661, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1689, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1726, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1763, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 372, in handle_async_request
    with map_httpcore_exceptions():
  File "C:\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 86, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
                    ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_httpxrequest.py", line 297, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-06-28 09:48:37,529 - root - WARNING - Network is down, skipping scheduled tasks
2025-06-28 09:48:42,289 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 69, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 373, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection_pool.py", line 216, in handle_async_request
    raise exc from None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection_pool.py", line 196, in handle_async_request
    response = await connection.handle_async_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 99, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 76, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 122, in _connect
    stream = await self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_backends\auto.py", line 30, in connect_tcp
    return await self._backend.connect_tcp(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_backends\anyio.py", line 114, in connect_tcp
    with map_exceptions(exc_map):
  File "C:\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\telegram\request\_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1574, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1661, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1689, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1726, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1763, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 372, in handle_async_request
    with map_httpcore_exceptions():
  File "C:\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 86, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
                    ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_httpxrequest.py", line 297, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-06-28 09:49:13,968 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 69, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 373, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection_pool.py", line 216, in handle_async_request
    raise exc from None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection_pool.py", line 196, in handle_async_request
    response = await connection.handle_async_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 99, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 76, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 122, in _connect
    stream = await self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_backends\auto.py", line 30, in connect_tcp
    return await self._backend.connect_tcp(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_backends\anyio.py", line 114, in connect_tcp
    with map_exceptions(exc_map):
  File "C:\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\telegram\request\_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1574, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1661, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1689, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1726, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1763, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 372, in handle_async_request
    with map_httpcore_exceptions():
  File "C:\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 86, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
                    ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_httpxrequest.py", line 297, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-06-28 09:49:43,981 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 69, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 373, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection_pool.py", line 216, in handle_async_request
    raise exc from None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection_pool.py", line 196, in handle_async_request
    response = await connection.handle_async_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 99, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 76, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 122, in _connect
    stream = await self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_backends\auto.py", line 30, in connect_tcp
    return await self._backend.connect_tcp(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_backends\anyio.py", line 114, in connect_tcp
    with map_exceptions(exc_map):
  File "C:\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\telegram\request\_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1574, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1661, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1689, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1726, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1763, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 372, in handle_async_request
    with map_httpcore_exceptions():
  File "C:\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 86, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
                    ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_httpxrequest.py", line 297, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-06-28 09:50:13,984 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 69, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 373, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection_pool.py", line 216, in handle_async_request
    raise exc from None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection_pool.py", line 196, in handle_async_request
    response = await connection.handle_async_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 99, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 76, in handle_async_request
    stream = await self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 122, in _connect
    stream = await self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_backends\auto.py", line 30, in connect_tcp
    return await self._backend.connect_tcp(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_backends\anyio.py", line 114, in connect_tcp
    with map_exceptions(exc_map):
  File "C:\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\telegram\request\_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1574, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1661, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1689, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1726, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1763, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 372, in handle_async_request
    with map_httpcore_exceptions():
  File "C:\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 86, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 11001] getaddrinfo failed

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
                    ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_httpxrequest.py", line 297, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-06-28 09:50:31,653 - root - INFO - Network connection restored at 2025-06-28 09:50:31
2025-06-28 09:50:31,654 - root - INFO - Connection was down for approximately 43220.6 seconds
2025-06-28 09:50:31,655 - root - INFO - Network recovery callback triggered after 43220.6 seconds downtime
2025-06-28 09:50:32,103 - root - WARNING - Missed execution detected! Expected at 2025-06-28 00:01:00, current time is 2025-06-28 09:50:32.103823
2025-06-28 09:50:32,110 - root - INFO - Saved recovery state to data/state\recovery_state.json
2025-06-28 09:50:32,111 - root - INFO - Processing pending operation: missed_execution
2025-06-28 09:50:32,111 - root - INFO - Recovering from missed execution: {'expected_time': '2025-06-28T00:01:00', 'current_time': '2025-06-28T09:50:32.103823', 'timeframe': '1d'}
2025-06-28 09:50:32,111 - root - INFO - Executing strategy (run #2)...
2025-06-28 09:50:32,112 - root - INFO - New day detected. Resetting daily trade counters.
2025-06-28 09:50:32,112 - root - INFO - Resetting daily trade counters for this strategy execution
2025-06-28 09:50:32,112 - root - INFO - No trades recorded today (Max: 5)
2025-06-28 09:50:32,113 - root - INFO - Initialized daily trades counter for 2025-06-28
2025-06-28 09:50:32,113 - root - INFO - Creating snapshot for candle timestamp: 20250628
2025-06-28 09:50:32,191 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-28 09:50:32,194 - root - INFO - Using trend method 'PGO For Loop' for strategy execution
2025-06-28 09:50:32,195 - root - INFO - Using configured start date for 1d timeframe: 2025-02-10
2025-06-28 09:50:32,195 - root - INFO - Using recent date for performance tracking: 2025-06-21
2025-06-28 09:50:32,199 - root - INFO - Using real-time data fetching - only fetching new data since last cached timestamp
2025-06-28 09:50:32,333 - root - INFO - Loaded 2138 rows of ETH/USDT data from cache (last updated: 2025-06-27)
2025-06-28 09:50:32,334 - root - INFO - Last timestamp in cache for ETH/USDT: 2025-06-26 00:00:00+00:00
2025-06-28 09:50:32,342 - root - INFO - Expected last timestamp for ETH/USDT: 2025-06-27 00:00:00+00:00
2025-06-28 09:50:32,342 - root - INFO - Missing data detected for ETH/USDT. Need to fetch data from 2025-06-26 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:35,024 - root - INFO - Fetched 3 candles for ETH/USDT
2025-06-28 09:50:35,039 - root - INFO - Filtered out 1 incomplete daily candles for current date 2025-06-28
2025-06-28 09:50:35,039 - root - INFO - Filtered out 1 incomplete candles
2025-06-28 09:50:35,039 - root - INFO - Filtered out 1 incomplete candles for ETH/USDT
2025-06-28 09:50:35,040 - root - INFO - Prepared 1 new candles for ETH/USDT from 2025-06-27 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:35,040 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:35,057 - root - INFO - Appended 1 rows to ETH/USDT cache file from 2025-06-27 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:35,057 - root - INFO - Successfully appended 1 candles for ETH/USDT
2025-06-28 09:50:35,109 - root - INFO - Loaded 2139 rows of ETH/USDT data from cache (last updated: 2025-06-28)
2025-06-28 09:50:35,109 - root - INFO - Cache now has 2139 rows for ETH/USDT
2025-06-28 09:50:35,109 - root - INFO - New last timestamp for ETH/USDT: 2025-06-27 00:00:00+00:00
2025-06-28 09:50:35,110 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:35,131 - root - INFO - Loaded 2138 rows of BTC/USDT data from cache (last updated: 2025-06-27)
2025-06-28 09:50:35,131 - root - INFO - Last timestamp in cache for BTC/USDT: 2025-06-26 00:00:00+00:00
2025-06-28 09:50:35,132 - root - INFO - Expected last timestamp for BTC/USDT: 2025-06-27 00:00:00+00:00
2025-06-28 09:50:35,132 - root - INFO - Missing data detected for BTC/USDT. Need to fetch data from 2025-06-26 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:35,365 - root - INFO - Fetched 3 candles for BTC/USDT
2025-06-28 09:50:35,367 - root - INFO - Filtered out 1 incomplete daily candles for current date 2025-06-28
2025-06-28 09:50:35,367 - root - INFO - Filtered out 1 incomplete candles
2025-06-28 09:50:35,367 - root - INFO - Filtered out 1 incomplete candles for BTC/USDT
2025-06-28 09:50:35,368 - root - INFO - Prepared 1 new candles for BTC/USDT from 2025-06-27 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:35,368 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:35,378 - root - INFO - Appended 1 rows to BTC/USDT cache file from 2025-06-27 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:35,379 - root - INFO - Successfully appended 1 candles for BTC/USDT
2025-06-28 09:50:35,430 - root - INFO - Loaded 2139 rows of BTC/USDT data from cache (last updated: 2025-06-28)
2025-06-28 09:50:35,431 - root - INFO - Cache now has 2139 rows for BTC/USDT
2025-06-28 09:50:35,431 - root - INFO - New last timestamp for BTC/USDT: 2025-06-27 00:00:00+00:00
2025-06-28 09:50:35,433 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:35,451 - root - INFO - Loaded 1781 rows of SOL/USDT data from cache (last updated: 2025-06-27)
2025-06-28 09:50:35,451 - root - INFO - Last timestamp in cache for SOL/USDT: 2025-06-26 00:00:00+00:00
2025-06-28 09:50:35,451 - root - INFO - Expected last timestamp for SOL/USDT: 2025-06-27 00:00:00+00:00
2025-06-28 09:50:35,452 - root - INFO - Missing data detected for SOL/USDT. Need to fetch data from 2025-06-26 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:35,679 - root - INFO - Fetched 3 candles for SOL/USDT
2025-06-28 09:50:35,681 - root - INFO - Filtered out 1 incomplete daily candles for current date 2025-06-28
2025-06-28 09:50:35,681 - root - INFO - Filtered out 1 incomplete candles
2025-06-28 09:50:35,681 - root - INFO - Filtered out 1 incomplete candles for SOL/USDT
2025-06-28 09:50:35,682 - root - INFO - Prepared 1 new candles for SOL/USDT from 2025-06-27 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:35,682 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:35,695 - root - INFO - Appended 1 rows to SOL/USDT cache file from 2025-06-27 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:35,696 - root - INFO - Successfully appended 1 candles for SOL/USDT
2025-06-28 09:50:35,755 - root - INFO - Loaded 1782 rows of SOL/USDT data from cache (last updated: 2025-06-28)
2025-06-28 09:50:35,755 - root - INFO - Cache now has 1782 rows for SOL/USDT
2025-06-28 09:50:35,756 - root - INFO - New last timestamp for SOL/USDT: 2025-06-27 00:00:00+00:00
2025-06-28 09:50:35,758 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:35,771 - root - INFO - Loaded 786 rows of SUI/USDT data from cache (last updated: 2025-06-27)
2025-06-28 09:50:35,772 - root - INFO - Last timestamp in cache for SUI/USDT: 2025-06-26 00:00:00+00:00
2025-06-28 09:50:35,772 - root - INFO - Expected last timestamp for SUI/USDT: 2025-06-27 00:00:00+00:00
2025-06-28 09:50:35,773 - root - INFO - Missing data detected for SUI/USDT. Need to fetch data from 2025-06-26 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:36,001 - root - INFO - Fetched 3 candles for SUI/USDT
2025-06-28 09:50:36,004 - root - INFO - Filtered out 1 incomplete daily candles for current date 2025-06-28
2025-06-28 09:50:36,004 - root - INFO - Filtered out 1 incomplete candles
2025-06-28 09:50:36,004 - root - INFO - Filtered out 1 incomplete candles for SUI/USDT
2025-06-28 09:50:36,005 - root - INFO - Prepared 1 new candles for SUI/USDT from 2025-06-27 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:36,005 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:36,016 - root - INFO - Appended 1 rows to SUI/USDT cache file from 2025-06-27 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:36,017 - root - INFO - Successfully appended 1 candles for SUI/USDT
2025-06-28 09:50:36,049 - root - INFO - Loaded 787 rows of SUI/USDT data from cache (last updated: 2025-06-28)
2025-06-28 09:50:36,050 - root - INFO - Cache now has 787 rows for SUI/USDT
2025-06-28 09:50:36,050 - root - INFO - New last timestamp for SUI/USDT: 2025-06-27 00:00:00+00:00
2025-06-28 09:50:36,052 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:36,072 - root - INFO - Loaded 2138 rows of XRP/USDT data from cache (last updated: 2025-06-27)
2025-06-28 09:50:36,072 - root - INFO - Last timestamp in cache for XRP/USDT: 2025-06-26 00:00:00+00:00
2025-06-28 09:50:36,072 - root - INFO - Expected last timestamp for XRP/USDT: 2025-06-27 00:00:00+00:00
2025-06-28 09:50:36,073 - root - INFO - Missing data detected for XRP/USDT. Need to fetch data from 2025-06-26 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:36,352 - root - INFO - Fetched 3 candles for XRP/USDT
2025-06-28 09:50:36,354 - root - INFO - Filtered out 1 incomplete daily candles for current date 2025-06-28
2025-06-28 09:50:36,355 - root - INFO - Filtered out 1 incomplete candles
2025-06-28 09:50:36,355 - root - INFO - Filtered out 1 incomplete candles for XRP/USDT
2025-06-28 09:50:36,355 - root - INFO - Prepared 1 new candles for XRP/USDT from 2025-06-27 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:36,355 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:36,370 - root - INFO - Appended 1 rows to XRP/USDT cache file from 2025-06-27 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:36,372 - root - INFO - Successfully appended 1 candles for XRP/USDT
2025-06-28 09:50:36,436 - root - INFO - Loaded 2139 rows of XRP/USDT data from cache (last updated: 2025-06-28)
2025-06-28 09:50:36,438 - root - INFO - Cache now has 2139 rows for XRP/USDT
2025-06-28 09:50:36,438 - root - INFO - New last timestamp for XRP/USDT: 2025-06-27 00:00:00+00:00
2025-06-28 09:50:36,439 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:36,458 - root - INFO - Loaded 1716 rows of AAVE/USDT data from cache (last updated: 2025-06-27)
2025-06-28 09:50:36,459 - root - INFO - Last timestamp in cache for AAVE/USDT: 2025-06-26 00:00:00+00:00
2025-06-28 09:50:36,460 - root - INFO - Expected last timestamp for AAVE/USDT: 2025-06-27 00:00:00+00:00
2025-06-28 09:50:36,460 - root - INFO - Missing data detected for AAVE/USDT. Need to fetch data from 2025-06-26 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:36,750 - root - INFO - Fetched 3 candles for AAVE/USDT
2025-06-28 09:50:36,752 - root - INFO - Filtered out 1 incomplete daily candles for current date 2025-06-28
2025-06-28 09:50:36,753 - root - INFO - Filtered out 1 incomplete candles
2025-06-28 09:50:36,753 - root - INFO - Filtered out 1 incomplete candles for AAVE/USDT
2025-06-28 09:50:36,753 - root - INFO - Prepared 1 new candles for AAVE/USDT from 2025-06-27 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:36,754 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:36,762 - root - INFO - Appended 1 rows to AAVE/USDT cache file from 2025-06-27 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:36,763 - root - INFO - Successfully appended 1 candles for AAVE/USDT
2025-06-28 09:50:36,819 - root - INFO - Loaded 1717 rows of AAVE/USDT data from cache (last updated: 2025-06-28)
2025-06-28 09:50:36,819 - root - INFO - Cache now has 1717 rows for AAVE/USDT
2025-06-28 09:50:36,820 - root - INFO - New last timestamp for AAVE/USDT: 2025-06-27 00:00:00+00:00
2025-06-28 09:50:36,821 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:36,852 - root - INFO - Loaded 1739 rows of AVAX/USDT data from cache (last updated: 2025-06-27)
2025-06-28 09:50:36,852 - root - INFO - Last timestamp in cache for AVAX/USDT: 2025-06-26 00:00:00+00:00
2025-06-28 09:50:36,853 - root - INFO - Expected last timestamp for AVAX/USDT: 2025-06-27 00:00:00+00:00
2025-06-28 09:50:36,854 - root - INFO - Missing data detected for AVAX/USDT. Need to fetch data from 2025-06-26 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:37,090 - root - INFO - Fetched 3 candles for AVAX/USDT
2025-06-28 09:50:37,092 - root - INFO - Filtered out 1 incomplete daily candles for current date 2025-06-28
2025-06-28 09:50:37,093 - root - INFO - Filtered out 1 incomplete candles
2025-06-28 09:50:37,093 - root - INFO - Filtered out 1 incomplete candles for AVAX/USDT
2025-06-28 09:50:37,093 - root - INFO - Prepared 1 new candles for AVAX/USDT from 2025-06-27 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:37,093 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:37,105 - root - INFO - Appended 1 rows to AVAX/USDT cache file from 2025-06-27 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:37,105 - root - INFO - Successfully appended 1 candles for AVAX/USDT
2025-06-28 09:50:37,147 - root - INFO - Loaded 1740 rows of AVAX/USDT data from cache (last updated: 2025-06-28)
2025-06-28 09:50:37,148 - root - INFO - Cache now has 1740 rows for AVAX/USDT
2025-06-28 09:50:37,148 - root - INFO - New last timestamp for AVAX/USDT: 2025-06-27 00:00:00+00:00
2025-06-28 09:50:37,149 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:37,172 - root - INFO - Loaded 2138 rows of ADA/USDT data from cache (last updated: 2025-06-27)
2025-06-28 09:50:37,172 - root - INFO - Last timestamp in cache for ADA/USDT: 2025-06-26 00:00:00+00:00
2025-06-28 09:50:37,173 - root - INFO - Expected last timestamp for ADA/USDT: 2025-06-27 00:00:00+00:00
2025-06-28 09:50:37,173 - root - INFO - Missing data detected for ADA/USDT. Need to fetch data from 2025-06-26 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:37,408 - root - INFO - Fetched 3 candles for ADA/USDT
2025-06-28 09:50:37,412 - root - INFO - Filtered out 1 incomplete daily candles for current date 2025-06-28
2025-06-28 09:50:37,412 - root - INFO - Filtered out 1 incomplete candles
2025-06-28 09:50:37,412 - root - INFO - Filtered out 1 incomplete candles for ADA/USDT
2025-06-28 09:50:37,413 - root - INFO - Prepared 1 new candles for ADA/USDT from 2025-06-27 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:37,413 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:37,424 - root - INFO - Appended 1 rows to ADA/USDT cache file from 2025-06-27 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:37,424 - root - INFO - Successfully appended 1 candles for ADA/USDT
2025-06-28 09:50:37,479 - root - INFO - Loaded 2139 rows of ADA/USDT data from cache (last updated: 2025-06-28)
2025-06-28 09:50:37,480 - root - INFO - Cache now has 2139 rows for ADA/USDT
2025-06-28 09:50:37,480 - root - INFO - New last timestamp for ADA/USDT: 2025-06-27 00:00:00+00:00
2025-06-28 09:50:37,481 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:37,518 - root - INFO - Loaded 2138 rows of LINK/USDT data from cache (last updated: 2025-06-27)
2025-06-28 09:50:37,518 - root - INFO - Last timestamp in cache for LINK/USDT: 2025-06-26 00:00:00+00:00
2025-06-28 09:50:37,519 - root - INFO - Expected last timestamp for LINK/USDT: 2025-06-27 00:00:00+00:00
2025-06-28 09:50:37,519 - root - INFO - Missing data detected for LINK/USDT. Need to fetch data from 2025-06-26 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:37,748 - root - INFO - Fetched 3 candles for LINK/USDT
2025-06-28 09:50:37,752 - root - INFO - Filtered out 1 incomplete daily candles for current date 2025-06-28
2025-06-28 09:50:37,752 - root - INFO - Filtered out 1 incomplete candles
2025-06-28 09:50:37,752 - root - INFO - Filtered out 1 incomplete candles for LINK/USDT
2025-06-28 09:50:37,753 - root - INFO - Prepared 1 new candles for LINK/USDT from 2025-06-27 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:37,753 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:37,771 - root - INFO - Appended 1 rows to LINK/USDT cache file from 2025-06-27 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:37,771 - root - INFO - Successfully appended 1 candles for LINK/USDT
2025-06-28 09:50:37,820 - root - INFO - Loaded 2139 rows of LINK/USDT data from cache (last updated: 2025-06-28)
2025-06-28 09:50:37,820 - root - INFO - Cache now has 2139 rows for LINK/USDT
2025-06-28 09:50:37,821 - root - INFO - New last timestamp for LINK/USDT: 2025-06-27 00:00:00+00:00
2025-06-28 09:50:37,821 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:37,842 - root - INFO - Loaded 2138 rows of TRX/USDT data from cache (last updated: 2025-06-27)
2025-06-28 09:50:37,843 - root - INFO - Last timestamp in cache for TRX/USDT: 2025-06-26 00:00:00+00:00
2025-06-28 09:50:37,843 - root - INFO - Expected last timestamp for TRX/USDT: 2025-06-27 00:00:00+00:00
2025-06-28 09:50:37,843 - root - INFO - Missing data detected for TRX/USDT. Need to fetch data from 2025-06-26 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:38,072 - root - INFO - Fetched 3 candles for TRX/USDT
2025-06-28 09:50:38,074 - root - INFO - Filtered out 1 incomplete daily candles for current date 2025-06-28
2025-06-28 09:50:38,074 - root - INFO - Filtered out 1 incomplete candles
2025-06-28 09:50:38,074 - root - INFO - Filtered out 1 incomplete candles for TRX/USDT
2025-06-28 09:50:38,074 - root - INFO - Prepared 1 new candles for TRX/USDT from 2025-06-27 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:38,075 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:38,081 - root - INFO - Appended 1 rows to TRX/USDT cache file from 2025-06-27 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:38,082 - root - INFO - Successfully appended 1 candles for TRX/USDT
2025-06-28 09:50:38,122 - root - INFO - Loaded 2139 rows of TRX/USDT data from cache (last updated: 2025-06-28)
2025-06-28 09:50:38,122 - root - INFO - Cache now has 2139 rows for TRX/USDT
2025-06-28 09:50:38,123 - root - INFO - New last timestamp for TRX/USDT: 2025-06-27 00:00:00+00:00
2025-06-28 09:50:38,123 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:38,137 - root - INFO - Loaded 784 rows of PEPE/USDT data from cache (last updated: 2025-06-27)
2025-06-28 09:50:38,138 - root - INFO - Last timestamp in cache for PEPE/USDT: 2025-06-26 00:00:00+00:00
2025-06-28 09:50:38,138 - root - INFO - Expected last timestamp for PEPE/USDT: 2025-06-27 00:00:00+00:00
2025-06-28 09:50:38,138 - root - INFO - Missing data detected for PEPE/USDT. Need to fetch data from 2025-06-26 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:38,364 - root - INFO - Fetched 3 candles for PEPE/USDT
2025-06-28 09:50:38,367 - root - INFO - Filtered out 1 incomplete daily candles for current date 2025-06-28
2025-06-28 09:50:38,367 - root - INFO - Filtered out 1 incomplete candles
2025-06-28 09:50:38,368 - root - INFO - Filtered out 1 incomplete candles for PEPE/USDT
2025-06-28 09:50:38,368 - root - INFO - Prepared 1 new candles for PEPE/USDT from 2025-06-27 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:38,369 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:38,390 - root - INFO - Appended 1 rows to PEPE/USDT cache file from 2025-06-27 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:38,391 - root - INFO - Successfully appended 1 candles for PEPE/USDT
2025-06-28 09:50:38,448 - root - INFO - Loaded 785 rows of PEPE/USDT data from cache (last updated: 2025-06-28)
2025-06-28 09:50:38,451 - root - INFO - Cache now has 785 rows for PEPE/USDT
2025-06-28 09:50:38,452 - root - INFO - New last timestamp for PEPE/USDT: 2025-06-27 00:00:00+00:00
2025-06-28 09:50:38,455 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:38,486 - root - INFO - Loaded 2138 rows of DOGE/USDT data from cache (last updated: 2025-06-27)
2025-06-28 09:50:38,486 - root - INFO - Last timestamp in cache for DOGE/USDT: 2025-06-26 00:00:00+00:00
2025-06-28 09:50:38,486 - root - INFO - Expected last timestamp for DOGE/USDT: 2025-06-27 00:00:00+00:00
2025-06-28 09:50:38,486 - root - INFO - Missing data detected for DOGE/USDT. Need to fetch data from 2025-06-26 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:38,721 - root - INFO - Fetched 3 candles for DOGE/USDT
2025-06-28 09:50:38,725 - root - INFO - Filtered out 1 incomplete daily candles for current date 2025-06-28
2025-06-28 09:50:38,726 - root - INFO - Filtered out 1 incomplete candles
2025-06-28 09:50:38,726 - root - INFO - Filtered out 1 incomplete candles for DOGE/USDT
2025-06-28 09:50:38,727 - root - INFO - Prepared 1 new candles for DOGE/USDT from 2025-06-27 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:38,728 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:38,745 - root - INFO - Appended 1 rows to DOGE/USDT cache file from 2025-06-27 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:38,746 - root - INFO - Successfully appended 1 candles for DOGE/USDT
2025-06-28 09:50:38,809 - root - INFO - Loaded 2139 rows of DOGE/USDT data from cache (last updated: 2025-06-28)
2025-06-28 09:50:38,810 - root - INFO - Cache now has 2139 rows for DOGE/USDT
2025-06-28 09:50:38,810 - root - INFO - New last timestamp for DOGE/USDT: 2025-06-27 00:00:00+00:00
2025-06-28 09:50:38,811 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:38,838 - root - INFO - Loaded 2138 rows of BNB/USDT data from cache (last updated: 2025-06-27)
2025-06-28 09:50:38,838 - root - INFO - Last timestamp in cache for BNB/USDT: 2025-06-26 00:00:00+00:00
2025-06-28 09:50:38,839 - root - INFO - Expected last timestamp for BNB/USDT: 2025-06-27 00:00:00+00:00
2025-06-28 09:50:38,839 - root - INFO - Missing data detected for BNB/USDT. Need to fetch data from 2025-06-26 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:39,069 - root - INFO - Fetched 3 candles for BNB/USDT
2025-06-28 09:50:39,071 - root - INFO - Filtered out 1 incomplete daily candles for current date 2025-06-28
2025-06-28 09:50:39,072 - root - INFO - Filtered out 1 incomplete candles
2025-06-28 09:50:39,072 - root - INFO - Filtered out 1 incomplete candles for BNB/USDT
2025-06-28 09:50:39,072 - root - INFO - Prepared 1 new candles for BNB/USDT from 2025-06-27 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:39,073 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:39,082 - root - INFO - Appended 1 rows to BNB/USDT cache file from 2025-06-27 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:39,083 - root - INFO - Successfully appended 1 candles for BNB/USDT
2025-06-28 09:50:39,131 - root - INFO - Loaded 2139 rows of BNB/USDT data from cache (last updated: 2025-06-28)
2025-06-28 09:50:39,131 - root - INFO - Cache now has 2139 rows for BNB/USDT
2025-06-28 09:50:39,131 - root - INFO - New last timestamp for BNB/USDT: 2025-06-27 00:00:00+00:00
2025-06-28 09:50:39,132 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:39,153 - root - INFO - Loaded 1774 rows of DOT/USDT data from cache (last updated: 2025-06-27)
2025-06-28 09:50:39,153 - root - INFO - Last timestamp in cache for DOT/USDT: 2025-06-26 00:00:00+00:00
2025-06-28 09:50:39,153 - root - INFO - Expected last timestamp for DOT/USDT: 2025-06-27 00:00:00+00:00
2025-06-28 09:50:39,154 - root - INFO - Missing data detected for DOT/USDT. Need to fetch data from 2025-06-26 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:39,385 - root - INFO - Fetched 3 candles for DOT/USDT
2025-06-28 09:50:39,387 - root - INFO - Filtered out 1 incomplete daily candles for current date 2025-06-28
2025-06-28 09:50:39,388 - root - INFO - Filtered out 1 incomplete candles
2025-06-28 09:50:39,388 - root - INFO - Filtered out 1 incomplete candles for DOT/USDT
2025-06-28 09:50:39,389 - root - INFO - Prepared 1 new candles for DOT/USDT from 2025-06-27 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:39,389 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:39,400 - root - INFO - Appended 1 rows to DOT/USDT cache file from 2025-06-27 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:39,400 - root - INFO - Successfully appended 1 candles for DOT/USDT
2025-06-28 09:50:39,470 - root - INFO - Loaded 1775 rows of DOT/USDT data from cache (last updated: 2025-06-28)
2025-06-28 09:50:39,471 - root - INFO - Cache now has 1775 rows for DOT/USDT
2025-06-28 09:50:39,471 - root - INFO - New last timestamp for DOT/USDT: 2025-06-27 00:00:00+00:00
2025-06-28 09:50:39,472 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:39,536 - root - INFO - Using 14 trend assets (USDT) for analysis and 14 trading assets (EUR) for execution
2025-06-28 09:50:39,536 - root - INFO - MTPI Multi-Indicator Configuration:
2025-06-28 09:50:39,537 - root - INFO -   - Number of indicators: 8
2025-06-28 09:50:39,537 - root - INFO -   - Enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-28 09:50:39,537 - root - INFO -   - Combination method: consensus
2025-06-28 09:50:39,537 - root - INFO -   - Long threshold: 0.1
2025-06-28 09:50:39,537 - root - INFO -   - Short threshold: -0.1
2025-06-28 09:50:39,538 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trend_assets order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-28 09:50:39,538 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trading_assets order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-28 09:50:39,538 - root - ERROR - [DEBUG] BACKGROUND SERVICE - BTC position in trend_assets: 2
2025-06-28 09:50:39,538 - root - ERROR - [DEBUG] BACKGROUND SERVICE - TRX position in trend_assets: 10
2025-06-28 09:50:39,538 - root - INFO - === RUNNING STRATEGY FOR WEB USING TEST_ALLOCATION ===
2025-06-28 09:50:39,538 - root - INFO - Parameters: use_mtpi_signal=True, mtpi_indicator_type=PGO
2025-06-28 09:50:39,538 - root - INFO - mtpi_timeframe=1d, mtpi_pgo_length=35
2025-06-28 09:50:39,538 - root - INFO - mtpi_upper_threshold=1.1, mtpi_lower_threshold=-0.58
2025-06-28 09:50:39,539 - root - INFO - timeframe=1d, analysis_start_date='2025-02-10'
2025-06-28 09:50:39,539 - root - INFO - n_assets=1, use_weighted_allocation=False
2025-06-28 09:50:39,539 - root - INFO - Using provided trend method: PGO For Loop
2025-06-28 09:50:39,540 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-28 09:50:39,560 - root - INFO - Configuration loaded successfully.
2025-06-28 09:50:39,560 - root - INFO - Saving configuration to config/settings_bitvavo_eur.yaml...
2025-06-28 09:50:39,577 - root - INFO - Configuration saved successfully.
2025-06-28 09:50:39,577 - root - INFO - Trend detection assets (USDT): ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-28 09:50:39,578 - root - INFO - Number of trend detection assets: 14
2025-06-28 09:50:39,579 - root - INFO - Selected assets type: <class 'list'>
2025-06-28 09:50:39,579 - root - INFO - Trading execution assets (EUR): ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-28 09:50:39,579 - root - INFO - Number of trading assets: 14
2025-06-28 09:50:39,580 - root - INFO - Trading assets type: <class 'list'>
2025-06-28 09:50:39,580 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-28 09:50:39,602 - root - INFO - Configuration loaded successfully.
2025-06-28 09:50:39,602 - root - INFO - Execution context: backtesting
2025-06-28 09:50:39,603 - root - INFO - Execution timing: candle_close
2025-06-28 09:50:39,603 - root - INFO - Ratio calculation method: independent
2025-06-28 09:50:39,603 - root - INFO - Tie-breaking strategy: imcumbent
2025-06-28 09:50:39,603 - root - INFO - Asset trend PGO parameters: length=None, upper_threshold=None, lower_threshold=None
2025-06-28 09:50:39,603 - root - INFO - MTPI indicators override: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-28 09:50:39,603 - root - INFO - MTPI combination method override: consensus
2025-06-28 09:50:39,603 - root - INFO - MTPI long threshold override: 0.1
2025-06-28 09:50:39,603 - root - INFO - MTPI short threshold override: -0.1
2025-06-28 09:50:39,603 - root - INFO - Using standard warmup period of 60 days for equal allocation
2025-06-28 09:50:39,603 - root - INFO - Fetching data from 2024-12-12 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-28 09:50:39,605 - root - INFO - Loaded metadata for 48 assets
2025-06-28 09:50:39,606 - root - INFO - Loaded metadata for 48 assets
2025-06-28 09:50:39,607 - root - INFO - Loaded metadata for 48 assets
2025-06-28 09:50:39,608 - root - INFO - Loaded metadata for 48 assets
2025-06-28 09:50:39,610 - root - INFO - Loaded metadata for 48 assets
2025-06-28 09:50:39,611 - root - INFO - Loaded metadata for 48 assets
2025-06-28 09:50:39,611 - root - INFO - Loaded metadata for 48 assets
2025-06-28 09:50:39,612 - root - INFO - Loaded metadata for 48 assets
2025-06-28 09:50:39,612 - root - INFO - Loaded metadata for 48 assets
2025-06-28 09:50:39,613 - root - INFO - Loaded metadata for 48 assets
2025-06-28 09:50:39,613 - root - INFO - Loaded metadata for 48 assets
2025-06-28 09:50:39,613 - root - INFO - Loaded metadata for 48 assets
2025-06-28 09:50:39,613 - root - INFO - Loaded metadata for 48 assets
2025-06-28 09:50:39,615 - root - INFO - Loaded metadata for 48 assets
2025-06-28 09:50:39,615 - root - INFO - Checking cache for 14 symbols (1d)...
2025-06-28 09:50:39,640 - root - INFO - Loaded 198 rows of ETH/USDT data from cache (last updated: 2025-06-28)
2025-06-28 09:50:39,642 - root - INFO - Metadata for ETH/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-28 09:50:39,643 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:39,643 - root - INFO - Loaded 198 rows of ETH/USDT data from cache (after filtering).
2025-06-28 09:50:39,675 - root - INFO - Loaded 198 rows of BTC/USDT data from cache (last updated: 2025-06-28)
2025-06-28 09:50:39,678 - root - INFO - Metadata for BTC/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-28 09:50:39,679 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:39,679 - root - INFO - Loaded 198 rows of BTC/USDT data from cache (after filtering).
2025-06-28 09:50:39,710 - root - INFO - Loaded 198 rows of SOL/USDT data from cache (last updated: 2025-06-28)
2025-06-28 09:50:39,712 - root - INFO - Metadata for SOL/USDT shows data starting from 2020-08-11, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-28 09:50:39,713 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:39,713 - root - INFO - Loaded 198 rows of SOL/USDT data from cache (after filtering).
2025-06-28 09:50:39,736 - root - INFO - Loaded 198 rows of SUI/USDT data from cache (last updated: 2025-06-28)
2025-06-28 09:50:39,738 - root - INFO - Metadata for SUI/USDT shows data starting from 2023-05-03, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-28 09:50:39,739 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:39,740 - root - INFO - Loaded 198 rows of SUI/USDT data from cache (after filtering).
2025-06-28 09:50:39,786 - root - INFO - Loaded 198 rows of XRP/USDT data from cache (last updated: 2025-06-28)
2025-06-28 09:50:39,789 - root - INFO - Metadata for XRP/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-28 09:50:39,790 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:39,790 - root - INFO - Loaded 198 rows of XRP/USDT data from cache (after filtering).
2025-06-28 09:50:39,819 - root - INFO - Loaded 198 rows of AAVE/USDT data from cache (last updated: 2025-06-28)
2025-06-28 09:50:39,822 - root - INFO - Metadata for AAVE/USDT shows data starting from 2020-10-15, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-28 09:50:39,823 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:39,823 - root - INFO - Loaded 198 rows of AAVE/USDT data from cache (after filtering).
2025-06-28 09:50:39,857 - root - INFO - Loaded 198 rows of AVAX/USDT data from cache (last updated: 2025-06-28)
2025-06-28 09:50:39,858 - root - INFO - Metadata for AVAX/USDT shows data starting from 2020-09-22, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-28 09:50:39,859 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:39,859 - root - INFO - Loaded 198 rows of AVAX/USDT data from cache (after filtering).
2025-06-28 09:50:39,891 - root - INFO - Loaded 198 rows of ADA/USDT data from cache (last updated: 2025-06-28)
2025-06-28 09:50:39,893 - root - INFO - Metadata for ADA/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-28 09:50:39,894 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:39,894 - root - INFO - Loaded 198 rows of ADA/USDT data from cache (after filtering).
2025-06-28 09:50:39,922 - root - INFO - Loaded 198 rows of LINK/USDT data from cache (last updated: 2025-06-28)
2025-06-28 09:50:39,924 - root - INFO - Metadata for LINK/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-28 09:50:39,925 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:39,925 - root - INFO - Loaded 198 rows of LINK/USDT data from cache (after filtering).
2025-06-28 09:50:39,952 - root - INFO - Loaded 198 rows of TRX/USDT data from cache (last updated: 2025-06-28)
2025-06-28 09:50:39,955 - root - INFO - Metadata for TRX/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-28 09:50:39,955 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:39,955 - root - INFO - Loaded 198 rows of TRX/USDT data from cache (after filtering).
2025-06-28 09:50:39,974 - root - INFO - Loaded 198 rows of PEPE/USDT data from cache (last updated: 2025-06-28)
2025-06-28 09:50:39,975 - root - INFO - Metadata for PEPE/USDT shows data starting from 2023-05-05, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-28 09:50:39,976 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:39,976 - root - INFO - Loaded 198 rows of PEPE/USDT data from cache (after filtering).
2025-06-28 09:50:40,008 - root - INFO - Loaded 198 rows of DOGE/USDT data from cache (last updated: 2025-06-28)
2025-06-28 09:50:40,009 - root - INFO - Metadata for DOGE/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-28 09:50:40,010 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:40,010 - root - INFO - Loaded 198 rows of DOGE/USDT data from cache (after filtering).
2025-06-28 09:50:40,042 - root - INFO - Loaded 198 rows of BNB/USDT data from cache (last updated: 2025-06-28)
2025-06-28 09:50:40,045 - root - INFO - Metadata for BNB/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-28 09:50:40,046 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:40,046 - root - INFO - Loaded 198 rows of BNB/USDT data from cache (after filtering).
2025-06-28 09:50:40,070 - root - INFO - Loaded 198 rows of DOT/USDT data from cache (last updated: 2025-06-28)
2025-06-28 09:50:40,074 - root - INFO - Metadata for DOT/USDT shows data starting from 2020-08-18, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-28 09:50:40,075 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:40,075 - root - INFO - Loaded 198 rows of DOT/USDT data from cache (after filtering).
2025-06-28 09:50:40,076 - root - INFO - All 14 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-28 09:50:40,076 - root - INFO - Asset ETH/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-28 09:50:40,077 - root - INFO - Asset BTC/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-28 09:50:40,077 - root - INFO - Asset SOL/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-28 09:50:40,078 - root - INFO - Asset SUI/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-28 09:50:40,078 - root - INFO - Asset XRP/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-28 09:50:40,078 - root - INFO - Asset AAVE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-28 09:50:40,078 - root - INFO - Asset AVAX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-28 09:50:40,079 - root - INFO - Asset ADA/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-28 09:50:40,079 - root - INFO - Asset LINK/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-28 09:50:40,079 - root - INFO - Asset TRX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-28 09:50:40,079 - root - INFO - Asset PEPE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-28 09:50:40,079 - root - INFO - Asset DOGE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-28 09:50:40,080 - root - INFO - Asset BNB/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-28 09:50:40,080 - root - INFO - Asset DOT/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-28 09:50:40,121 - root - INFO - Using standard MTPI warmup period of 120 days
2025-06-28 09:50:40,121 - root - INFO - Fetching MTPI signals from 2024-10-13 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-28 09:50:40,121 - root - INFO - Fetching MTPI signals using trend method: PGO For Loop
2025-06-28 09:50:40,122 - root - INFO - Using multi-indicator MTPI with config override: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-28 09:50:40,122 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-28 09:50:40,140 - root - INFO - Configuration loaded successfully.
2025-06-28 09:50:40,141 - root - INFO - Loaded MTPI multi-indicator configuration with 8 enabled indicators
2025-06-28 09:50:40,141 - root - INFO - Applying configuration overrides: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-28 09:50:40,142 - root - INFO - Override: enabled_indicators = ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-28 09:50:40,142 - root - INFO - Override: combination_method = consensus
2025-06-28 09:50:40,142 - root - INFO - Override: long_threshold = 0.1
2025-06-28 09:50:40,142 - root - INFO - Override: short_threshold = -0.1
2025-06-28 09:50:40,142 - root - INFO - Using MTPI configuration: indicators=['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], method=consensus, thresholds=(-0.1, 0.1)
2025-06-28 09:50:40,143 - root - INFO - Calculated appropriate limit for 1d timeframe: 500 candles (minimum 180.0 candles needed for 60 length indicator)
2025-06-28 09:50:40,143 - root - INFO - Using adjusted limit: 500 for max indicator length: 60
2025-06-28 09:50:40,143 - root - INFO - Checking cache for 1 symbols (1d)...
2025-06-28 09:50:40,182 - root - INFO - Loaded 258 rows of BTC/USDT data from cache (last updated: 2025-06-28)
2025-06-28 09:50:40,183 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:50:40,183 - root - INFO - Loaded 258 rows of BTC/USDT data from cache (after filtering).
2025-06-28 09:50:40,183 - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-28 09:50:40,184 - root - INFO - Fetched BTC data: 258 candles from 2024-10-13 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:50:40,185 - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-06-28 09:50:40,241 - root - INFO - Generated PGO Score signals: {-1: 106, 0: 34, 1: 118}
2025-06-28 09:50:40,242 - root - INFO - Generated pgo signals: 258 values
2025-06-28 09:50:40,242 - root - INFO - Generating Bollinger Band signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False, heikin_src=close
2025-06-28 09:50:40,242 - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-06-28 09:50:40,263 - root - INFO - Generated BB Score signals: {-1: 108, 0: 32, 1: 118}
2025-06-28 09:50:40,263 - root - INFO - Generated Bollinger Band signals: 258 values
2025-06-28 09:50:40,264 - root - INFO - Generated bollinger_bands signals: 258 values
2025-06-28 09:50:40,966 - root - INFO - Generated DWMA signals using Weighted SD method
2025-06-28 09:50:40,966 - root - INFO - Generated dwma_score signals: 258 values
2025-06-28 09:50:41,053 - root - INFO - Generated DEMA Supertrend signals
2025-06-28 09:50:41,054 - root - INFO - Signal distribution: {-1: 144, 0: 1, 1: 113}
2025-06-28 09:50:41,054 - root - INFO - Generated DEMA Super Score signals
2025-06-28 09:50:41,054 - root - INFO - Generated dema_super_score signals: 258 values
2025-06-28 09:50:41,195 - root - INFO - Generated DPSD signals
2025-06-28 09:50:41,196 - root - INFO - Signal distribution: {-1: 100, 0: 87, 1: 71}
2025-06-28 09:50:41,196 - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-06-28 09:50:41,197 - root - INFO - Generated dpsd_score signals: 258 values
2025-06-28 09:50:41,210 - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-06-28 09:50:41,210 - root - INFO - Generated AAD Score signals using SMA method
2025-06-28 09:50:41,210 - root - INFO - Generated aad_score signals: 258 values
2025-06-28 09:50:41,304 - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-06-28 09:50:41,305 - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-06-28 09:50:41,305 - root - INFO - Generated dynamic_ema_score signals: 258 values
2025-06-28 09:50:41,422 - root - INFO - Generated quantile_dema_score signals: 258 values
2025-06-28 09:50:41,430 - root - INFO - Combined 8 signals using arithmetic mean aggregation
2025-06-28 09:50:41,431 - root - INFO - Signal distribution: {1: 145, -1: 112, 0: 1}
2025-06-28 09:50:41,431 - root - INFO - Generated combined MTPI signals: 258 values using consensus method
2025-06-28 09:50:41,432 - root - INFO - Signal distribution: {1: 145, -1: 112, 0: 1}
2025-06-28 09:50:41,432 - root - INFO - Calculating daily scores using trend method: PGO For Loop...
2025-06-28 09:50:41,436 - root - INFO - Saving configuration to config/settings.yaml...
2025-06-28 09:50:41,442 - root - INFO - Configuration saved successfully.
2025-06-28 09:50:41,442 - root - INFO - Updated config with trend method: PGO For Loop and PGO parameters
2025-06-28 09:50:41,442 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-28 09:50:41,455 - root - INFO - Configuration loaded successfully.
2025-06-28 09:50:41,456 - root - INFO - Using ratio-based approach with PGO (PGO For Loop)...
2025-06-28 09:50:41,456 - root - INFO - Calculating ratio PGO signals for 14 assets with PGO(35) using full OHLCV data
2025-06-28 09:50:41,457 - root - INFO - Using ratio calculation method: independent
2025-06-28 09:50:41,486 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:41,511 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BTC/USDT using full OHLCV data
2025-06-28 09:50:41,549 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-28 09:50:41,549 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:41,567 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-28 09:50:41,574 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SOL/USDT using full OHLCV data
2025-06-28 09:50:41,599 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-28 09:50:41,600 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:41,639 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-28 09:50:41,653 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SUI/USDT using full OHLCV data
2025-06-28 09:50:41,689 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-28 09:50:41,690 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:41,713 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-28 09:50:41,728 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/XRP/USDT using full OHLCV data
2025-06-28 09:50:41,773 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-28 09:50:41,773 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:41,803 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-28 09:50:41,811 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AAVE/USDT using full OHLCV data
2025-06-28 09:50:41,839 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-28 09:50:41,839 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:41,862 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-28 09:50:41,869 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AVAX/USDT using full OHLCV data
2025-06-28 09:50:41,901 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-28 09:50:41,901 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:41,969 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-28 09:50:41,994 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/ADA/USDT using full OHLCV data
2025-06-28 09:50:42,047 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-28 09:50:42,048 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:42,083 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-28 09:50:42,094 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/LINK/USDT using full OHLCV data
2025-06-28 09:50:42,127 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-28 09:50:42,127 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:42,156 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-28 09:50:42,164 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/TRX/USDT using full OHLCV data
2025-06-28 09:50:42,193 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-28 09:50:42,193 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:42,217 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-28 09:50:42,228 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/PEPE/USDT using full OHLCV data
2025-06-28 09:50:42,256 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-28 09:50:42,256 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:42,283 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-28 09:50:42,293 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOGE/USDT using full OHLCV data
2025-06-28 09:50:42,322 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:42,357 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BNB/USDT using full OHLCV data
2025-06-28 09:50:42,383 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-28 09:50:42,385 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:42,822 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-28 09:50:42,846 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOT/USDT using full OHLCV data
2025-06-28 09:50:42,902 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-28 09:50:42,903 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:42,945 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-28 09:50:42,958 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ETH/USDT using full OHLCV data
2025-06-28 09:50:42,987 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:43,022 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SOL/USDT using full OHLCV data
2025-06-28 09:50:43,052 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-28 09:50:43,054 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:43,075 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-28 09:50:43,086 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SUI/USDT using full OHLCV data
2025-06-28 09:50:43,111 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-28 09:50:43,112 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:43,132 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-28 09:50:43,136 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/XRP/USDT using full OHLCV data
2025-06-28 09:50:43,162 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-28 09:50:43,162 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:43,186 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-28 09:50:43,195 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AAVE/USDT using full OHLCV data
2025-06-28 09:50:43,220 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-28 09:50:43,220 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:43,245 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-28 09:50:43,253 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AVAX/USDT using full OHLCV data
2025-06-28 09:50:43,283 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-28 09:50:43,283 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:43,311 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-28 09:50:43,323 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ADA/USDT using full OHLCV data
2025-06-28 09:50:43,354 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-28 09:50:43,354 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:43,381 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-28 09:50:43,391 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/LINK/USDT using full OHLCV data
2025-06-28 09:50:43,420 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-28 09:50:43,420 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:43,451 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-28 09:50:43,458 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/TRX/USDT using full OHLCV data
2025-06-28 09:50:43,485 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-28 09:50:43,486 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:43,508 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-28 09:50:43,519 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/PEPE/USDT using full OHLCV data
2025-06-28 09:50:43,546 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:43,576 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOGE/USDT using full OHLCV data
2025-06-28 09:50:43,608 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:43,644 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/BNB/USDT using full OHLCV data
2025-06-28 09:50:43,672 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-28 09:50:43,672 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:43,695 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-28 09:50:43,704 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOT/USDT using full OHLCV data
2025-06-28 09:50:43,739 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-28 09:50:43,739 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:43,781 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-28 09:50:43,791 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ETH/USDT using full OHLCV data
2025-06-28 09:50:43,814 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:43,842 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BTC/USDT using full OHLCV data
2025-06-28 09:50:43,871 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-28 09:50:43,871 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:43,900 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-28 09:50:43,908 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/SUI/USDT using full OHLCV data
2025-06-28 09:50:43,937 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-28 09:50:43,937 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:43,960 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-28 09:50:43,972 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/XRP/USDT using full OHLCV data
2025-06-28 09:50:44,006 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-28 09:50:44,007 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:44,029 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-28 09:50:44,038 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AAVE/USDT using full OHLCV data
2025-06-28 09:50:44,068 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-28 09:50:44,070 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:44,198 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-28 09:50:44,209 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AVAX/USDT using full OHLCV data
2025-06-28 09:50:44,243 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-28 09:50:44,244 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:44,273 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-28 09:50:44,289 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ADA/USDT using full OHLCV data
2025-06-28 09:50:44,328 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-28 09:50:44,328 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:44,358 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-28 09:50:44,367 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/LINK/USDT using full OHLCV data
2025-06-28 09:50:44,403 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-28 09:50:44,404 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:44,433 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-28 09:50:44,447 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/TRX/USDT using full OHLCV data
2025-06-28 09:50:44,487 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-28 09:50:44,488 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:44,519 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-28 09:50:44,529 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/PEPE/USDT using full OHLCV data
2025-06-28 09:50:44,560 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-28 09:50:44,560 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:44,590 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-28 09:50:44,604 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOGE/USDT using full OHLCV data
2025-06-28 09:50:44,636 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-28 09:50:44,637 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:44,655 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-28 09:50:44,666 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BNB/USDT using full OHLCV data
2025-06-28 09:50:44,696 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-28 09:50:44,696 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:44,721 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-28 09:50:44,731 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOT/USDT using full OHLCV data
2025-06-28 09:50:44,769 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-28 09:50:44,769 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:44,798 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-28 09:50:44,807 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ETH/USDT using full OHLCV data
2025-06-28 09:50:44,839 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:44,877 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BTC/USDT using full OHLCV data
2025-06-28 09:50:44,906 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:44,945 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/SOL/USDT using full OHLCV data
2025-06-28 09:50:44,969 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:45,000 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/XRP/USDT using full OHLCV data
2025-06-28 09:50:45,036 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-28 09:50:45,036 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:45,071 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-28 09:50:45,079 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AAVE/USDT using full OHLCV data
2025-06-28 09:50:45,112 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:45,147 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AVAX/USDT using full OHLCV data
2025-06-28 09:50:45,188 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:45,229 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ADA/USDT using full OHLCV data
2025-06-28 09:50:45,260 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-28 09:50:45,260 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:45,281 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-28 09:50:45,289 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/LINK/USDT using full OHLCV data
2025-06-28 09:50:45,316 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:45,342 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/TRX/USDT using full OHLCV data
2025-06-28 09:50:45,369 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:45,412 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/PEPE/USDT using full OHLCV data
2025-06-28 09:50:45,441 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-28 09:50:45,441 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:45,462 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-28 09:50:45,474 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOGE/USDT using full OHLCV data
2025-06-28 09:50:45,503 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:45,537 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BNB/USDT using full OHLCV data
2025-06-28 09:50:45,564 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:45,603 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOT/USDT using full OHLCV data
2025-06-28 09:50:45,638 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-28 09:50:45,643 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:45,679 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-28 09:50:45,690 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ETH/USDT using full OHLCV data
2025-06-28 09:50:45,724 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:45,759 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BTC/USDT using full OHLCV data
2025-06-28 09:50:45,788 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:45,816 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SOL/USDT using full OHLCV data
2025-06-28 09:50:45,844 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-28 09:50:45,844 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:45,865 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-28 09:50:45,873 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SUI/USDT using full OHLCV data
2025-06-28 09:50:45,901 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-28 09:50:45,902 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:45,929 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-28 09:50:45,936 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AAVE/USDT using full OHLCV data
2025-06-28 09:50:45,966 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-28 09:50:45,966 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:45,991 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-28 09:50:46,000 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AVAX/USDT using full OHLCV data
2025-06-28 09:50:46,031 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-28 09:50:46,031 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:46,052 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-28 09:50:46,059 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ADA/USDT using full OHLCV data
2025-06-28 09:50:46,088 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-28 09:50:46,088 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:46,114 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-28 09:50:46,121 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/LINK/USDT using full OHLCV data
2025-06-28 09:50:46,156 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-28 09:50:46,156 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:46,181 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-28 09:50:46,189 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/TRX/USDT using full OHLCV data
2025-06-28 09:50:46,218 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-28 09:50:46,218 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:46,248 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-28 09:50:46,257 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/PEPE/USDT using full OHLCV data
2025-06-28 09:50:46,286 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:46,328 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOGE/USDT using full OHLCV data
2025-06-28 09:50:46,359 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:46,392 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BNB/USDT using full OHLCV data
2025-06-28 09:50:46,431 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-28 09:50:46,431 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:46,466 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-28 09:50:46,476 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOT/USDT using full OHLCV data
2025-06-28 09:50:46,507 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:46,545 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ETH/USDT using full OHLCV data
2025-06-28 09:50:46,570 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:46,598 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BTC/USDT using full OHLCV data
2025-06-28 09:50:46,624 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:46,665 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SOL/USDT using full OHLCV data
2025-06-28 09:50:46,690 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:46,726 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SUI/USDT using full OHLCV data
2025-06-28 09:50:46,756 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:46,784 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/XRP/USDT using full OHLCV data
2025-06-28 09:50:46,815 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-28 09:50:46,816 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:46,841 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-28 09:50:46,854 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/AVAX/USDT using full OHLCV data
2025-06-28 09:50:46,884 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-28 09:50:46,886 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:46,911 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-28 09:50:46,919 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ADA/USDT using full OHLCV data
2025-06-28 09:50:46,947 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-28 09:50:46,948 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:46,972 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-28 09:50:46,980 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/LINK/USDT using full OHLCV data
2025-06-28 09:50:47,011 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:47,043 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/TRX/USDT using full OHLCV data
2025-06-28 09:50:47,070 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:47,107 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/PEPE/USDT using full OHLCV data
2025-06-28 09:50:47,149 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-28 09:50:47,149 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:47,180 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-28 09:50:47,190 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOGE/USDT using full OHLCV data
2025-06-28 09:50:47,220 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:47,253 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BNB/USDT using full OHLCV data
2025-06-28 09:50:47,276 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-28 09:50:47,276 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:47,300 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-28 09:50:47,307 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOT/USDT using full OHLCV data
2025-06-28 09:50:47,329 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:47,368 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ETH/USDT using full OHLCV data
2025-06-28 09:50:47,404 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:47,452 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BTC/USDT using full OHLCV data
2025-06-28 09:50:47,487 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:47,526 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SOL/USDT using full OHLCV data
2025-06-28 09:50:47,553 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:47,585 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SUI/USDT using full OHLCV data
2025-06-28 09:50:47,613 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:47,645 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/XRP/USDT using full OHLCV data
2025-06-28 09:50:47,670 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-28 09:50:47,670 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:47,691 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-28 09:50:47,698 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/AAVE/USDT using full OHLCV data
2025-06-28 09:50:47,725 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:47,765 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ADA/USDT using full OHLCV data
2025-06-28 09:50:47,809 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-28 09:50:47,810 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:47,841 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-28 09:50:47,852 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/LINK/USDT using full OHLCV data
2025-06-28 09:50:47,874 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:47,919 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/TRX/USDT using full OHLCV data
2025-06-28 09:50:47,970 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:48,029 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/PEPE/USDT using full OHLCV data
2025-06-28 09:50:48,073 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:48,121 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOGE/USDT using full OHLCV data
2025-06-28 09:50:48,161 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:48,199 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BNB/USDT using full OHLCV data
2025-06-28 09:50:48,228 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:48,264 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOT/USDT using full OHLCV data
2025-06-28 09:50:48,300 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-28 09:50:48,303 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:48,327 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-28 09:50:48,339 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/ETH/USDT using full OHLCV data
2025-06-28 09:50:48,378 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:48,410 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BTC/USDT using full OHLCV data
2025-06-28 09:50:48,438 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:48,479 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SOL/USDT using full OHLCV data
2025-06-28 09:50:48,513 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-28 09:50:48,514 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:48,541 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-28 09:50:48,550 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SUI/USDT using full OHLCV data
2025-06-28 09:50:48,575 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:48,608 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/XRP/USDT using full OHLCV data
2025-06-28 09:50:48,641 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-28 09:50:48,641 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:48,661 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-28 09:50:48,667 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AAVE/USDT using full OHLCV data
2025-06-28 09:50:48,698 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-28 09:50:48,698 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:48,723 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-28 09:50:48,732 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AVAX/USDT using full OHLCV data
2025-06-28 09:50:48,758 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-28 09:50:48,758 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:48,792 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-28 09:50:48,804 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/LINK/USDT using full OHLCV data
2025-06-28 09:50:48,831 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:48,866 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/TRX/USDT using full OHLCV data
2025-06-28 09:50:48,903 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-28 09:50:48,903 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:48,926 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-28 09:50:48,939 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/PEPE/USDT using full OHLCV data
2025-06-28 09:50:48,981 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-28 09:50:48,984 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:49,006 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-28 09:50:49,012 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOGE/USDT using full OHLCV data
2025-06-28 09:50:49,037 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:49,081 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BNB/USDT using full OHLCV data
2025-06-28 09:50:49,112 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-28 09:50:49,113 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:49,148 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-28 09:50:49,159 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOT/USDT using full OHLCV data
2025-06-28 09:50:49,188 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:49,221 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ETH/USDT using full OHLCV data
2025-06-28 09:50:49,247 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:49,284 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BTC/USDT using full OHLCV data
2025-06-28 09:50:49,306 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:49,338 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SOL/USDT using full OHLCV data
2025-06-28 09:50:49,369 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:49,401 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SUI/USDT using full OHLCV data
2025-06-28 09:50:49,430 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:49,465 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/XRP/USDT using full OHLCV data
2025-06-28 09:50:49,493 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-28 09:50:49,494 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:49,523 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-28 09:50:49,535 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AAVE/USDT using full OHLCV data
2025-06-28 09:50:49,567 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:49,637 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AVAX/USDT using full OHLCV data
2025-06-28 09:50:49,694 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:49,763 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ADA/USDT using full OHLCV data
2025-06-28 09:50:49,816 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:49,884 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/TRX/USDT using full OHLCV data
2025-06-28 09:50:49,920 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-28 09:50:49,921 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:49,947 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-28 09:50:49,961 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/PEPE/USDT using full OHLCV data
2025-06-28 09:50:50,003 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:50,052 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOGE/USDT using full OHLCV data
2025-06-28 09:50:50,079 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:50,111 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BNB/USDT using full OHLCV data
2025-06-28 09:50:50,141 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:50,180 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOT/USDT using full OHLCV data
2025-06-28 09:50:50,206 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:50,239 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ETH/USDT using full OHLCV data
2025-06-28 09:50:50,266 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:50,324 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BTC/USDT using full OHLCV data
2025-06-28 09:50:50,378 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-28 09:50:50,378 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:50,402 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-28 09:50:50,416 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SOL/USDT using full OHLCV data
2025-06-28 09:50:50,463 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:50,527 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SUI/USDT using full OHLCV data
2025-06-28 09:50:50,582 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-28 09:50:50,583 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:50,644 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-28 09:50:50,670 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/XRP/USDT using full OHLCV data
2025-06-28 09:50:50,717 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-28 09:50:50,717 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:50,749 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-28 09:50:50,759 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AAVE/USDT using full OHLCV data
2025-06-28 09:50:50,810 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-28 09:50:50,811 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:50,865 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-28 09:50:50,881 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AVAX/USDT using full OHLCV data
2025-06-28 09:50:50,935 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:50,993 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ADA/USDT using full OHLCV data
2025-06-28 09:50:51,040 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-28 09:50:51,041 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:51,068 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-28 09:50:51,075 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/LINK/USDT using full OHLCV data
2025-06-28 09:50:51,104 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-28 09:50:51,104 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:51,129 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-28 09:50:51,138 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/PEPE/USDT using full OHLCV data
2025-06-28 09:50:51,163 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:51,197 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOGE/USDT using full OHLCV data
2025-06-28 09:50:51,219 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:51,256 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BNB/USDT using full OHLCV data
2025-06-28 09:50:51,280 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:51,313 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOT/USDT using full OHLCV data
2025-06-28 09:50:51,343 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:51,370 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ETH/USDT using full OHLCV data
2025-06-28 09:50:51,396 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:51,430 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BTC/USDT using full OHLCV data
2025-06-28 09:50:51,457 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:51,492 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SOL/USDT using full OHLCV data
2025-06-28 09:50:51,522 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:51,552 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SUI/USDT using full OHLCV data
2025-06-28 09:50:51,578 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:51,614 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/XRP/USDT using full OHLCV data
2025-06-28 09:50:51,646 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-28 09:50:51,647 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:51,670 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-28 09:50:51,679 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AAVE/USDT using full OHLCV data
2025-06-28 09:50:51,706 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-28 09:50:51,706 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:51,732 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-28 09:50:51,742 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AVAX/USDT using full OHLCV data
2025-06-28 09:50:51,770 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-28 09:50:51,771 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:51,800 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-28 09:50:51,810 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ADA/USDT using full OHLCV data
2025-06-28 09:50:51,837 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-28 09:50:51,837 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:51,861 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-28 09:50:51,871 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/LINK/USDT using full OHLCV data
2025-06-28 09:50:51,897 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:51,932 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/TRX/USDT using full OHLCV data
2025-06-28 09:50:51,962 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:52,004 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOGE/USDT using full OHLCV data
2025-06-28 09:50:52,031 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:52,069 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BNB/USDT using full OHLCV data
2025-06-28 09:50:52,097 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:52,132 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOT/USDT using full OHLCV data
2025-06-28 09:50:52,163 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:52,201 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ETH/USDT using full OHLCV data
2025-06-28 09:50:52,228 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:52,264 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BTC/USDT using full OHLCV data
2025-06-28 09:50:52,293 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:52,323 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SOL/USDT using full OHLCV data
2025-06-28 09:50:52,350 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-28 09:50:52,351 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:52,375 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-28 09:50:52,384 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SUI/USDT using full OHLCV data
2025-06-28 09:50:52,418 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-28 09:50:52,418 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:52,444 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-28 09:50:52,455 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/XRP/USDT using full OHLCV data
2025-06-28 09:50:52,502 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-28 09:50:52,502 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:52,551 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-28 09:50:52,571 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AAVE/USDT using full OHLCV data
2025-06-28 09:50:52,633 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-28 09:50:52,634 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:52,685 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-28 09:50:52,701 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AVAX/USDT using full OHLCV data
2025-06-28 09:50:52,724 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-28 09:50:52,724 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:52,755 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-28 09:50:52,764 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ADA/USDT using full OHLCV data
2025-06-28 09:50:52,791 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-28 09:50:52,792 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:52,817 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-28 09:50:52,826 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/LINK/USDT using full OHLCV data
2025-06-28 09:50:52,849 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-28 09:50:52,849 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:52,874 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-28 09:50:52,882 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/TRX/USDT using full OHLCV data
2025-06-28 09:50:52,910 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-28 09:50:52,910 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:52,937 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-28 09:50:52,949 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/PEPE/USDT using full OHLCV data
2025-06-28 09:50:52,979 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:53,013 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BNB/USDT using full OHLCV data
2025-06-28 09:50:53,046 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-28 09:50:53,047 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:53,076 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-28 09:50:53,083 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/DOT/USDT using full OHLCV data
2025-06-28 09:50:53,111 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:53,166 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ETH/USDT using full OHLCV data
2025-06-28 09:50:53,194 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:53,231 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/BTC/USDT using full OHLCV data
2025-06-28 09:50:53,270 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-28 09:50:53,270 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:53,297 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-28 09:50:53,307 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SOL/USDT using full OHLCV data
2025-06-28 09:50:53,352 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-28 09:50:53,352 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:53,384 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-28 09:50:53,396 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SUI/USDT using full OHLCV data
2025-06-28 09:50:53,427 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-28 09:50:53,427 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:53,470 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-28 09:50:53,487 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/XRP/USDT using full OHLCV data
2025-06-28 09:50:53,537 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-28 09:50:53,538 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:53,584 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-28 09:50:53,602 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AAVE/USDT using full OHLCV data
2025-06-28 09:50:53,654 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-28 09:50:53,655 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:53,684 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-28 09:50:53,693 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AVAX/USDT using full OHLCV data
2025-06-28 09:50:53,721 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-28 09:50:53,722 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:53,751 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-28 09:50:53,773 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ADA/USDT using full OHLCV data
2025-06-28 09:50:53,834 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-28 09:50:53,835 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:53,882 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-28 09:50:53,903 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/LINK/USDT using full OHLCV data
2025-06-28 09:50:53,946 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-28 09:50:53,947 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:53,970 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-28 09:50:53,978 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/TRX/USDT using full OHLCV data
2025-06-28 09:50:54,003 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-28 09:50:54,003 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:54,026 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-28 09:50:54,033 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/PEPE/USDT using full OHLCV data
2025-06-28 09:50:54,059 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:54,091 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOGE/USDT using full OHLCV data
2025-06-28 09:50:54,117 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:54,147 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOT/USDT using full OHLCV data
2025-06-28 09:50:54,176 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:54,234 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ETH/USDT using full OHLCV data
2025-06-28 09:50:54,236 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-28 09:50:54,268 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:54,319 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BTC/USDT using full OHLCV data
2025-06-28 09:50:54,369 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:54,419 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SOL/USDT using full OHLCV data
2025-06-28 09:50:54,452 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-28 09:50:54,452 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:54,483 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-28 09:50:54,490 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SUI/USDT using full OHLCV data
2025-06-28 09:50:54,550 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-28 09:50:54,551 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:54,584 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-28 09:50:54,595 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/XRP/USDT using full OHLCV data
2025-06-28 09:50:54,642 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-28 09:50:54,646 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:54,680 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-28 09:50:54,689 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AAVE/USDT using full OHLCV data
2025-06-28 09:50:54,716 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-28 09:50:54,717 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:54,744 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-28 09:50:54,751 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AVAX/USDT using full OHLCV data
2025-06-28 09:50:54,797 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-28 09:50:54,809 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:54,842 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-28 09:50:54,851 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ADA/USDT using full OHLCV data
2025-06-28 09:50:54,894 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-28 09:50:54,895 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:54,925 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-28 09:50:54,940 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/LINK/USDT using full OHLCV data
2025-06-28 09:50:54,973 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:55,017 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/TRX/USDT using full OHLCV data
2025-06-28 09:50:55,056 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-28 09:50:55,057 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:55,086 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-28 09:50:55,095 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/PEPE/USDT using full OHLCV data
2025-06-28 09:50:55,119 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:55,155 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/DOGE/USDT using full OHLCV data
2025-06-28 09:50:55,177 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-28 09:50:55,215 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BNB/USDT using full OHLCV data
2025-06-28 09:50:58,915 - root - INFO - Successfully calculated daily scores using ratio-based comparisons.
2025-06-28 09:50:58,915 - root - INFO - Latest MTPI signal is -1
2025-06-28 09:50:58,915 - root - INFO - Latest MTPI signal is -1, will stay out of market during equity curve calculation
2025-06-28 09:50:58,915 - root - INFO - Finished calculating daily scores. DataFrame shape: (198, 14)
2025-06-28 09:50:58,915 - root - WARNING - Running strategy with n_assets=1, equal allocation, MTPI filtering ENABLED
2025-06-28 09:50:58,922 - root - INFO - Date ranges for each asset:
2025-06-28 09:50:58,922 - root - INFO -   ETH/USDT: 2024-12-12 to 2025-06-27 (198 candles)
2025-06-28 09:50:58,923 - root - INFO -   BTC/USDT: 2024-12-12 to 2025-06-27 (198 candles)
2025-06-28 09:50:58,923 - root - INFO -   SOL/USDT: 2024-12-12 to 2025-06-27 (198 candles)
2025-06-28 09:50:58,923 - root - INFO -   SUI/USDT: 2024-12-12 to 2025-06-27 (198 candles)
2025-06-28 09:50:58,923 - root - INFO -   XRP/USDT: 2024-12-12 to 2025-06-27 (198 candles)
2025-06-28 09:50:58,924 - root - INFO -   AAVE/USDT: 2024-12-12 to 2025-06-27 (198 candles)
2025-06-28 09:50:58,924 - root - INFO -   AVAX/USDT: 2024-12-12 to 2025-06-27 (198 candles)
2025-06-28 09:50:58,924 - root - INFO -   ADA/USDT: 2024-12-12 to 2025-06-27 (198 candles)
2025-06-28 09:50:58,924 - root - INFO -   LINK/USDT: 2024-12-12 to 2025-06-27 (198 candles)
2025-06-28 09:50:58,924 - root - INFO -   TRX/USDT: 2024-12-12 to 2025-06-27 (198 candles)
2025-06-28 09:50:58,924 - root - INFO -   PEPE/USDT: 2024-12-12 to 2025-06-27 (198 candles)
2025-06-28 09:50:58,924 - root - INFO -   DOGE/USDT: 2024-12-12 to 2025-06-27 (198 candles)
2025-06-28 09:50:58,924 - root - INFO -   BNB/USDT: 2024-12-12 to 2025-06-27 (198 candles)
2025-06-28 09:50:58,924 - root - INFO -   DOT/USDT: 2024-12-12 to 2025-06-27 (198 candles)
2025-06-28 09:50:58,924 - root - INFO - Common dates range: 2024-12-12 to 2025-06-27 (198 candles)
2025-06-28 09:50:58,925 - root - INFO - Analysis will run from: 2025-02-10 to 2025-06-27 (138 candles)
2025-06-28 09:50:58,930 - root - INFO - EXECUTION TIMING VERIFICATION:
2025-06-28 09:50:58,930 - root - INFO -    Execution Method: candle_close
2025-06-28 09:50:58,930 - root - INFO -    Automatic execution at 00:00 UTC (candle close)
2025-06-28 09:50:58,930 - root - INFO -    Signal generated and executed immediately
2025-06-28 09:50:58,951 - root - INFO - Including ETH/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:58,952 - root - INFO - Including BTC/USDT on 2025-02-10 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:58,952 - root - INFO - Including SOL/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:58,954 - root - INFO - Including SUI/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:58,955 - root - INFO - Including XRP/USDT on 2025-02-10 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:58,955 - root - INFO - Including AAVE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:58,957 - root - INFO - Including AVAX/USDT on 2025-02-10 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:58,957 - root - INFO - Including ADA/USDT on 2025-02-10 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:58,957 - root - INFO - Including LINK/USDT on 2025-02-10 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:58,957 - root - INFO - Including TRX/USDT on 2025-02-10 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:58,957 - root - INFO - Including PEPE/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:58,957 - root - INFO - Including DOGE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:58,957 - root - INFO - Including BNB/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:58,959 - root - INFO - Including DOT/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:58,990 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:58,995 - root - INFO - Including ETH/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:58,995 - root - INFO - Including BTC/USDT on 2025-02-11 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:58,995 - root - INFO - Including SOL/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:58,995 - root - INFO - Including SUI/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:58,995 - root - INFO - Including XRP/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:58,996 - root - INFO - Including AAVE/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:58,996 - root - INFO - Including AVAX/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:58,996 - root - INFO - Including ADA/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:58,996 - root - INFO - Including LINK/USDT on 2025-02-11 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:58,996 - root - INFO - Including TRX/USDT on 2025-02-11 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:58,996 - root - INFO - Including PEPE/USDT on 2025-02-11 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:58,997 - root - INFO - Including DOGE/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:58,997 - root - INFO - Including BNB/USDT on 2025-02-11 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:58,997 - root - INFO - Including DOT/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,015 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,021 - root - INFO - Including ETH/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,022 - root - INFO - Including BTC/USDT on 2025-02-12 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,024 - root - INFO - Including SOL/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,025 - root - INFO - Including SUI/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,026 - root - INFO - Including XRP/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,029 - root - INFO - Including AAVE/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,030 - root - INFO - Including AVAX/USDT on 2025-02-12 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,031 - root - INFO - Including ADA/USDT on 2025-02-12 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,031 - root - INFO - Including LINK/USDT on 2025-02-12 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,031 - root - INFO - Including TRX/USDT on 2025-02-12 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,031 - root - INFO - Including PEPE/USDT on 2025-02-12 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,031 - root - INFO - Including DOGE/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,031 - root - INFO - Including BNB/USDT on 2025-02-12 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,031 - root - INFO - Including DOT/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,040 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,046 - root - INFO - Including ETH/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,047 - root - INFO - Including BTC/USDT on 2025-02-13 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,047 - root - INFO - Including SOL/USDT on 2025-02-13 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,047 - root - INFO - Including SUI/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,047 - root - INFO - Including XRP/USDT on 2025-02-13 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,047 - root - INFO - Including AAVE/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,048 - root - INFO - Including AVAX/USDT on 2025-02-13 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,048 - root - INFO - Including ADA/USDT on 2025-02-13 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,048 - root - INFO - Including LINK/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,048 - root - INFO - Including TRX/USDT on 2025-02-13 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,048 - root - INFO - Including PEPE/USDT on 2025-02-13 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,049 - root - INFO - Including DOGE/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,049 - root - INFO - Including BNB/USDT on 2025-02-13 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,049 - root - INFO - Including DOT/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,058 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,062 - root - INFO - Including ETH/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,063 - root - INFO - Including BTC/USDT on 2025-02-14 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,063 - root - INFO - Including SOL/USDT on 2025-02-14 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,063 - root - INFO - Including SUI/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,063 - root - INFO - Including XRP/USDT on 2025-02-14 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,063 - root - INFO - Including AAVE/USDT on 2025-02-14 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,063 - root - INFO - Including AVAX/USDT on 2025-02-14 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,064 - root - INFO - Including ADA/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,064 - root - INFO - Including LINK/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,064 - root - INFO - Including TRX/USDT on 2025-02-14 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,064 - root - INFO - Including PEPE/USDT on 2025-02-14 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,064 - root - INFO - Including DOGE/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,064 - root - INFO - Including BNB/USDT on 2025-02-14 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,064 - root - INFO - Including DOT/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-28 09:50:59,070 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,082 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,097 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,111 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,121 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,130 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,143 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,154 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,168 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,177 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,186 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,195 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,203 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,212 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,221 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,229 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,238 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,245 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,253 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,262 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,270 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,279 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,289 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,298 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,307 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,316 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,324 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,334 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,344 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,354 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,366 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,407 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,433 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,450 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,465 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,478 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,494 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,519 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,538 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,560 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,572 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,592 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,606 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,617 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,627 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,633 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,643 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,650 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,659 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,669 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,819 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,830 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,862 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,869 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,875 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,881 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,887 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,894 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,900 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,909 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,915 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,921 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,927 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,933 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,941 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,946 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,952 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,959 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:50:59,959 - root - INFO - ASSET CHANGE DETECTED on 2025-04-23:
2025-06-28 09:50:59,960 - root - INFO -    Signal Date: 2025-04-22 (generated at 00:00 UTC)
2025-06-28 09:50:59,960 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-23 00:00 UTC (immediate)
2025-06-28 09:50:59,960 - root - INFO -    Execution Delay: 0 hours
2025-06-28 09:50:59,960 - root - INFO -    Buying: ['SOL/USDT']
2025-06-28 09:50:59,960 - root - INFO -    SOL/USDT buy price: $151.1000 (close price)
2025-06-28 09:50:59,966 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:50:59,966 - root - INFO - ASSET CHANGE DETECTED on 2025-04-24:
2025-06-28 09:50:59,966 - root - INFO -    Signal Date: 2025-04-23 (generated at 00:00 UTC)
2025-06-28 09:50:59,966 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-24 00:00 UTC (immediate)
2025-06-28 09:50:59,966 - root - INFO -    Execution Delay: 0 hours
2025-06-28 09:50:59,966 - root - INFO -    Selling: ['SOL/USDT']
2025-06-28 09:50:59,967 - root - INFO -    Buying: ['SUI/USDT']
2025-06-28 09:50:59,967 - root - INFO -    SUI/USDT buy price: $3.3437 (close price)
2025-06-28 09:50:59,974 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:50:59,980 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:50:59,987 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:50:59,993 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:50:59,999 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,007 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,032 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,048 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,059 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,072 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,083 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,095 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,103 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,113 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,122 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,133 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,133 - root - INFO - ASSET CHANGE DETECTED on 2025-05-10:
2025-06-28 09:51:00,133 - root - INFO -    Signal Date: 2025-05-09 (generated at 00:00 UTC)
2025-06-28 09:51:00,134 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-10 00:00 UTC (immediate)
2025-06-28 09:51:00,134 - root - INFO -    Execution Delay: 0 hours
2025-06-28 09:51:00,134 - root - INFO -    Selling: ['SUI/USDT']
2025-06-28 09:51:00,134 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-28 09:51:00,134 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-28 09:51:00,147 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,165 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,188 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,200 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,210 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,233 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,245 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,256 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,273 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,291 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,300 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,300 - root - INFO - ASSET CHANGE DETECTED on 2025-05-21:
2025-06-28 09:51:00,300 - root - INFO -    Signal Date: 2025-05-20 (generated at 00:00 UTC)
2025-06-28 09:51:00,301 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-21 00:00 UTC (immediate)
2025-06-28 09:51:00,301 - root - INFO -    Execution Delay: 0 hours
2025-06-28 09:51:00,301 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-28 09:51:00,301 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-28 09:51:00,301 - root - INFO -    AAVE/USDT buy price: $247.5400 (close price)
2025-06-28 09:51:00,311 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,320 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,320 - root - INFO - ASSET CHANGE DETECTED on 2025-05-23:
2025-06-28 09:51:00,320 - root - INFO -    Signal Date: 2025-05-22 (generated at 00:00 UTC)
2025-06-28 09:51:00,320 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-23 00:00 UTC (immediate)
2025-06-28 09:51:00,321 - root - INFO -    Execution Delay: 0 hours
2025-06-28 09:51:00,321 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-28 09:51:00,321 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-28 09:51:00,321 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-28 09:51:00,329 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,339 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,339 - root - INFO - ASSET CHANGE DETECTED on 2025-05-25:
2025-06-28 09:51:00,339 - root - INFO -    Signal Date: 2025-05-24 (generated at 00:00 UTC)
2025-06-28 09:51:00,339 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-25 00:00 UTC (immediate)
2025-06-28 09:51:00,339 - root - INFO -    Execution Delay: 0 hours
2025-06-28 09:51:00,339 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-28 09:51:00,340 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-28 09:51:00,340 - root - INFO -    AAVE/USDT buy price: $269.2800 (close price)
2025-06-28 09:51:00,348 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,359 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,366 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,375 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,380 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,389 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,394 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,400 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,409 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,417 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,426 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,435 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,445 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,458 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,467 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,478 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,486 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,496 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,504 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,514 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,522 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,530 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,537 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,544 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,551 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,561 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,569 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-28 09:51:00,571 - root - INFO - ASSET CHANGE DETECTED on 2025-06-21:
2025-06-28 09:51:00,571 - root - INFO -    Signal Date: 2025-06-20 (generated at 00:00 UTC)
2025-06-28 09:51:00,571 - root - INFO -    AUTOMATIC EXECUTION: 2025-06-21 00:00 UTC (immediate)
2025-06-28 09:51:00,571 - root - INFO -    Execution Delay: 0 hours
2025-06-28 09:51:00,572 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-28 09:51:00,578 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:51:00,584 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:51:00,591 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:51:00,596 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:51:00,604 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:51:00,611 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-28 09:51:00,641 - root - INFO - Entry trade at 2025-04-23 00:00:00+00:00: SOL/USDT
2025-06-28 09:51:00,642 - root - INFO - Swap trade at 2025-04-24 00:00:00+00:00: SOL/USDT -> SUI/USDT
2025-06-28 09:51:00,642 - root - INFO - Swap trade at 2025-05-10 00:00:00+00:00: SUI/USDT -> PEPE/USDT
2025-06-28 09:51:00,642 - root - INFO - Swap trade at 2025-05-21 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-28 09:51:00,642 - root - INFO - Swap trade at 2025-05-23 00:00:00+00:00: AAVE/USDT -> PEPE/USDT
2025-06-28 09:51:00,642 - root - INFO - Swap trade at 2025-05-25 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-28 09:51:00,643 - root - INFO - Exit trade at 2025-06-21 00:00:00+00:00 from AAVE/USDT
2025-06-28 09:51:00,643 - root - INFO - Total trades: 7 (Entries: 1, Exits: 1, Swaps: 5)
2025-06-28 09:51:00,644 - root - INFO - Strategy execution completed in 1s
2025-06-28 09:51:00,644 - root - INFO - DEBUG: self.elapsed_time = 1.7287580966949463 seconds
2025-06-28 09:51:00,652 - root - INFO - Saved allocation history to allocation_history_1d_1d_with_mtpi_no_rebal_independent_imcumbent_2025-02-10.csv
2025-06-28 09:51:00,653 - root - INFO - Strategy equity curve starts at: 2025-02-10
2025-06-28 09:51:00,653 - root - INFO - Assets included in buy-and-hold comparison:
2025-06-28 09:51:00,653 - root - INFO -   ETH/USDT: First available date 2024-12-12
2025-06-28 09:51:00,653 - root - INFO -   BTC/USDT: First available date 2024-12-12
2025-06-28 09:51:00,654 - root - INFO -   SOL/USDT: First available date 2024-12-12
2025-06-28 09:51:00,654 - root - INFO -   SUI/USDT: First available date 2024-12-12
2025-06-28 09:51:00,654 - root - INFO -   XRP/USDT: First available date 2024-12-12
2025-06-28 09:51:00,654 - root - INFO -   AAVE/USDT: First available date 2024-12-12
2025-06-28 09:51:00,654 - root - INFO -   AVAX/USDT: First available date 2024-12-12
2025-06-28 09:51:00,654 - root - INFO -   ADA/USDT: First available date 2024-12-12
2025-06-28 09:51:00,654 - root - INFO -   LINK/USDT: First available date 2024-12-12
2025-06-28 09:51:00,654 - root - INFO -   TRX/USDT: First available date 2024-12-12
2025-06-28 09:51:00,654 - root - INFO -   PEPE/USDT: First available date 2024-12-12
2025-06-28 09:51:00,655 - root - INFO -   DOGE/USDT: First available date 2024-12-12
2025-06-28 09:51:00,655 - root - INFO -   BNB/USDT: First available date 2024-12-12
2025-06-28 09:51:00,655 - root - INFO -   DOT/USDT: First available date 2024-12-12
2025-06-28 09:51:00,655 - root - INFO - Using provided start date for normalization: 2025-02-10 00:00:00+00:00
2025-06-28 09:51:00,656 - root - INFO - Normalized ETH/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-28 09:51:00,657 - root - INFO - Normalized BTC/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-28 09:51:00,659 - root - INFO - Normalized SOL/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-28 09:51:00,660 - root - INFO - Normalized SUI/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-28 09:51:00,662 - root - INFO - Normalized XRP/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-28 09:51:00,663 - root - INFO - Normalized AAVE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-28 09:51:00,664 - root - INFO - Normalized AVAX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-28 09:51:00,666 - root - INFO - Normalized ADA/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-28 09:51:00,667 - root - INFO - Normalized LINK/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-28 09:51:00,670 - root - INFO - Normalized TRX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-28 09:51:00,671 - root - INFO - Normalized PEPE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-28 09:51:00,674 - root - INFO - Normalized DOGE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-28 09:51:00,676 - root - INFO - Normalized BNB/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-28 09:51:00,677 - root - INFO - Normalized DOT/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-28 09:51:00,680 - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 198 points
2025-06-28 09:51:00,680 - root - INFO - ETH/USDT B&H total return: -8.94%
2025-06-28 09:51:00,682 - root - INFO - Added equity_curve to bnh_metrics for BTC/USDT with 198 points
2025-06-28 09:51:00,682 - root - INFO - BTC/USDT B&H total return: 9.87%
2025-06-28 09:51:00,684 - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 198 points
2025-06-28 09:51:00,685 - root - INFO - SOL/USDT B&H total return: -29.12%
2025-06-28 09:51:00,687 - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 198 points
2025-06-28 09:51:00,687 - root - INFO - SUI/USDT B&H total return: -15.38%
2025-06-28 09:51:00,690 - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 198 points
2025-06-28 09:51:00,690 - root - INFO - XRP/USDT B&H total return: -11.63%
2025-06-28 09:51:00,693 - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 198 points
2025-06-28 09:51:00,693 - root - INFO - AAVE/USDT B&H total return: 2.63%
2025-06-28 09:51:00,695 - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 198 points
2025-06-28 09:51:00,695 - root - INFO - AVAX/USDT B&H total return: -31.52%
2025-06-28 09:51:00,698 - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 198 points
2025-06-28 09:51:00,699 - root - INFO - ADA/USDT B&H total return: -21.54%
2025-06-28 09:51:00,700 - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 198 points
2025-06-28 09:51:00,700 - root - INFO - LINK/USDT B&H total return: -30.62%
2025-06-28 09:51:00,702 - root - INFO - Added equity_curve to bnh_metrics for TRX/USDT with 198 points
2025-06-28 09:51:00,703 - root - INFO - TRX/USDT B&H total return: 10.97%
2025-06-28 09:51:00,705 - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 198 points
2025-06-28 09:51:00,705 - root - INFO - PEPE/USDT B&H total return: -4.28%
2025-06-28 09:51:00,706 - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 198 points
2025-06-28 09:51:00,706 - root - INFO - DOGE/USDT B&H total return: -36.95%
2025-06-28 09:51:00,708 - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 198 points
2025-06-28 09:51:00,708 - root - INFO - BNB/USDT B&H total return: 4.37%
2025-06-28 09:51:00,709 - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 198 points
2025-06-28 09:51:00,709 - root - INFO - DOT/USDT B&H total return: -30.67%
2025-06-28 09:51:00,710 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-28 09:51:00,717 - root - INFO - Configuration loaded successfully.
2025-06-28 09:51:00,723 - root - INFO - Using colored segments for single-asset strategy visualization
2025-06-28 09:51:00,806 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-28 09:51:00,814 - root - INFO - Configuration loaded successfully.
2025-06-28 09:51:02,167 - root - INFO - Added ETH/USDT buy-and-hold curve with 198 points
2025-06-28 09:51:02,167 - root - INFO - Added BTC/USDT buy-and-hold curve with 198 points
2025-06-28 09:51:02,167 - root - INFO - Added SOL/USDT buy-and-hold curve with 198 points
2025-06-28 09:51:02,167 - root - INFO - Added SUI/USDT buy-and-hold curve with 198 points
2025-06-28 09:51:02,167 - root - INFO - Added XRP/USDT buy-and-hold curve with 198 points
2025-06-28 09:51:02,168 - root - INFO - Added AAVE/USDT buy-and-hold curve with 198 points
2025-06-28 09:51:02,168 - root - INFO - Added AVAX/USDT buy-and-hold curve with 198 points
2025-06-28 09:51:02,168 - root - INFO - Added ADA/USDT buy-and-hold curve with 198 points
2025-06-28 09:51:02,168 - root - INFO - Added LINK/USDT buy-and-hold curve with 198 points
2025-06-28 09:51:02,168 - root - INFO - Added TRX/USDT buy-and-hold curve with 198 points
2025-06-28 09:51:02,169 - root - INFO - Added PEPE/USDT buy-and-hold curve with 198 points
2025-06-28 09:51:02,169 - root - INFO - Added DOGE/USDT buy-and-hold curve with 198 points
2025-06-28 09:51:02,169 - root - INFO - Added BNB/USDT buy-and-hold curve with 198 points
2025-06-28 09:51:02,169 - root - INFO - Added DOT/USDT buy-and-hold curve with 198 points
2025-06-28 09:51:02,169 - root - INFO - Added 14 buy-and-hold curves to results
2025-06-28 09:51:02,170 - root - INFO -   - ETH/USDT: 198 points from 2024-12-12 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:51:02,170 - root - INFO -   - BTC/USDT: 198 points from 2024-12-12 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:51:02,170 - root - INFO -   - SOL/USDT: 198 points from 2024-12-12 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:51:02,170 - root - INFO -   - SUI/USDT: 198 points from 2024-12-12 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:51:02,170 - root - INFO -   - XRP/USDT: 198 points from 2024-12-12 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:51:02,170 - root - INFO -   - AAVE/USDT: 198 points from 2024-12-12 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:51:02,171 - root - INFO -   - AVAX/USDT: 198 points from 2024-12-12 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:51:02,171 - root - INFO -   - ADA/USDT: 198 points from 2024-12-12 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:51:02,171 - root - INFO -   - LINK/USDT: 198 points from 2024-12-12 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:51:02,171 - root - INFO -   - TRX/USDT: 198 points from 2024-12-12 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:51:02,171 - root - INFO -   - PEPE/USDT: 198 points from 2024-12-12 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:51:02,171 - root - INFO -   - DOGE/USDT: 198 points from 2024-12-12 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:51:02,171 - root - INFO -   - BNB/USDT: 198 points from 2024-12-12 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:51:02,172 - root - INFO -   - DOT/USDT: 198 points from 2024-12-12 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:51:02,184 - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-06-28 09:51:02,184 - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-28 09:51:02,186 - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-06-28 09:51:02,186 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-28 09:51:02,196 - root - INFO - Configuration loaded successfully.
2025-06-28 09:51:02,196 - root - INFO - Loaded MTPI configuration with 8 enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-28 09:51:02,196 - root - INFO - Using MTPI indicators from config: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-28 09:51:02,197 - root - INFO - Combination method: consensus
2025-06-28 09:51:02,197 - root - INFO - Calculated appropriate limit for 1d timeframe: 216 candles (minimum 180.0 candles needed for 60 length indicator)
2025-06-28 09:51:02,197 - root - INFO - Checking cache for 1 symbols (1d)...
2025-06-28 09:51:02,216 - root - INFO - Loaded 2139 rows of BTC/USDT data from cache (last updated: 2025-06-28)
2025-06-28 09:51:02,217 - root - INFO - No incomplete daily candles to filter for current date 2025-06-28
2025-06-28 09:51:02,217 - root - INFO - Loaded 2139 rows of BTC/USDT data from cache (after filtering).
2025-06-28 09:51:02,217 - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-28 09:51:02,217 - root - INFO - Fetched BTC data: 2139 candles from 2019-08-20 00:00:00+00:00 to 2025-06-27 00:00:00+00:00
2025-06-28 09:51:02,217 - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-06-28 09:51:02,437 - root - INFO - Generated PGO Score signals: {-1: 991, 0: 34, 1: 1114}
2025-06-28 09:51:02,437 - root - INFO - PGO signal: -1
2025-06-28 09:51:02,437 - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-06-28 09:51:02,510 - root - INFO - Generated BB Score signals: {-1: 992, 0: 33, 1: 1114}
2025-06-28 09:51:02,511 - root - INFO - Bollinger Bands signal: -1
2025-06-28 09:51:04,344 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-28 09:51:04,769 - root - INFO - Received signal 2, shutting down...
2025-06-28 09:51:05,728 - root - INFO - Generated DWMA signals using Weighted SD method
2025-06-28 09:51:05,728 - root - INFO - DWMA Score signal: -1
2025-06-28 09:51:06,063 - root - INFO - Generated DEMA Supertrend signals
2025-06-28 09:51:06,063 - root - INFO - Signal distribution: {-1: 1256, 0: 1, 1: 882}
2025-06-28 09:51:06,063 - root - INFO - Generated DEMA Super Score signals
2025-06-28 09:51:06,063 - root - INFO - DEMA Super Score signal: -1
2025-06-28 09:51:06,878 - root - INFO - Generated DPSD signals
2025-06-28 09:51:06,878 - root - INFO - Signal distribution: {-1: 1082, 0: 87, 1: 970}
2025-06-28 09:51:06,878 - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-06-28 09:51:06,879 - root - INFO - DPSD Score signal: -1
2025-06-28 09:51:06,952 - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-06-28 09:51:06,952 - root - INFO - Generated AAD Score signals using SMA method
2025-06-28 09:51:06,953 - root - INFO - AAD Score signal: -1
2025-06-28 09:51:07,425 - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-06-28 09:51:07,425 - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-06-28 09:51:07,425 - root - INFO - Dynamic EMA Score signal: -1
2025-06-28 09:51:08,106 - root - INFO - Quantile DEMA Score signal: -1
2025-06-28 09:51:08,107 - root - INFO - Individual signals: {'pgo': -1, 'bollinger_bands': -1, 'dwma_score': -1, 'dema_super_score': -1, 'dpsd_score': -1, 'aad_score': -1, 'dynamic_ema_score': -1, 'quantile_dema_score': -1}
2025-06-28 09:51:08,107 - root - INFO - Combined MTPI signal (consensus): -1
2025-06-28 09:51:08,107 - root - INFO - MTPI Score: -1.000000
2025-06-28 09:51:08,108 - root - INFO - Added current MTPI score to results: -1.000000 (using 1d timeframe)
2025-06-28 09:51:08,110 - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-06-28 09:51:08,110 - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 8.0, 'BTC/USDT': 13.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 2.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-28 09:51:08,111 - root - ERROR - [DEBUG] CONVERSION - Original USDT scores: {'ETH/USDT': 8.0, 'BTC/USDT': 13.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 2.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-28 09:51:08,111 - root - ERROR - [DEBUG] CONVERSION - Original USDT keys order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-28 09:51:08,111 - root - ERROR - [DEBUG] CONVERSION - Conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-28 09:51:08,111 - root - ERROR - [DEBUG] CONVERSION - ETH/USDT -> ETH/EUR (score: 8.0)
2025-06-28 09:51:08,111 - root - ERROR - [DEBUG] CONVERSION - BTC/USDT -> BTC/EUR (score: 13.0)
2025-06-28 09:51:08,111 - root - ERROR - [DEBUG] CONVERSION - SOL/USDT -> SOL/EUR (score: 6.0)
2025-06-28 09:51:08,112 - root - ERROR - [DEBUG] CONVERSION - SUI/USDT -> SUI/EUR (score: 2.0)
2025-06-28 09:51:08,112 - root - ERROR - [DEBUG] CONVERSION - XRP/USDT -> XRP/EUR (score: 9.0)
2025-06-28 09:51:08,112 - root - ERROR - [DEBUG] CONVERSION - AAVE/USDT -> AAVE/EUR (score: 9.0)
2025-06-28 09:51:08,112 - root - ERROR - [DEBUG] CONVERSION - AVAX/USDT -> AVAX/EUR (score: 4.0)
2025-06-28 09:51:08,112 - root - ERROR - [DEBUG] CONVERSION - ADA/USDT -> ADA/EUR (score: 2.0)
2025-06-28 09:51:08,113 - root - ERROR - [DEBUG] CONVERSION - LINK/USDT -> LINK/EUR (score: 7.0)
2025-06-28 09:51:08,113 - root - ERROR - [DEBUG] CONVERSION - TRX/USDT -> TRX/EUR (score: 12.0)
2025-06-28 09:51:08,113 - root - ERROR - [DEBUG] CONVERSION - PEPE/USDT -> PEPE/EUR (score: 0.0)
2025-06-28 09:51:08,113 - root - ERROR - [DEBUG] CONVERSION - DOGE/USDT -> DOGE/EUR (score: 4.0)
2025-06-28 09:51:08,114 - root - ERROR - [DEBUG] CONVERSION - BNB/USDT -> BNB/EUR (score: 11.0)
2025-06-28 09:51:08,114 - root - ERROR - [DEBUG] CONVERSION - DOT/USDT -> DOT/EUR (score: 1.0)
2025-06-28 09:51:08,114 - root - ERROR - [DEBUG] CONVERSION - Final EUR scores: {'ETH/EUR': 8.0, 'BTC/EUR': 13.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 4.0, 'ADA/EUR': 2.0, 'LINK/EUR': 7.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 4.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-28 09:51:08,114 - root - ERROR - [DEBUG] CONVERSION - Final EUR keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-28 09:51:08,114 - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 8.0, 'BTC/EUR': 13.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 4.0, 'ADA/EUR': 2.0, 'LINK/EUR': 7.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 4.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-28 09:51:08,119 - root - INFO - Saved metrics to new file: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250621_run_********_214259.csv
2025-06-28 09:51:08,119 - root - INFO - Saved performance metrics to CSV: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250621_run_********_214259.csv
2025-06-28 09:51:08,119 - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-06-28 09:51:08,120 - root - INFO - Results type: <class 'dict'>
2025-06-28 09:51:08,120 - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-06-28 09:51:08,120 - root - INFO - Success flag set to: True
2025-06-28 09:51:08,121 - root - INFO - Message set to: Strategy calculation completed successfully
2025-06-28 09:51:08,121 - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 198 entries
2025-06-28 09:51:08,121 - root - INFO -   - buy_hold_curves: dict with 14 entries
2025-06-28 09:51:08,121 - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 198 entries
2025-06-28 09:51:08,121 - root - INFO -   - mtpi_signals: <class 'pandas.core.series.Series'> with 258 entries
2025-06-28 09:51:08,121 - root - INFO -   - mtpi_score: <class 'float'>
2025-06-28 09:51:08,122 - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 198 entries
2025-06-28 09:51:08,122 - root - INFO -   - performance_metrics: dict with 3 entries
2025-06-28 09:51:08,122 - root - INFO -   - metrics_file: <class 'str'>
2025-06-28 09:51:08,122 - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-06-28 09:51:08,122 - root - INFO -   - success: <class 'bool'>
2025-06-28 09:51:08,122 - root - INFO -   - message: <class 'str'>
2025-06-28 09:51:08,123 - root - ERROR - [DEBUG] STRATEGY RESULTS - Available keys: ['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message']
2025-06-28 09:51:08,123 - root - INFO - [DEBUG] ASSET SELECTION - Extracted best_asset from best_asset_series: 
2025-06-28 09:51:08,123 - root - INFO - [DEBUG] ASSET SELECTION - Last 5 entries in best_asset_series: 2025-06-23 00:00:00+00:00    
2025-06-24 00:00:00+00:00    
2025-06-25 00:00:00+00:00    
2025-06-26 00:00:00+00:00    
2025-06-27 00:00:00+00:00    
dtype: object
2025-06-28 09:51:08,123 - root - ERROR - [DEBUG] ASSET SELECTION - Latest scores available: {'ETH/EUR': 8.0, 'BTC/EUR': 13.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 4.0, 'ADA/EUR': 2.0, 'LINK/EUR': 7.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 4.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-28 09:51:08,123 - root - ERROR - [DEBUG] ASSET SELECTION - Scores dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-28 09:51:08,124 - root - INFO - [DEBUG] TIE-BREAKING - Strategy: imcumbent
2025-06-28 09:51:08,124 - root - WARNING - [DEBUG] ASSET SELECTION - find_best_asset_for_day() called with 14 assets
2025-06-28 09:51:08,124 - root - WARNING - [DEBUG] ASSET SELECTION - Input scores: {'ETH/EUR': 8.0, 'BTC/EUR': 13.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 4.0, 'ADA/EUR': 2.0, 'LINK/EUR': 7.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 4.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-28 09:51:08,124 - root - WARNING - [DEBUG] ASSET SELECTION - Dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-28 09:51:08,124 - root - INFO - MTPI signal is -1, staying out of the market
2025-06-28 09:51:08,124 - root - ERROR - [DEBUG] ASSET SELECTION - Current day's best asset:  (MTPI signal: -1)
2025-06-28 09:51:08,124 - root - ERROR - [DEBUG] ASSET SELECTION - Maximum score: 13.0
2025-06-28 09:51:08,124 - root - ERROR - [DEBUG] ASSET SELECTION - Assets with max score: ['BTC/EUR']
2025-06-28 09:51:08,124 - root - ERROR - [DEBUG] NO TIE - CONFIRMED SELECTION: 
2025-06-28 09:51:08,125 - root - ERROR - [DEBUG] NO TIE - Single winner: 
2025-06-28 09:51:08,140 - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
       'BNB/EUR', 'DOT/EUR'],
      dtype='object')
2025-06-28 09:51:08,140 - root - INFO - Last row columns: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-28 09:51:08,141 - root - INFO - [DEBUG] FINAL ASSET SELECTION SUMMARY:
2025-06-28 09:51:08,141 - root - INFO - [DEBUG]   - Best asset selected: 
2025-06-28 09:51:08,141 - root - INFO - [DEBUG]   - Assets held: {}
2025-06-28 09:51:08,141 - root - INFO - [DEBUG]   - MTPI signal: -1
2025-06-28 09:51:08,141 - root - INFO - [DEBUG]   - Use MTPI signal: True
2025-06-28 09:51:08,144 - root - INFO - MTPI signal is bearish (-1). Exiting all positions.
2025-06-28 09:51:08,145 - root - INFO - No open positions to exit.
2025-06-28 09:51:08,145 - root - INFO - Exit all positions result logged to trade log file
2025-06-28 09:51:08,145 - root - INFO - All positions exited successfully
2025-06-28 09:51:08,148 - root - INFO - Asset selection logged: 0 assets selected with single_asset allocation
2025-06-28 09:51:08,148 - root - INFO - Asset scores (sorted by score):
2025-06-28 09:51:08,148 - root - INFO -   BTC/EUR: score=13.0, status=NOT SELECTED, weight=0.00
2025-06-28 09:51:08,148 - root - INFO -   TRX/EUR: score=12.0, status=NOT SELECTED, weight=0.00
2025-06-28 09:51:08,148 - root - INFO -   BNB/EUR: score=11.0, status=NOT SELECTED, weight=0.00
2025-06-28 09:51:08,148 - root - INFO -   XRP/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-28 09:51:08,149 - root - INFO -   AAVE/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-28 09:51:08,149 - root - INFO -   ETH/EUR: score=8.0, status=NOT SELECTED, weight=0.00
2025-06-28 09:51:08,149 - root - INFO -   LINK/EUR: score=7.0, status=NOT SELECTED, weight=0.00
2025-06-28 09:51:08,149 - root - INFO -   SOL/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-28 09:51:08,149 - root - INFO -   AVAX/EUR: score=4.0, status=NOT SELECTED, weight=0.00
2025-06-28 09:51:08,149 - root - INFO -   DOGE/EUR: score=4.0, status=NOT SELECTED, weight=0.00
2025-06-28 09:51:08,149 - root - INFO -   SUI/EUR: score=2.0, status=NOT SELECTED, weight=0.00
2025-06-28 09:51:08,150 - root - INFO -   ADA/EUR: score=2.0, status=NOT SELECTED, weight=0.00
2025-06-28 09:51:08,150 - root - INFO -   DOT/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-06-28 09:51:08,150 - root - INFO -   PEPE/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-06-28 09:51:08,150 - root - INFO - Rejected assets:
2025-06-28 09:51:08,150 - root - INFO -   BTC/EUR: reason=Failed to trade, score=13.0, rank=1
2025-06-28 09:51:08,150 - root - WARNING -   HIGH-SCORING ASSET REJECTED: BTC/EUR (rank 1, score 13.0) - Failed to trade
2025-06-28 09:51:08,150 - root - INFO - Asset selection logged with 14 assets scored and 0 assets selected
2025-06-28 09:51:08,150 - root - INFO - Extracted asset scores: {'ETH/EUR': 8.0, 'BTC/EUR': 13.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 4.0, 'ADA/EUR': 2.0, 'LINK/EUR': 7.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 4.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-28 09:51:08,221 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-28 09:51:08,223 - root - INFO - Strategy execution completed successfully in 36.11 seconds
2025-06-28 09:51:08,227 - root - INFO - Saved recovery state to data/state\recovery_state.json
2025-06-28 09:51:08,227 - root - WARNING - Failed to process pending operation: missed_execution
2025-06-28 09:51:08,231 - root - INFO - Saved recovery state to data/state\recovery_state.json
2025-06-28 09:51:08,273 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-28 09:51:08,275 - root - INFO - Attempting to send network recovery notification via Telegram
2025-06-28 09:51:08,312 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-28 09:51:08,315 - root - INFO - Network recovery notification sent successfully
2025-06-28 09:51:08,315 - root - INFO - Successfully executed recovery callback
2025-06-28 09:51:14,420 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-28 09:51:14,814 - root - INFO - Network watchdog stopped
2025-06-28 09:51:14,814 - root - INFO - Network watchdog stopped
2025-06-28 09:51:14,815 - root - INFO - Background service stopped
2025-06-28 09:51:14,816 - asyncio - ERROR - Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):
  File "C:\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
ConnectionAbortedError: [WinError 10053] An established connection was aborted by the software in your host machine
2025-06-28 09:51:14,908 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
