2025-06-27 21:40:09,600 - root - INFO - Loaded environment variables from .env file
2025-06-27 21:40:10,678 - root - INFO - Loaded 6 trade records from logs/trades\trade_log_********.json
2025-06-27 21:40:10,678 - root - INFO - Loaded 3 asset selection records from logs/trades\asset_selection_********.json
2025-06-27 21:40:10,678 - root - INFO - Trade logger initialized with log directory: logs/trades
2025-06-27 21:40:12,062 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 21:40:12,079 - root - INFO - Configuration loaded successfully.
2025-06-27 21:40:12,084 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 21:40:12,090 - root - INFO - Configuration loaded successfully.
2025-06-27 21:40:12,090 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 21:40:12,097 - root - INFO - Configuration loaded successfully.
2025-06-27 21:40:12,097 - root - INFO - Loading notification configuration from config/notifications_bitvavo.json...
2025-06-27 21:40:12,097 - root - INFO - Notification configuration loaded successfully.
2025-06-27 21:40:13,335 - root - INFO - Telegram command handlers registered
2025-06-27 21:40:13,335 - root - INFO - Telegram bot polling started
2025-06-27 21:40:13,335 - root - INFO - Telegram notifier initialized with notification level: standard
2025-06-27 21:40:13,335 - root - INFO - Telegram notification channel initialized
2025-06-27 21:40:13,341 - root - INFO - Successfully loaded templates using utf-8 encoding
2025-06-27 21:40:13,341 - root - INFO - Loaded 24 templates from file
2025-06-27 21:40:13,341 - root - INFO - Notification manager initialized with 1 channels
2025-06-27 21:40:13,341 - root - INFO - Notification manager initialized
2025-06-27 21:40:13,341 - root - INFO - Added critical time window: 23:55 for 10 minutes - Before daily candle close (1d)
2025-06-27 21:40:13,341 - root - INFO - Added critical time window: 00:00 for 10 minutes - After daily candle close (1d)
2025-06-27 21:40:13,345 - root - INFO - Set up critical time windows for 1d timeframe
2025-06-27 21:40:13,345 - root - INFO - Network watchdog initialized with 10s check interval
2025-06-27 21:40:13,345 - root - INFO - Loaded recovery state from data/state\recovery_state.json
2025-06-27 21:40:13,350 - root - INFO - No state file found at C:\Users\<USER>\OneDrive\Stalinis kompiuteris\Asset_Screener_Python\data\state\data/state\background_service_20250626_114325.json.json
2025-06-27 21:40:13,350 - root - INFO - Recovery manager initialized
2025-06-27 21:40:13,350 - root - INFO - [DEBUG] TRADING EXECUTOR - Initializing with exchange: bitvavo
2025-06-27 21:40:13,350 - root - INFO - [DEBUG] TRADING EXECUTOR - Trading config: {'enabled': True, 'exchange': 'bitvavo', 'initial_capital': 100, 'max_slippage_pct': 0.5, 'min_order_values': {'default': 5.0}, 'mode': 'paper', 'order_type': 'market', 'position_size_pct': 10, 'risk_management': {'max_daily_trades': 5, 'max_open_positions': 10}, 'transaction_fee_rate': 0.0025}
2025-06-27 21:40:13,350 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 21:40:13,364 - root - INFO - Configuration loaded successfully.
2025-06-27 21:40:13,364 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-27 21:40:13,364 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-27 21:40:13,364 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-27 21:40:13,364 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-27 21:40:13,364 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-27 21:40:13,364 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-27 21:40:13,364 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-27 21:40:13,364 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-27 21:40:13,364 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 21:40:13,374 - root - INFO - Configuration loaded successfully.
2025-06-27 21:40:13,666 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getMe "HTTP/1.1 200 OK"
2025-06-27 21:40:13,682 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-27 21:40:13,682 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-27 21:40:13,682 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-27 21:40:13,682 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-27 21:40:13,682 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-27 21:40:13,689 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 21:40:13,698 - root - INFO - Configuration loaded successfully.
2025-06-27 21:40:13,698 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 21:40:13,707 - root - INFO - Configuration loaded successfully.
2025-06-27 21:40:13,707 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-27 21:40:13,707 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/deleteWebhook "HTTP/1.1 200 OK"
2025-06-27 21:40:13,721 - root - INFO - Configuration loaded successfully.
2025-06-27 21:40:13,724 - telegram.ext.Application - INFO - Application started
2025-06-27 21:40:13,725 - root - INFO - Loaded paper trading history from paper_trading_history.json
2025-06-27 21:40:13,725 - root - INFO - Paper trading initialized with EUR as quote currency
2025-06-27 21:40:13,725 - root - INFO - Trading executor initialized for bitvavo
2025-06-27 21:40:13,725 - root - INFO - Trading mode: paper
2025-06-27 21:40:13,725 - root - INFO - Trading enabled: True
2025-06-27 21:40:13,725 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-27 21:40:13,725 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-27 21:40:13,725 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-27 21:40:13,725 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-27 21:40:13,725 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 21:40:13,737 - root - INFO - Configuration loaded successfully.
2025-06-27 21:40:13,984 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-27 21:40:13,984 - root - INFO - Trading enabled in paper mode
2025-06-27 21:40:13,984 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-27 21:40:13,984 - root - INFO - Reset paper trading account to initial balance: 100 EUR
2025-06-27 21:40:13,984 - root - INFO - Reset paper trading account to initial balance
2025-06-27 21:40:13,990 - root - INFO - Generated run ID: ********_214013
2025-06-27 21:40:13,990 - root - INFO - Ensured metrics directory exists: Performance_Metrics
2025-06-27 21:40:13,990 - root - INFO - Background service initialized
2025-06-27 21:40:13,990 - root - INFO - Network watchdog started
2025-06-27 21:40:13,992 - root - INFO - Network watchdog started
2025-06-27 21:40:13,993 - root - INFO - Schedule set up for 1d timeframe
2025-06-27 21:40:13,993 - root - INFO - Background service started
2025-06-27 21:40:13,993 - root - INFO - Executing strategy (run #1)...
2025-06-27 21:40:13,993 - root - INFO - Resetting daily trade counters for this strategy execution
2025-06-27 21:40:13,993 - root - INFO - No trades recorded today (Max: 5)
2025-06-27 21:40:13,993 - root - INFO - Initialized daily trades counter for 2025-06-27
2025-06-27 21:40:13,993 - root - INFO - Creating snapshot for candle timestamp: ********
2025-06-27 21:40:14,148 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-27 21:40:17,783 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 409 Conflict"
2025-06-27 21:40:17,783 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_baserequest.py", line 355, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-06-27 21:40:20,014 - root - WARNING - Failed to send with Markdown formatting: Timed out
2025-06-27 21:40:20,092 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-27 21:40:20,092 - root - INFO - Using trend method 'PGO For Loop' for strategy execution
2025-06-27 21:40:20,098 - root - INFO - Using configured start date for 1d timeframe: 2025-02-10
2025-06-27 21:40:20,098 - root - INFO - Using recent date for performance tracking: 2025-06-20
2025-06-27 21:40:20,102 - root - INFO - Using real-time data fetching - only fetching new data since last cached timestamp
2025-06-27 21:40:20,194 - root - INFO - Loaded 2138 rows of ETH/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:40:20,195 - root - INFO - Last timestamp in cache for ETH/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:40:20,196 - root - INFO - Expected last timestamp for ETH/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:40:20,196 - root - INFO - Data is up to date for ETH/USDT
2025-06-27 21:40:20,200 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:40:20,252 - root - INFO - Loaded 2138 rows of BTC/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:40:20,253 - root - INFO - Last timestamp in cache for BTC/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:40:20,254 - root - INFO - Expected last timestamp for BTC/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:40:20,255 - root - INFO - Data is up to date for BTC/USDT
2025-06-27 21:40:20,257 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:40:20,300 - root - INFO - Loaded 1781 rows of SOL/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:40:20,301 - root - INFO - Last timestamp in cache for SOL/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:40:20,302 - root - INFO - Expected last timestamp for SOL/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:40:20,303 - root - INFO - Data is up to date for SOL/USDT
2025-06-27 21:40:20,305 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:40:20,333 - root - INFO - Loaded 786 rows of SUI/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:40:20,334 - root - INFO - Last timestamp in cache for SUI/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:40:20,335 - root - INFO - Expected last timestamp for SUI/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:40:20,336 - root - INFO - Data is up to date for SUI/USDT
2025-06-27 21:40:20,337 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:40:20,384 - root - INFO - Loaded 2138 rows of XRP/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:40:20,385 - root - INFO - Last timestamp in cache for XRP/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:40:20,386 - root - INFO - Expected last timestamp for XRP/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:40:20,387 - root - INFO - Data is up to date for XRP/USDT
2025-06-27 21:40:20,389 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:40:20,429 - root - INFO - Loaded 1716 rows of AAVE/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:40:20,429 - root - INFO - Last timestamp in cache for AAVE/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:40:20,429 - root - INFO - Expected last timestamp for AAVE/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:40:20,429 - root - INFO - Data is up to date for AAVE/USDT
2025-06-27 21:40:20,439 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:40:20,488 - root - INFO - Loaded 1739 rows of AVAX/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:40:20,488 - root - INFO - Last timestamp in cache for AVAX/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:40:20,491 - root - INFO - Expected last timestamp for AVAX/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:40:20,491 - root - INFO - Data is up to date for AVAX/USDT
2025-06-27 21:40:20,491 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:40:20,542 - root - INFO - Loaded 2138 rows of ADA/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:40:20,545 - root - INFO - Last timestamp in cache for ADA/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:40:20,545 - root - INFO - Expected last timestamp for ADA/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:40:20,545 - root - INFO - Data is up to date for ADA/USDT
2025-06-27 21:40:20,545 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:40:20,598 - root - INFO - Loaded 2138 rows of LINK/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:40:20,598 - root - INFO - Last timestamp in cache for LINK/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:40:20,598 - root - INFO - Expected last timestamp for LINK/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:40:20,598 - root - INFO - Data is up to date for LINK/USDT
2025-06-27 21:40:20,603 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:40:20,647 - root - INFO - Loaded 2138 rows of TRX/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:40:20,647 - root - INFO - Last timestamp in cache for TRX/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:40:20,647 - root - INFO - Expected last timestamp for TRX/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:40:20,647 - root - INFO - Data is up to date for TRX/USDT
2025-06-27 21:40:20,657 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:40:20,684 - root - INFO - Loaded 784 rows of PEPE/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:40:20,684 - root - INFO - Last timestamp in cache for PEPE/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:40:20,684 - root - INFO - Expected last timestamp for PEPE/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:40:20,684 - root - INFO - Data is up to date for PEPE/USDT
2025-06-27 21:40:20,684 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:40:20,739 - root - INFO - Loaded 2138 rows of DOGE/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:40:20,741 - root - INFO - Last timestamp in cache for DOGE/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:40:20,743 - root - INFO - Expected last timestamp for DOGE/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:40:20,743 - root - INFO - Data is up to date for DOGE/USDT
2025-06-27 21:40:20,745 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:40:20,798 - root - INFO - Loaded 2138 rows of BNB/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:40:20,798 - root - INFO - Last timestamp in cache for BNB/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:40:20,798 - root - INFO - Expected last timestamp for BNB/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:40:20,798 - root - INFO - Data is up to date for BNB/USDT
2025-06-27 21:40:20,801 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:40:20,856 - root - INFO - Loaded 1774 rows of DOT/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:40:20,858 - root - INFO - Last timestamp in cache for DOT/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:40:20,860 - root - INFO - Expected last timestamp for DOT/USDT: 2025-06-26 00:00:00+00:00
2025-06-27 21:40:20,860 - root - INFO - Data is up to date for DOT/USDT
2025-06-27 21:40:20,863 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:40:20,867 - root - INFO - Using 14 trend assets (USDT) for analysis and 14 trading assets (EUR) for execution
2025-06-27 21:40:20,868 - root - INFO - MTPI Multi-Indicator Configuration:
2025-06-27 21:40:20,868 - root - INFO -   - Number of indicators: 8
2025-06-27 21:40:20,869 - root - INFO -   - Enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-27 21:40:20,869 - root - INFO -   - Combination method: consensus
2025-06-27 21:40:20,870 - root - INFO -   - Long threshold: 0.1
2025-06-27 21:40:20,870 - root - INFO -   - Short threshold: -0.1
2025-06-27 21:40:20,870 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trend_assets order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-27 21:40:20,871 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trading_assets order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-27 21:40:20,871 - root - ERROR - [DEBUG] BACKGROUND SERVICE - BTC position in trend_assets: 2
2025-06-27 21:40:20,872 - root - ERROR - [DEBUG] BACKGROUND SERVICE - TRX position in trend_assets: 10
2025-06-27 21:40:20,872 - root - INFO - === RUNNING STRATEGY FOR WEB USING TEST_ALLOCATION ===
2025-06-27 21:40:20,873 - root - INFO - Parameters: use_mtpi_signal=False, mtpi_indicator_type=PGO
2025-06-27 21:40:20,873 - root - INFO - mtpi_timeframe=1d, mtpi_pgo_length=35
2025-06-27 21:40:20,874 - root - INFO - mtpi_upper_threshold=1.1, mtpi_lower_threshold=-0.58
2025-06-27 21:40:20,874 - root - INFO - timeframe=1d, analysis_start_date='2025-02-10'
2025-06-27 21:40:20,875 - root - INFO - n_assets=1, use_weighted_allocation=False
2025-06-27 21:40:20,876 - root - INFO - Using provided trend method: PGO For Loop
2025-06-27 21:40:20,876 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 21:40:20,905 - root - INFO - Configuration loaded successfully.
2025-06-27 21:40:20,906 - root - INFO - Saving configuration to config/settings_bitvavo_eur.yaml...
2025-06-27 21:40:20,927 - root - INFO - Configuration saved successfully.
2025-06-27 21:40:20,927 - root - INFO - Trend detection assets (USDT): ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-27 21:40:20,928 - root - INFO - Number of trend detection assets: 14
2025-06-27 21:40:20,929 - root - INFO - Selected assets type: <class 'list'>
2025-06-27 21:40:20,929 - root - INFO - Trading execution assets (EUR): ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-27 21:40:20,930 - root - INFO - Number of trading assets: 14
2025-06-27 21:40:20,930 - root - INFO - Trading assets type: <class 'list'>
2025-06-27 21:40:21,635 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-27 21:40:21,669 - root - INFO - Configuration loaded successfully.
2025-06-27 21:40:21,711 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-27 21:40:21,750 - root - INFO - Configuration loaded successfully.
2025-06-27 21:40:21,751 - root - INFO - Execution context: backtesting
2025-06-27 21:40:21,751 - root - INFO - Execution timing: candle_close
2025-06-27 21:40:21,752 - root - INFO - Ratio calculation method: independent
2025-06-27 21:40:21,752 - root - INFO - Tie-breaking strategy: imcumbent
2025-06-27 21:40:21,754 - root - INFO - Asset trend PGO parameters: length=None, upper_threshold=None, lower_threshold=None
2025-06-27 21:40:21,754 - root - INFO - MTPI indicators override: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-27 21:40:21,756 - root - INFO - MTPI combination method override: consensus
2025-06-27 21:40:21,756 - root - INFO - MTPI long threshold override: 0.1
2025-06-27 21:40:21,757 - root - INFO - MTPI short threshold override: -0.1
2025-06-27 21:40:21,758 - root - INFO - Using standard warmup period of 60 days for equal allocation
2025-06-27 21:40:21,759 - root - INFO - Fetching data from 2024-12-12 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-27 21:40:21,763 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:40:21,765 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:40:21,766 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:40:21,767 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:40:21,769 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:40:21,770 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:40:21,771 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:40:21,773 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:40:21,775 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:40:21,776 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:40:21,778 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:40:21,780 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:40:21,781 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:40:21,783 - root - INFO - Loaded metadata for 48 assets
2025-06-27 21:40:21,783 - root - INFO - Checking cache for 14 symbols (1d)...
2025-06-27 21:40:21,847 - root - INFO - Loaded 197 rows of ETH/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:40:21,851 - root - INFO - Metadata for ETH/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:40:21,853 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:40:21,853 - root - INFO - Loaded 197 rows of ETH/USDT data from cache (after filtering).
2025-06-27 21:40:21,923 - root - INFO - Loaded 197 rows of BTC/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:40:21,927 - root - INFO - Metadata for BTC/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:40:21,931 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:40:21,933 - root - INFO - Loaded 197 rows of BTC/USDT data from cache (after filtering).
2025-06-27 21:40:21,981 - root - INFO - Loaded 197 rows of SOL/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:40:21,984 - root - INFO - Metadata for SOL/USDT shows data starting from 2020-08-11, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:40:21,985 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:40:21,986 - root - INFO - Loaded 197 rows of SOL/USDT data from cache (after filtering).
2025-06-27 21:40:22,016 - root - INFO - Loaded 197 rows of SUI/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:40:22,019 - root - INFO - Metadata for SUI/USDT shows data starting from 2023-05-03, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:40:22,021 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:40:22,021 - root - INFO - Loaded 197 rows of SUI/USDT data from cache (after filtering).
2025-06-27 21:40:22,061 - root - INFO - Loaded 197 rows of XRP/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:40:22,063 - root - INFO - Metadata for XRP/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:40:22,063 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:40:22,063 - root - INFO - Loaded 197 rows of XRP/USDT data from cache (after filtering).
2025-06-27 21:40:22,119 - root - INFO - Loaded 197 rows of AAVE/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:40:22,119 - root - INFO - Metadata for AAVE/USDT shows data starting from 2020-10-15, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:40:22,123 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:40:22,123 - root - INFO - Loaded 197 rows of AAVE/USDT data from cache (after filtering).
2025-06-27 21:40:22,173 - root - INFO - Loaded 197 rows of AVAX/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:40:22,183 - root - INFO - Metadata for AVAX/USDT shows data starting from 2020-09-22, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:40:22,183 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:40:22,183 - root - INFO - Loaded 197 rows of AVAX/USDT data from cache (after filtering).
2025-06-27 21:40:22,240 - root - INFO - Loaded 197 rows of ADA/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:40:22,250 - root - INFO - Metadata for ADA/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:40:22,250 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:40:22,250 - root - INFO - Loaded 197 rows of ADA/USDT data from cache (after filtering).
2025-06-27 21:40:22,301 - root - INFO - Loaded 197 rows of LINK/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:40:22,307 - root - INFO - Metadata for LINK/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:40:22,307 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:40:22,307 - root - INFO - Loaded 197 rows of LINK/USDT data from cache (after filtering).
2025-06-27 21:40:22,366 - root - INFO - Loaded 197 rows of TRX/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:40:22,366 - root - INFO - Metadata for TRX/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:40:22,376 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:40:22,377 - root - INFO - Loaded 197 rows of TRX/USDT data from cache (after filtering).
2025-06-27 21:40:22,411 - root - INFO - Loaded 197 rows of PEPE/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:40:22,417 - root - INFO - Metadata for PEPE/USDT shows data starting from 2023-05-05, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:40:22,417 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:40:22,417 - root - INFO - Loaded 197 rows of PEPE/USDT data from cache (after filtering).
2025-06-27 21:40:22,492 - root - INFO - Loaded 197 rows of DOGE/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:40:22,497 - root - INFO - Metadata for DOGE/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:40:22,497 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:40:22,497 - root - INFO - Loaded 197 rows of DOGE/USDT data from cache (after filtering).
2025-06-27 21:40:22,573 - root - INFO - Loaded 197 rows of BNB/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:40:22,575 - root - INFO - Metadata for BNB/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:40:22,575 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:40:22,575 - root - INFO - Loaded 197 rows of BNB/USDT data from cache (after filtering).
2025-06-27 21:40:22,633 - root - INFO - Loaded 197 rows of DOT/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:40:22,641 - root - INFO - Metadata for DOT/USDT shows data starting from 2020-08-18, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-27 21:40:22,641 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:40:22,641 - root - INFO - Loaded 197 rows of DOT/USDT data from cache (after filtering).
2025-06-27 21:40:22,641 - root - INFO - All 14 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-27 21:40:22,641 - root - INFO - Asset ETH/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:40:22,641 - root - INFO - Asset BTC/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:40:22,641 - root - INFO - Asset SOL/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:40:22,652 - root - INFO - Asset SUI/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:40:22,652 - root - INFO - Asset XRP/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:40:22,652 - root - INFO - Asset AAVE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:40:22,652 - root - INFO - Asset AVAX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:40:22,652 - root - INFO - Asset ADA/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:40:22,652 - root - INFO - Asset LINK/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:40:22,652 - root - INFO - Asset TRX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:40:22,657 - root - INFO - Asset PEPE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:40:22,657 - root - INFO - Asset DOGE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:40:22,657 - root - INFO - Asset BNB/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:40:22,657 - root - INFO - Asset DOT/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-27 21:40:22,719 - root - INFO - Using standard MTPI warmup period of 120 days
2025-06-27 21:40:22,719 - root - INFO - Fetching MTPI signals from 2024-10-13 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-27 21:40:22,721 - root - INFO - Fetching MTPI signals using trend method: PGO For Loop
2025-06-27 21:40:22,721 - root - INFO - Using multi-indicator MTPI with config override: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-27 21:40:22,721 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-27 21:40:22,751 - root - INFO - Configuration loaded successfully.
2025-06-27 21:40:22,753 - root - INFO - Loaded MTPI multi-indicator configuration with 8 enabled indicators
2025-06-27 21:40:22,753 - root - INFO - Applying configuration overrides: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-27 21:40:22,753 - root - INFO - Override: enabled_indicators = ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-27 21:40:22,755 - root - INFO - Override: combination_method = consensus
2025-06-27 21:40:22,755 - root - INFO - Override: long_threshold = 0.1
2025-06-27 21:40:22,755 - root - INFO - Override: short_threshold = -0.1
2025-06-27 21:40:22,757 - root - INFO - Using MTPI configuration: indicators=['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], method=consensus, thresholds=(-0.1, 0.1)
2025-06-27 21:40:22,757 - root - INFO - Calculated appropriate limit for 1d timeframe: 500 candles (minimum 180.0 candles needed for 60 length indicator)
2025-06-27 21:40:22,757 - root - INFO - Using adjusted limit: 500 for max indicator length: 60
2025-06-27 21:40:22,759 - root - INFO - Checking cache for 1 symbols (1d)...
2025-06-27 21:40:22,818 - root - INFO - Loaded 257 rows of BTC/USDT data from cache (last updated: 2025-06-27)
2025-06-27 21:40:22,823 - root - INFO - No incomplete daily candles to filter for current date 2025-06-27
2025-06-27 21:40:22,823 - root - INFO - Loaded 257 rows of BTC/USDT data from cache (after filtering).
2025-06-27 21:40:22,823 - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-27 21:40:22,823 - root - INFO - Fetched BTC data: 257 candles from 2024-10-13 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:40:22,823 - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-06-27 21:40:22,941 - root - INFO - Generated PGO Score signals: {-1: 105, 0: 34, 1: 118}
2025-06-27 21:40:22,941 - root - INFO - Generated pgo signals: 257 values
2025-06-27 21:40:22,941 - root - INFO - Generating Bollinger Band signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False, heikin_src=close
2025-06-27 21:40:22,941 - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-06-27 21:40:22,990 - root - INFO - Generated BB Score signals: {-1: 107, 0: 32, 1: 118}
2025-06-27 21:40:22,990 - root - INFO - Generated Bollinger Band signals: 257 values
2025-06-27 21:40:22,990 - root - INFO - Generated bollinger_bands signals: 257 values
2025-06-27 21:40:24,894 - root - INFO - Generated DWMA signals using Weighted SD method
2025-06-27 21:40:24,894 - root - INFO - Generated dwma_score signals: 257 values
2025-06-27 21:40:25,121 - root - INFO - Generated DEMA Supertrend signals
2025-06-27 21:40:25,121 - root - INFO - Signal distribution: {-1: 143, 0: 1, 1: 113}
2025-06-27 21:40:25,121 - root - INFO - Generated DEMA Super Score signals
2025-06-27 21:40:25,123 - root - INFO - Generated dema_super_score signals: 257 values
2025-06-27 21:40:25,417 - root - INFO - Generated DPSD signals
2025-06-27 21:40:25,417 - root - INFO - Signal distribution: {-1: 99, 0: 87, 1: 71}
2025-06-27 21:40:25,417 - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-06-27 21:40:25,417 - root - INFO - Generated dpsd_score signals: 257 values
2025-06-27 21:40:25,457 - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-06-27 21:40:25,457 - root - INFO - Generated AAD Score signals using SMA method
2025-06-27 21:40:25,457 - root - INFO - Generated aad_score signals: 257 values
2025-06-27 21:40:25,660 - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-06-27 21:40:25,660 - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-06-27 21:40:25,660 - root - INFO - Generated dynamic_ema_score signals: 257 values
2025-06-27 21:40:25,987 - root - INFO - Generated quantile_dema_score signals: 257 values
2025-06-27 21:40:26,014 - root - INFO - Combined 8 signals using arithmetic mean aggregation
2025-06-27 21:40:26,014 - root - INFO - Signal distribution: {1: 145, -1: 111, 0: 1}
2025-06-27 21:40:26,014 - root - INFO - Generated combined MTPI signals: 257 values using consensus method
2025-06-27 21:40:26,022 - root - INFO - Signal distribution: {1: 145, -1: 111, 0: 1}
2025-06-27 21:40:26,022 - root - INFO - Calculating daily scores using trend method: PGO For Loop...
2025-06-27 21:40:26,023 - root - INFO - Saving configuration to config/settings.yaml...
2025-06-27 21:40:26,050 - root - INFO - Configuration saved successfully.
2025-06-27 21:40:26,050 - root - INFO - Updated config with trend method: PGO For Loop and PGO parameters
2025-06-27 21:40:26,050 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-27 21:40:26,083 - root - INFO - Configuration loaded successfully.
2025-06-27 21:40:26,083 - root - INFO - Using ratio-based approach with PGO (PGO For Loop)...
2025-06-27 21:40:26,090 - root - INFO - Calculating ratio PGO signals for 14 assets with PGO(35) using full OHLCV data
2025-06-27 21:40:26,091 - root - INFO - Using ratio calculation method: independent
2025-06-27 21:40:26,165 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:26,243 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BTC/USDT using full OHLCV data
2025-06-27 21:40:26,319 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 21:40:26,319 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:26,374 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 21:40:26,400 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SOL/USDT using full OHLCV data
2025-06-27 21:40:26,461 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-27 21:40:26,461 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:26,523 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-27 21:40:26,557 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SUI/USDT using full OHLCV data
2025-06-27 21:40:26,620 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 21:40:26,620 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:26,673 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 21:40:26,699 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/XRP/USDT using full OHLCV data
2025-06-27 21:40:26,764 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 21:40:26,764 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:26,828 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 21:40:26,850 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AAVE/USDT using full OHLCV data
2025-06-27 21:40:26,916 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-27 21:40:26,920 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:26,974 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-27 21:40:27,001 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AVAX/USDT using full OHLCV data
2025-06-27 21:40:27,067 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 21:40:27,067 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:27,130 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 21:40:27,157 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/ADA/USDT using full OHLCV data
2025-06-27 21:40:27,223 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-27 21:40:27,223 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:27,281 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-27 21:40:27,299 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/LINK/USDT using full OHLCV data
2025-06-27 21:40:27,356 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-27 21:40:27,356 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:27,418 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-27 21:40:27,441 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/TRX/USDT using full OHLCV data
2025-06-27 21:40:27,510 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 21:40:27,510 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:27,573 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 21:40:27,593 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/PEPE/USDT using full OHLCV data
2025-06-27 21:40:27,663 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:40:27,663 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:27,724 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:40:27,747 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOGE/USDT using full OHLCV data
2025-06-27 21:40:27,815 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:27,886 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BNB/USDT using full OHLCV data
2025-06-27 21:40:27,960 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-27 21:40:27,960 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:28,012 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-27 21:40:28,047 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOT/USDT using full OHLCV data
2025-06-27 21:40:28,115 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 21:40:28,115 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:28,177 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 21:40:28,196 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ETH/USDT using full OHLCV data
2025-06-27 21:40:28,268 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:28,341 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SOL/USDT using full OHLCV data
2025-06-27 21:40:28,417 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 21:40:28,417 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:28,479 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 21:40:28,498 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SUI/USDT using full OHLCV data
2025-06-27 21:40:28,573 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 21:40:28,573 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:28,632 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 21:40:28,657 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/XRP/USDT using full OHLCV data
2025-06-27 21:40:28,725 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 21:40:28,725 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:28,785 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 21:40:28,808 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AAVE/USDT using full OHLCV data
2025-06-27 21:40:28,876 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-27 21:40:28,876 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:28,936 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-27 21:40:28,959 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AVAX/USDT using full OHLCV data
2025-06-27 21:40:29,029 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 21:40:29,030 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:29,080 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 21:40:29,101 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ADA/USDT using full OHLCV data
2025-06-27 21:40:29,174 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 21:40:29,184 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:29,184 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:40:29,246 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 21:40:29,273 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/LINK/USDT using full OHLCV data
2025-06-27 21:40:29,340 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:40:29,340 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:29,398 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:40:29,417 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/TRX/USDT using full OHLCV data
2025-06-27 21:40:29,483 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 21:40:29,483 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:29,545 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 21:40:29,570 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/PEPE/USDT using full OHLCV data
2025-06-27 21:40:29,644 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:29,716 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOGE/USDT using full OHLCV data
2025-06-27 21:40:29,782 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:29,870 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/BNB/USDT using full OHLCV data
2025-06-27 21:40:29,932 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-27 21:40:29,932 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:29,997 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-27 21:40:30,017 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOT/USDT using full OHLCV data
2025-06-27 21:40:30,079 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-27 21:40:30,079 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:30,144 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-27 21:40:30,173 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ETH/USDT using full OHLCV data
2025-06-27 21:40:30,240 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:30,318 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BTC/USDT using full OHLCV data
2025-06-27 21:40:30,384 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:40:30,384 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:30,446 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:40:30,473 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/SUI/USDT using full OHLCV data
2025-06-27 21:40:30,542 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 21:40:30,542 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:30,598 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 21:40:30,625 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/XRP/USDT using full OHLCV data
2025-06-27 21:40:30,688 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-27 21:40:30,688 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:30,750 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-27 21:40:30,774 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AAVE/USDT using full OHLCV data
2025-06-27 21:40:30,840 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-27 21:40:30,840 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:30,898 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-27 21:40:30,926 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AVAX/USDT using full OHLCV data
2025-06-27 21:40:30,987 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 21:40:30,987 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:31,057 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 21:40:31,081 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ADA/USDT using full OHLCV data
2025-06-27 21:40:31,150 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 21:40:31,150 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:31,219 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 21:40:31,240 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/LINK/USDT using full OHLCV data
2025-06-27 21:40:31,310 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-27 21:40:31,310 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:31,374 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-27 21:40:31,397 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/TRX/USDT using full OHLCV data
2025-06-27 21:40:31,469 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-27 21:40:31,469 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:31,530 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-27 21:40:31,552 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/PEPE/USDT using full OHLCV data
2025-06-27 21:40:31,620 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:40:31,620 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:31,680 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:40:31,698 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOGE/USDT using full OHLCV data
2025-06-27 21:40:31,767 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:40:31,773 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:31,833 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:40:31,847 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BNB/USDT using full OHLCV data
2025-06-27 21:40:31,923 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 21:40:31,923 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:31,986 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 21:40:32,007 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOT/USDT using full OHLCV data
2025-06-27 21:40:32,074 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:40:32,080 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:32,140 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:40:32,174 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ETH/USDT using full OHLCV data
2025-06-27 21:40:32,240 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:32,319 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BTC/USDT using full OHLCV data
2025-06-27 21:40:32,386 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:32,463 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/SOL/USDT using full OHLCV data
2025-06-27 21:40:32,525 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:32,596 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/XRP/USDT using full OHLCV data
2025-06-27 21:40:32,675 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-27 21:40:32,675 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:32,739 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-27 21:40:32,757 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AAVE/USDT using full OHLCV data
2025-06-27 21:40:32,828 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:32,899 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AVAX/USDT using full OHLCV data
2025-06-27 21:40:32,967 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:33,054 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ADA/USDT using full OHLCV data
2025-06-27 21:40:33,106 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:40:33,106 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:33,173 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:40:33,197 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/LINK/USDT using full OHLCV data
2025-06-27 21:40:33,264 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:33,341 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/TRX/USDT using full OHLCV data
2025-06-27 21:40:33,407 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:33,487 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/PEPE/USDT using full OHLCV data
2025-06-27 21:40:33,547 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:40:33,556 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:33,623 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:40:33,642 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOGE/USDT using full OHLCV data
2025-06-27 21:40:33,706 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:33,782 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BNB/USDT using full OHLCV data
2025-06-27 21:40:33,860 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:33,939 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOT/USDT using full OHLCV data
2025-06-27 21:40:33,993 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-27 21:40:34,004 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:34,064 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-27 21:40:34,083 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ETH/USDT using full OHLCV data
2025-06-27 21:40:34,147 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:34,248 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BTC/USDT using full OHLCV data
2025-06-27 21:40:34,323 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:34,404 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SOL/USDT using full OHLCV data
2025-06-27 21:40:34,470 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 21:40:34,470 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:34,532 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 21:40:34,548 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SUI/USDT using full OHLCV data
2025-06-27 21:40:34,623 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:40:34,623 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:34,674 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:40:34,702 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AAVE/USDT using full OHLCV data
2025-06-27 21:40:34,765 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-27 21:40:34,765 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:34,827 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-27 21:40:34,846 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AVAX/USDT using full OHLCV data
2025-06-27 21:40:34,916 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-27 21:40:34,916 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:34,978 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-27 21:40:35,000 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ADA/USDT using full OHLCV data
2025-06-27 21:40:35,094 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:40:35,094 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:35,167 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:40:35,192 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/LINK/USDT using full OHLCV data
2025-06-27 21:40:35,286 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 21:40:35,286 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:35,366 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 21:40:35,392 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/TRX/USDT using full OHLCV data
2025-06-27 21:40:35,470 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:40:35,470 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:35,535 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-27 21:40:35,552 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/PEPE/USDT using full OHLCV data
2025-06-27 21:40:35,626 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:35,708 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOGE/USDT using full OHLCV data
2025-06-27 21:40:35,774 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:35,852 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BNB/USDT using full OHLCV data
2025-06-27 21:40:35,923 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-27 21:40:35,923 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:35,980 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-27 21:40:36,007 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOT/USDT using full OHLCV data
2025-06-27 21:40:36,081 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:36,140 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ETH/USDT using full OHLCV data
2025-06-27 21:40:36,181 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:36,235 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BTC/USDT using full OHLCV data
2025-06-27 21:40:36,310 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:36,407 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SOL/USDT using full OHLCV data
2025-06-27 21:40:36,472 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:36,543 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SUI/USDT using full OHLCV data
2025-06-27 21:40:36,629 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:36,722 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/XRP/USDT using full OHLCV data
2025-06-27 21:40:36,801 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 21:40:36,801 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:36,860 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 21:40:36,890 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/AVAX/USDT using full OHLCV data
2025-06-27 21:40:36,969 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:40:36,969 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:37,040 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:40:37,058 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ADA/USDT using full OHLCV data
2025-06-27 21:40:37,138 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 21:40:37,138 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:37,219 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 21:40:37,244 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/LINK/USDT using full OHLCV data
2025-06-27 21:40:37,319 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:37,425 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/TRX/USDT using full OHLCV data
2025-06-27 21:40:37,511 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:37,617 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/PEPE/USDT using full OHLCV data
2025-06-27 21:40:37,702 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-27 21:40:37,702 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:37,775 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-27 21:40:37,800 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOGE/USDT using full OHLCV data
2025-06-27 21:40:37,888 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:37,977 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BNB/USDT using full OHLCV data
2025-06-27 21:40:38,068 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:40:38,068 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:38,157 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:40:38,183 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOT/USDT using full OHLCV data
2025-06-27 21:40:38,282 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:38,374 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ETH/USDT using full OHLCV data
2025-06-27 21:40:38,474 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:38,571 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BTC/USDT using full OHLCV data
2025-06-27 21:40:38,652 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:38,768 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SOL/USDT using full OHLCV data
2025-06-27 21:40:38,852 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:38,937 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SUI/USDT using full OHLCV data
2025-06-27 21:40:39,023 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:39,116 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/XRP/USDT using full OHLCV data
2025-06-27 21:40:39,207 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-27 21:40:39,207 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:39,297 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-27 21:40:39,297 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:40:39,330 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/AAVE/USDT using full OHLCV data
2025-06-27 21:40:39,406 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:39,486 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ADA/USDT using full OHLCV data
2025-06-27 21:40:39,557 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-27 21:40:39,557 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:39,621 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-27 21:40:39,641 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/LINK/USDT using full OHLCV data
2025-06-27 21:40:39,709 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:39,781 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/TRX/USDT using full OHLCV data
2025-06-27 21:40:39,859 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:39,934 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/PEPE/USDT using full OHLCV data
2025-06-27 21:40:40,004 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:40,086 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOGE/USDT using full OHLCV data
2025-06-27 21:40:40,153 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:40,232 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BNB/USDT using full OHLCV data
2025-06-27 21:40:40,301 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:40,374 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOT/USDT using full OHLCV data
2025-06-27 21:40:40,450 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:40:40,450 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:40,502 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:40:40,529 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/ETH/USDT using full OHLCV data
2025-06-27 21:40:40,590 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:40,677 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BTC/USDT using full OHLCV data
2025-06-27 21:40:40,743 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:40,823 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SOL/USDT using full OHLCV data
2025-06-27 21:40:40,881 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-27 21:40:40,891 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:40,945 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-27 21:40:40,977 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SUI/USDT using full OHLCV data
2025-06-27 21:40:41,044 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:41,125 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/XRP/USDT using full OHLCV data
2025-06-27 21:40:41,191 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-27 21:40:41,191 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:41,253 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-27 21:40:41,274 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AAVE/USDT using full OHLCV data
2025-06-27 21:40:41,342 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 21:40:41,342 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:41,399 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 21:40:41,428 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AVAX/USDT using full OHLCV data
2025-06-27 21:40:41,490 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-27 21:40:41,497 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:41,557 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-27 21:40:41,573 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/LINK/USDT using full OHLCV data
2025-06-27 21:40:41,646 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:41,727 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/TRX/USDT using full OHLCV data
2025-06-27 21:40:41,793 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 21:40:41,793 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:41,857 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 21:40:41,876 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/PEPE/USDT using full OHLCV data
2025-06-27 21:40:41,940 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:40:41,940 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:42,007 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:40:42,031 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOGE/USDT using full OHLCV data
2025-06-27 21:40:42,097 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:42,181 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BNB/USDT using full OHLCV data
2025-06-27 21:40:42,247 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:40:42,247 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:42,307 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:40:42,324 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOT/USDT using full OHLCV data
2025-06-27 21:40:42,394 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:42,471 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ETH/USDT using full OHLCV data
2025-06-27 21:40:42,543 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:42,619 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BTC/USDT using full OHLCV data
2025-06-27 21:40:42,690 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:42,765 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SOL/USDT using full OHLCV data
2025-06-27 21:40:42,836 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:42,914 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SUI/USDT using full OHLCV data
2025-06-27 21:40:42,981 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:43,075 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/XRP/USDT using full OHLCV data
2025-06-27 21:40:43,153 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-27 21:40:43,153 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:43,223 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-27 21:40:43,240 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AAVE/USDT using full OHLCV data
2025-06-27 21:40:43,311 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:43,394 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AVAX/USDT using full OHLCV data
2025-06-27 21:40:43,458 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:43,543 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ADA/USDT using full OHLCV data
2025-06-27 21:40:43,605 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:43,684 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/TRX/USDT using full OHLCV data
2025-06-27 21:40:43,750 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:40:43,750 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:43,812 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:40:43,841 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/PEPE/USDT using full OHLCV data
2025-06-27 21:40:43,899 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:43,976 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOGE/USDT using full OHLCV data
2025-06-27 21:40:44,051 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:44,126 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BNB/USDT using full OHLCV data
2025-06-27 21:40:44,207 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:44,281 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOT/USDT using full OHLCV data
2025-06-27 21:40:44,349 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:44,434 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ETH/USDT using full OHLCV data
2025-06-27 21:40:44,492 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:44,581 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BTC/USDT using full OHLCV data
2025-06-27 21:40:44,649 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:40:44,649 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:44,709 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:40:44,734 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SOL/USDT using full OHLCV data
2025-06-27 21:40:44,794 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:44,879 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SUI/USDT using full OHLCV data
2025-06-27 21:40:44,943 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:40:44,943 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:45,006 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:40:45,026 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/XRP/USDT using full OHLCV data
2025-06-27 21:40:45,093 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 21:40:45,093 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:45,164 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 21:40:45,195 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AAVE/USDT using full OHLCV data
2025-06-27 21:40:45,262 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 21:40:45,262 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:45,328 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 21:40:45,347 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AVAX/USDT using full OHLCV data
2025-06-27 21:40:45,406 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:45,492 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ADA/USDT using full OHLCV data
2025-06-27 21:40:45,557 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 21:40:45,557 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:45,620 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 21:40:45,644 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/LINK/USDT using full OHLCV data
2025-06-27 21:40:45,706 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 21:40:45,708 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:45,769 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 21:40:45,793 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/PEPE/USDT using full OHLCV data
2025-06-27 21:40:45,865 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:45,960 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOGE/USDT using full OHLCV data
2025-06-27 21:40:46,025 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:46,105 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BNB/USDT using full OHLCV data
2025-06-27 21:40:46,172 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:46,258 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOT/USDT using full OHLCV data
2025-06-27 21:40:46,323 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:46,407 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ETH/USDT using full OHLCV data
2025-06-27 21:40:46,485 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:46,564 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BTC/USDT using full OHLCV data
2025-06-27 21:40:46,624 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:46,707 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SOL/USDT using full OHLCV data
2025-06-27 21:40:46,773 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:46,859 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SUI/USDT using full OHLCV data
2025-06-27 21:40:46,917 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:46,997 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/XRP/USDT using full OHLCV data
2025-06-27 21:40:47,068 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-27 21:40:47,076 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:47,134 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-27 21:40:47,157 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AAVE/USDT using full OHLCV data
2025-06-27 21:40:47,223 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 21:40:47,223 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:47,284 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-27 21:40:47,307 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AVAX/USDT using full OHLCV data
2025-06-27 21:40:47,374 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:40:47,374 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:47,429 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-27 21:40:47,457 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ADA/USDT using full OHLCV data
2025-06-27 21:40:47,517 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-27 21:40:47,517 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:47,585 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-27 21:40:47,614 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/LINK/USDT using full OHLCV data
2025-06-27 21:40:47,675 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:47,749 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/TRX/USDT using full OHLCV data
2025-06-27 21:40:47,827 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:47,898 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOGE/USDT using full OHLCV data
2025-06-27 21:40:47,969 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:48,051 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BNB/USDT using full OHLCV data
2025-06-27 21:40:48,114 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:48,192 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOT/USDT using full OHLCV data
2025-06-27 21:40:48,267 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:48,347 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ETH/USDT using full OHLCV data
2025-06-27 21:40:48,409 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:48,492 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BTC/USDT using full OHLCV data
2025-06-27 21:40:48,557 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:48,637 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SOL/USDT using full OHLCV data
2025-06-27 21:40:48,700 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 21:40:48,700 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:48,762 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 21:40:48,790 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SUI/USDT using full OHLCV data
2025-06-27 21:40:48,859 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 21:40:48,859 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:48,922 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-27 21:40:48,942 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/XRP/USDT using full OHLCV data
2025-06-27 21:40:49,010 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 21:40:49,010 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:49,080 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 21:40:49,103 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AAVE/USDT using full OHLCV data
2025-06-27 21:40:49,170 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-27 21:40:49,170 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:49,233 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-27 21:40:49,256 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AVAX/USDT using full OHLCV data
2025-06-27 21:40:49,315 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-27 21:40:49,315 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:49,379 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-27 21:40:49,405 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ADA/USDT using full OHLCV data
2025-06-27 21:40:49,439 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:40:49,486 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 21:40:49,486 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:49,550 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 21:40:49,573 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/LINK/USDT using full OHLCV data
2025-06-27 21:40:49,637 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-27 21:40:49,639 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:49,694 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-27 21:40:49,717 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/TRX/USDT using full OHLCV data
2025-06-27 21:40:49,783 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-27 21:40:49,783 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:49,846 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-27 21:40:49,864 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/PEPE/USDT using full OHLCV data
2025-06-27 21:40:49,937 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:50,030 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BNB/USDT using full OHLCV data
2025-06-27 21:40:50,107 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 21:40:50,107 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:50,170 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 21:40:50,190 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/DOT/USDT using full OHLCV data
2025-06-27 21:40:50,261 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:50,337 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ETH/USDT using full OHLCV data
2025-06-27 21:40:50,405 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:50,480 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/BTC/USDT using full OHLCV data
2025-06-27 21:40:50,554 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:40:50,554 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:50,607 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:40:50,633 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SOL/USDT using full OHLCV data
2025-06-27 21:40:50,699 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 21:40:50,699 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:50,763 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 21:40:50,793 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SUI/USDT using full OHLCV data
2025-06-27 21:40:50,868 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 21:40:50,868 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:50,938 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-27 21:40:50,957 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/XRP/USDT using full OHLCV data
2025-06-27 21:40:51,046 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-27 21:40:51,046 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:51,103 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-27 21:40:51,121 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AAVE/USDT using full OHLCV data
2025-06-27 21:40:51,187 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-27 21:40:51,187 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:51,254 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-27 21:40:51,282 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AVAX/USDT using full OHLCV data
2025-06-27 21:40:51,349 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:40:51,349 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:51,412 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:40:51,437 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ADA/USDT using full OHLCV data
2025-06-27 21:40:51,527 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-27 21:40:51,528 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:51,594 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-27 21:40:51,627 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/LINK/USDT using full OHLCV data
2025-06-27 21:40:51,703 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 21:40:51,703 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:51,770 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-27 21:40:51,805 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/TRX/USDT using full OHLCV data
2025-06-27 21:40:51,877 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:40:51,877 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:51,946 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:40:51,978 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/PEPE/USDT using full OHLCV data
2025-06-27 21:40:52,050 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:52,132 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOGE/USDT using full OHLCV data
2025-06-27 21:40:52,204 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:52,312 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOT/USDT using full OHLCV data
2025-06-27 21:40:52,403 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:52,518 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ETH/USDT using full OHLCV data
2025-06-27 21:40:52,612 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:52,724 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BTC/USDT using full OHLCV data
2025-06-27 21:40:52,812 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:52,917 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SOL/USDT using full OHLCV data
2025-06-27 21:40:52,985 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 21:40:52,987 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:53,067 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-27 21:40:53,095 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SUI/USDT using full OHLCV data
2025-06-27 21:40:53,171 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 21:40:53,171 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:53,251 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-27 21:40:53,283 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/XRP/USDT using full OHLCV data
2025-06-27 21:40:53,384 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 21:40:53,384 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:53,449 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-27 21:40:53,482 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AAVE/USDT using full OHLCV data
2025-06-27 21:40:53,573 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-27 21:40:53,575 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:53,660 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-27 21:40:53,680 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AVAX/USDT using full OHLCV data
2025-06-27 21:40:53,754 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-27 21:40:53,756 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:53,840 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-27 21:40:53,864 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ADA/USDT using full OHLCV data
2025-06-27 21:40:53,955 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-27 21:40:53,955 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:54,033 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-27 21:40:54,059 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/LINK/USDT using full OHLCV data
2025-06-27 21:40:54,145 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:54,228 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/TRX/USDT using full OHLCV data
2025-06-27 21:40:54,290 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:40:54,290 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:54,363 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-27 21:40:54,387 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/PEPE/USDT using full OHLCV data
2025-06-27 21:40:54,468 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:54,541 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/DOGE/USDT using full OHLCV data
2025-06-27 21:40:54,580 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-27 21:40:54,676 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BNB/USDT using full OHLCV data
2025-06-27 21:40:59,624 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:41:02,514 - root - INFO - Successfully calculated daily scores using ratio-based comparisons.
2025-06-27 21:41:02,514 - root - INFO - Finished calculating daily scores. DataFrame shape: (197, 14)
2025-06-27 21:41:02,514 - root - WARNING - Running strategy with n_assets=1, equal allocation, MTPI filtering DISABLED
2025-06-27 21:41:02,533 - root - INFO - Date ranges for each asset:
2025-06-27 21:41:02,540 - root - INFO -   ETH/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:41:02,541 - root - INFO -   BTC/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:41:02,541 - root - INFO -   SOL/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:41:02,541 - root - INFO -   SUI/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:41:02,541 - root - INFO -   XRP/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:41:02,541 - root - INFO -   AAVE/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:41:02,541 - root - INFO -   AVAX/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:41:02,541 - root - INFO -   ADA/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:41:02,541 - root - INFO -   LINK/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:41:02,541 - root - INFO -   TRX/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:41:02,541 - root - INFO -   PEPE/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:41:02,541 - root - INFO -   DOGE/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:41:02,541 - root - INFO -   BNB/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:41:02,541 - root - INFO -   DOT/USDT: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:41:02,541 - root - INFO - Common dates range: 2024-12-12 to 2025-06-26 (197 candles)
2025-06-27 21:41:02,549 - root - INFO - Analysis will run from: 2025-02-10 to 2025-06-26 (137 candles)
2025-06-27 21:41:02,563 - root - INFO - EXECUTION TIMING VERIFICATION:
2025-06-27 21:41:02,563 - root - INFO -    Execution Method: candle_close
2025-06-27 21:41:02,563 - root - INFO -    Automatic execution at 00:00 UTC (candle close)
2025-06-27 21:41:02,565 - root - INFO -    Signal generated and executed immediately
2025-06-27 21:41:02,609 - root - INFO - Including ETH/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,611 - root - INFO - Including BTC/USDT on 2025-02-10 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,611 - root - INFO - Including SOL/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,611 - root - INFO - Including SUI/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,613 - root - INFO - Including XRP/USDT on 2025-02-10 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,613 - root - INFO - Including AAVE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,613 - root - INFO - Including AVAX/USDT on 2025-02-10 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,615 - root - INFO - Including ADA/USDT on 2025-02-10 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,615 - root - INFO - Including LINK/USDT on 2025-02-10 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,615 - root - INFO - Including TRX/USDT on 2025-02-10 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,617 - root - INFO - Including PEPE/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,617 - root - INFO - Including DOGE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,617 - root - INFO - Including BNB/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,617 - root - INFO - Including DOT/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,637 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-06-27 21:41:02,637 - root - INFO - ASSET CHANGE DETECTED on 2025-02-11:
2025-06-27 21:41:02,639 - root - INFO -    Signal Date: 2025-02-10 (generated at 00:00 UTC)
2025-06-27 21:41:02,639 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-11 00:00 UTC (immediate)
2025-06-27 21:41:02,639 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:41:02,640 - root - INFO -    Buying: ['TRX/USDT']
2025-06-27 21:41:02,642 - root - INFO -    TRX/USDT buy price: $0.2410 (close price)
2025-06-27 21:41:02,648 - root - INFO - Including ETH/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,648 - root - INFO - Including BTC/USDT on 2025-02-11 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,653 - root - INFO - Including SOL/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,653 - root - INFO - Including SUI/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,653 - root - INFO - Including XRP/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,653 - root - INFO - Including AAVE/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,653 - root - INFO - Including AVAX/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,653 - root - INFO - Including ADA/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,655 - root - INFO - Including LINK/USDT on 2025-02-11 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,655 - root - INFO - Including TRX/USDT on 2025-02-11 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,656 - root - INFO - Including PEPE/USDT on 2025-02-11 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,658 - root - INFO - Including DOGE/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,658 - root - INFO - Including BNB/USDT on 2025-02-11 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,658 - root - INFO - Including DOT/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,676 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:02,683 - root - INFO - Including ETH/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,688 - root - INFO - Including BTC/USDT on 2025-02-12 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,688 - root - INFO - Including SOL/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,688 - root - INFO - Including SUI/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,688 - root - INFO - Including XRP/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,691 - root - INFO - Including AAVE/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,691 - root - INFO - Including AVAX/USDT on 2025-02-12 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,691 - root - INFO - Including ADA/USDT on 2025-02-12 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,691 - root - INFO - Including LINK/USDT on 2025-02-12 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,691 - root - INFO - Including TRX/USDT on 2025-02-12 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,694 - root - INFO - Including PEPE/USDT on 2025-02-12 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,694 - root - INFO - Including DOGE/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,694 - root - INFO - Including BNB/USDT on 2025-02-12 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,696 - root - INFO - Including DOT/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,709 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:02,710 - root - INFO - ASSET CHANGE DETECTED on 2025-02-13:
2025-06-27 21:41:02,710 - root - INFO -    Signal Date: 2025-02-12 (generated at 00:00 UTC)
2025-06-27 21:41:02,710 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-13 00:00 UTC (immediate)
2025-06-27 21:41:02,712 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:41:02,712 - root - INFO -    Selling: ['TRX/USDT']
2025-06-27 21:41:02,712 - root - INFO -    Buying: ['BNB/USDT']
2025-06-27 21:41:02,712 - root - INFO -    BNB/USDT buy price: $664.7200 (close price)
2025-06-27 21:41:02,724 - root - INFO - Including ETH/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,724 - root - INFO - Including BTC/USDT on 2025-02-13 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,724 - root - INFO - Including SOL/USDT on 2025-02-13 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,724 - root - INFO - Including SUI/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,724 - root - INFO - Including XRP/USDT on 2025-02-13 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,724 - root - INFO - Including AAVE/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,724 - root - INFO - Including AVAX/USDT on 2025-02-13 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,724 - root - INFO - Including ADA/USDT on 2025-02-13 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,729 - root - INFO - Including LINK/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,729 - root - INFO - Including TRX/USDT on 2025-02-13 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,730 - root - INFO - Including PEPE/USDT on 2025-02-13 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,730 - root - INFO - Including DOGE/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,730 - root - INFO - Including BNB/USDT on 2025-02-13 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,730 - root - INFO - Including DOT/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,749 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:02,756 - root - INFO - Including ETH/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,756 - root - INFO - Including BTC/USDT on 2025-02-14 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,756 - root - INFO - Including SOL/USDT on 2025-02-14 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,756 - root - INFO - Including SUI/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,761 - root - INFO - Including XRP/USDT on 2025-02-14 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,761 - root - INFO - Including AAVE/USDT on 2025-02-14 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,761 - root - INFO - Including AVAX/USDT on 2025-02-14 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,761 - root - INFO - Including ADA/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,761 - root - INFO - Including LINK/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,761 - root - INFO - Including TRX/USDT on 2025-02-14 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,761 - root - INFO - Including PEPE/USDT on 2025-02-14 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,761 - root - INFO - Including DOGE/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,761 - root - INFO - Including BNB/USDT on 2025-02-14 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,761 - root - INFO - Including DOT/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-27 21:41:02,777 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:02,807 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:02,833 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:02,851 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:02,876 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:02,876 - root - INFO - ASSET CHANGE DETECTED on 2025-02-19:
2025-06-27 21:41:02,876 - root - INFO -    Signal Date: 2025-02-18 (generated at 00:00 UTC)
2025-06-27 21:41:02,876 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-19 00:00 UTC (immediate)
2025-06-27 21:41:02,876 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:41:02,876 - root - INFO -    Selling: ['BNB/USDT']
2025-06-27 21:41:02,876 - root - INFO -    Buying: ['TRX/USDT']
2025-06-27 21:41:02,876 - root - INFO -    TRX/USDT buy price: $0.2424 (close price)
2025-06-27 21:41:02,907 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:02,932 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:02,957 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:02,980 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:02,980 - root - INFO - ASSET CHANGE DETECTED on 2025-02-23:
2025-06-27 21:41:02,980 - root - INFO -    Signal Date: 2025-02-22 (generated at 00:00 UTC)
2025-06-27 21:41:02,980 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-23 00:00 UTC (immediate)
2025-06-27 21:41:02,980 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:41:02,980 - root - INFO -    Selling: ['TRX/USDT']
2025-06-27 21:41:02,988 - root - INFO -    Buying: ['BNB/USDT']
2025-06-27 21:41:02,988 - root - INFO -    BNB/USDT buy price: $658.4200 (close price)
2025-06-27 21:41:03,013 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,013 - root - INFO - ASSET CHANGE DETECTED on 2025-02-24:
2025-06-27 21:41:03,013 - root - INFO -    Signal Date: 2025-02-23 (generated at 00:00 UTC)
2025-06-27 21:41:03,013 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-24 00:00 UTC (immediate)
2025-06-27 21:41:03,013 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:41:03,013 - root - INFO -    Selling: ['BNB/USDT']
2025-06-27 21:41:03,013 - root - INFO -    Buying: ['TRX/USDT']
2025-06-27 21:41:03,013 - root - INFO -    TRX/USDT buy price: $0.2401 (close price)
2025-06-27 21:41:03,045 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,070 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,092 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,121 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,144 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,170 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,197 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,197 - root - INFO - ASSET CHANGE DETECTED on 2025-03-03:
2025-06-27 21:41:03,197 - root - INFO -    Signal Date: 2025-03-02 (generated at 00:00 UTC)
2025-06-27 21:41:03,201 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-03 00:00 UTC (immediate)
2025-06-27 21:41:03,201 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:41:03,201 - root - INFO -    Selling: ['TRX/USDT']
2025-06-27 21:41:03,201 - root - INFO -    Buying: ['ADA/USDT']
2025-06-27 21:41:03,201 - root - INFO -    ADA/USDT buy price: $0.8578 (close price)
2025-06-27 21:41:03,225 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,249 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,275 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,299 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,323 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,347 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,376 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,401 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,401 - root - INFO - ASSET CHANGE DETECTED on 2025-03-11:
2025-06-27 21:41:03,403 - root - INFO -    Signal Date: 2025-03-10 (generated at 00:00 UTC)
2025-06-27 21:41:03,404 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-11 00:00 UTC (immediate)
2025-06-27 21:41:03,404 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:41:03,404 - root - INFO -    Selling: ['ADA/USDT']
2025-06-27 21:41:03,406 - root - INFO -    Buying: ['TRX/USDT']
2025-06-27 21:41:03,407 - root - INFO -    TRX/USDT buy price: $0.2244 (close price)
2025-06-27 21:41:03,428 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,457 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,478 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,508 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,509 - root - INFO - ASSET CHANGE DETECTED on 2025-03-15:
2025-06-27 21:41:03,509 - root - INFO -    Signal Date: 2025-03-14 (generated at 00:00 UTC)
2025-06-27 21:41:03,509 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-15 00:00 UTC (immediate)
2025-06-27 21:41:03,511 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:41:03,511 - root - INFO -    Selling: ['TRX/USDT']
2025-06-27 21:41:03,511 - root - INFO -    Buying: ['ADA/USDT']
2025-06-27 21:41:03,511 - root - INFO -    ADA/USDT buy price: $0.7468 (close price)
2025-06-27 21:41:03,538 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,563 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,563 - root - INFO - ASSET CHANGE DETECTED on 2025-03-17:
2025-06-27 21:41:03,563 - root - INFO -    Signal Date: 2025-03-16 (generated at 00:00 UTC)
2025-06-27 21:41:03,563 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-17 00:00 UTC (immediate)
2025-06-27 21:41:03,563 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:41:03,563 - root - INFO -    Selling: ['ADA/USDT']
2025-06-27 21:41:03,563 - root - INFO -    Buying: ['BNB/USDT']
2025-06-27 21:41:03,567 - root - INFO -    BNB/USDT buy price: $631.6900 (close price)
2025-06-27 21:41:03,591 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,617 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,642 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,642 - root - INFO - ASSET CHANGE DETECTED on 2025-03-20:
2025-06-27 21:41:03,642 - root - INFO -    Signal Date: 2025-03-19 (generated at 00:00 UTC)
2025-06-27 21:41:03,645 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-20 00:00 UTC (immediate)
2025-06-27 21:41:03,645 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:41:03,645 - root - INFO -    Selling: ['BNB/USDT']
2025-06-27 21:41:03,645 - root - INFO -    Buying: ['XRP/USDT']
2025-06-27 21:41:03,647 - root - INFO -    XRP/USDT buy price: $2.4361 (close price)
2025-06-27 21:41:03,670 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,692 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,692 - root - INFO - ASSET CHANGE DETECTED on 2025-03-22:
2025-06-27 21:41:03,694 - root - INFO -    Signal Date: 2025-03-21 (generated at 00:00 UTC)
2025-06-27 21:41:03,694 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-22 00:00 UTC (immediate)
2025-06-27 21:41:03,694 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:41:03,694 - root - INFO -    Selling: ['XRP/USDT']
2025-06-27 21:41:03,694 - root - INFO -    Buying: ['BNB/USDT']
2025-06-27 21:41:03,697 - root - INFO -    BNB/USDT buy price: $627.0100 (close price)
2025-06-27 21:41:03,723 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,748 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,771 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,798 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,798 - root - INFO - ASSET CHANGE DETECTED on 2025-03-26:
2025-06-27 21:41:03,798 - root - INFO -    Signal Date: 2025-03-25 (generated at 00:00 UTC)
2025-06-27 21:41:03,798 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-26 00:00 UTC (immediate)
2025-06-27 21:41:03,798 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:41:03,798 - root - INFO -    Selling: ['BNB/USDT']
2025-06-27 21:41:03,798 - root - INFO -    Buying: ['AVAX/USDT']
2025-06-27 21:41:03,798 - root - INFO -    AVAX/USDT buy price: $22.0400 (close price)
2025-06-27 21:41:03,833 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,835 - root - INFO - ASSET CHANGE DETECTED on 2025-03-27:
2025-06-27 21:41:03,835 - root - INFO -    Signal Date: 2025-03-26 (generated at 00:00 UTC)
2025-06-27 21:41:03,835 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-27 00:00 UTC (immediate)
2025-06-27 21:41:03,835 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:41:03,835 - root - INFO -    Selling: ['AVAX/USDT']
2025-06-27 21:41:03,838 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-27 21:41:03,838 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-27 21:41:03,867 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,901 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,927 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,952 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,953 - root - INFO - ASSET CHANGE DETECTED on 2025-03-31:
2025-06-27 21:41:03,954 - root - INFO -    Signal Date: 2025-03-30 (generated at 00:00 UTC)
2025-06-27 21:41:03,955 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-31 00:00 UTC (immediate)
2025-06-27 21:41:03,955 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:41:03,956 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-27 21:41:03,957 - root - INFO -    Buying: ['BNB/USDT']
2025-06-27 21:41:03,957 - root - INFO -    BNB/USDT buy price: $604.8100 (close price)
2025-06-27 21:41:03,980 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:03,981 - root - INFO - ASSET CHANGE DETECTED on 2025-04-01:
2025-06-27 21:41:03,981 - root - INFO -    Signal Date: 2025-03-31 (generated at 00:00 UTC)
2025-06-27 21:41:03,982 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-01 00:00 UTC (immediate)
2025-06-27 21:41:03,982 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:41:03,982 - root - INFO -    Selling: ['BNB/USDT']
2025-06-27 21:41:03,983 - root - INFO -    Buying: ['TRX/USDT']
2025-06-27 21:41:03,983 - root - INFO -    TRX/USDT buy price: $0.2378 (close price)
2025-06-27 21:41:04,004 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,033 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,056 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,075 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,097 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,121 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,142 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,165 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,188 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,210 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,230 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,253 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,275 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,297 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,319 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,343 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,367 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,393 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,394 - root - INFO - ASSET CHANGE DETECTED on 2025-04-19:
2025-06-27 21:41:04,394 - root - INFO -    Signal Date: 2025-04-18 (generated at 00:00 UTC)
2025-06-27 21:41:04,395 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-19 00:00 UTC (immediate)
2025-06-27 21:41:04,395 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:41:04,396 - root - INFO -    Selling: ['TRX/USDT']
2025-06-27 21:41:04,396 - root - INFO -    Buying: ['SOL/USDT']
2025-06-27 21:41:04,397 - root - INFO -    SOL/USDT buy price: $139.8700 (close price)
2025-06-27 21:41:04,419 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,441 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,464 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,488 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,512 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,512 - root - INFO - ASSET CHANGE DETECTED on 2025-04-24:
2025-06-27 21:41:04,513 - root - INFO -    Signal Date: 2025-04-23 (generated at 00:00 UTC)
2025-06-27 21:41:04,513 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-24 00:00 UTC (immediate)
2025-06-27 21:41:04,514 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:41:04,514 - root - INFO -    Selling: ['SOL/USDT']
2025-06-27 21:41:04,515 - root - INFO -    Buying: ['SUI/USDT']
2025-06-27 21:41:04,515 - root - INFO -    SUI/USDT buy price: $3.3437 (close price)
2025-06-27 21:41:04,542 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,567 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,588 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,611 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,634 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,661 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,684 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,707 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,732 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,756 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,778 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,802 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,830 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,854 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,879 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,903 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,904 - root - INFO - ASSET CHANGE DETECTED on 2025-05-10:
2025-06-27 21:41:04,905 - root - INFO -    Signal Date: 2025-05-09 (generated at 00:00 UTC)
2025-06-27 21:41:04,905 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-10 00:00 UTC (immediate)
2025-06-27 21:41:04,906 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:41:04,907 - root - INFO -    Selling: ['SUI/USDT']
2025-06-27 21:41:04,907 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-27 21:41:04,908 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-27 21:41:04,932 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,956 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:04,981 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,007 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,033 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,058 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,074 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,112 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,131 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,157 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,179 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,179 - root - INFO - ASSET CHANGE DETECTED on 2025-05-21:
2025-06-27 21:41:05,179 - root - INFO -    Signal Date: 2025-05-20 (generated at 00:00 UTC)
2025-06-27 21:41:05,179 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-21 00:00 UTC (immediate)
2025-06-27 21:41:05,179 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:41:05,179 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-27 21:41:05,179 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-27 21:41:05,188 - root - INFO -    AAVE/USDT buy price: $247.5400 (close price)
2025-06-27 21:41:05,208 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,240 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,240 - root - INFO - ASSET CHANGE DETECTED on 2025-05-23:
2025-06-27 21:41:05,243 - root - INFO -    Signal Date: 2025-05-22 (generated at 00:00 UTC)
2025-06-27 21:41:05,244 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-23 00:00 UTC (immediate)
2025-06-27 21:41:05,244 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:41:05,244 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-27 21:41:05,244 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-27 21:41:05,244 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-27 21:41:05,268 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,292 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,292 - root - INFO - ASSET CHANGE DETECTED on 2025-05-25:
2025-06-27 21:41:05,292 - root - INFO -    Signal Date: 2025-05-24 (generated at 00:00 UTC)
2025-06-27 21:41:05,292 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-25 00:00 UTC (immediate)
2025-06-27 21:41:05,292 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:41:05,292 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-27 21:41:05,292 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-27 21:41:05,301 - root - INFO -    AAVE/USDT buy price: $269.2800 (close price)
2025-06-27 21:41:05,324 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,347 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,376 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,402 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,423 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,446 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,469 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,493 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,518 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,542 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,567 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,595 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,624 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,649 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,674 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,699 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,723 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,760 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,796 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,819 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,838 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,859 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,875 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,900 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,917 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,941 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,961 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:05,961 - root - INFO - ASSET CHANGE DETECTED on 2025-06-21:
2025-06-27 21:41:05,961 - root - INFO -    Signal Date: 2025-06-20 (generated at 00:00 UTC)
2025-06-27 21:41:05,961 - root - INFO -    AUTOMATIC EXECUTION: 2025-06-21 00:00 UTC (immediate)
2025-06-27 21:41:05,964 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:41:05,964 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-27 21:41:05,965 - root - INFO -    Buying: ['TRX/USDT']
2025-06-27 21:41:05,965 - root - INFO -    TRX/USDT buy price: $0.2710 (close price)
2025-06-27 21:41:05,981 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:06,006 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:06,037 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:06,059 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:06,076 - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-06-27 21:41:06,076 - root - INFO - ASSET CHANGE DETECTED on 2025-06-26:
2025-06-27 21:41:06,076 - root - INFO -    Signal Date: 2025-06-25 (generated at 00:00 UTC)
2025-06-27 21:41:06,076 - root - INFO -    AUTOMATIC EXECUTION: 2025-06-26 00:00 UTC (immediate)
2025-06-27 21:41:06,076 - root - INFO -    Execution Delay: 0 hours
2025-06-27 21:41:06,076 - root - INFO -    Selling: ['TRX/USDT']
2025-06-27 21:41:06,076 - root - INFO -    Buying: ['BTC/USDT']
2025-06-27 21:41:06,084 - root - INFO -    BTC/USDT buy price: $106947.0600 (close price)
2025-06-27 21:41:06,210 - root - INFO - Entry trade at 2025-02-11 00:00:00+00:00: TRX/USDT
2025-06-27 21:41:06,210 - root - INFO - Swap trade at 2025-02-13 00:00:00+00:00: TRX/USDT -> BNB/USDT
2025-06-27 21:41:06,210 - root - INFO - Swap trade at 2025-02-19 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-27 21:41:06,212 - root - INFO - Swap trade at 2025-02-23 00:00:00+00:00: TRX/USDT -> BNB/USDT
2025-06-27 21:41:06,212 - root - INFO - Swap trade at 2025-02-24 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-27 21:41:06,212 - root - INFO - Swap trade at 2025-03-03 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-06-27 21:41:06,214 - root - INFO - Swap trade at 2025-03-11 00:00:00+00:00: ADA/USDT -> TRX/USDT
2025-06-27 21:41:06,214 - root - INFO - Swap trade at 2025-03-15 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-06-27 21:41:06,214 - root - INFO - Swap trade at 2025-03-17 00:00:00+00:00: ADA/USDT -> BNB/USDT
2025-06-27 21:41:06,217 - root - INFO - Swap trade at 2025-03-20 00:00:00+00:00: BNB/USDT -> XRP/USDT
2025-06-27 21:41:06,217 - root - INFO - Swap trade at 2025-03-22 00:00:00+00:00: XRP/USDT -> BNB/USDT
2025-06-27 21:41:06,217 - root - INFO - Swap trade at 2025-03-26 00:00:00+00:00: BNB/USDT -> AVAX/USDT
2025-06-27 21:41:06,218 - root - INFO - Swap trade at 2025-03-27 00:00:00+00:00: AVAX/USDT -> PEPE/USDT
2025-06-27 21:41:06,218 - root - INFO - Swap trade at 2025-03-31 00:00:00+00:00: PEPE/USDT -> BNB/USDT
2025-06-27 21:41:06,218 - root - INFO - Swap trade at 2025-04-01 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-27 21:41:06,218 - root - INFO - Swap trade at 2025-04-19 00:00:00+00:00: TRX/USDT -> SOL/USDT
2025-06-27 21:41:06,218 - root - INFO - Swap trade at 2025-04-24 00:00:00+00:00: SOL/USDT -> SUI/USDT
2025-06-27 21:41:06,223 - root - INFO - Swap trade at 2025-05-10 00:00:00+00:00: SUI/USDT -> PEPE/USDT
2025-06-27 21:41:06,225 - root - INFO - Swap trade at 2025-05-21 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-27 21:41:06,225 - root - INFO - Swap trade at 2025-05-23 00:00:00+00:00: AAVE/USDT -> PEPE/USDT
2025-06-27 21:41:06,227 - root - INFO - Swap trade at 2025-05-25 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-27 21:41:06,227 - root - INFO - Swap trade at 2025-06-21 00:00:00+00:00: AAVE/USDT -> TRX/USDT
2025-06-27 21:41:06,227 - root - INFO - Swap trade at 2025-06-26 00:00:00+00:00: TRX/USDT -> BTC/USDT
2025-06-27 21:41:06,227 - root - INFO - Total trades: 23 (Entries: 1, Exits: 0, Swaps: 22)
2025-06-27 21:41:06,240 - root - INFO - Strategy execution completed in 3s
2025-06-27 21:41:06,276 - root - INFO - DEBUG: self.elapsed_time = 3.7260067462921143 seconds
2025-06-27 21:41:06,374 - root - INFO - Saved allocation history to allocation_history_1d_1d_no_mtpi_no_rebal_independent_imcumbent_2025-02-10.csv
2025-06-27 21:41:06,430 - root - INFO - Strategy equity curve starts at: 2025-02-10
2025-06-27 21:41:06,480 - root - INFO - Assets included in buy-and-hold comparison:
2025-06-27 21:41:06,498 - root - INFO -   ETH/USDT: First available date 2024-12-12
2025-06-27 21:41:06,554 - root - INFO -   BTC/USDT: First available date 2024-12-12
2025-06-27 21:41:06,598 - root - INFO -   SOL/USDT: First available date 2024-12-12
2025-06-27 21:41:06,598 - root - INFO -   SUI/USDT: First available date 2024-12-12
2025-06-27 21:41:06,598 - root - INFO -   XRP/USDT: First available date 2024-12-12
2025-06-27 21:41:06,600 - root - INFO -   AAVE/USDT: First available date 2024-12-12
2025-06-27 21:41:06,602 - root - INFO -   AVAX/USDT: First available date 2024-12-12
2025-06-27 21:41:06,602 - root - INFO -   ADA/USDT: First available date 2024-12-12
2025-06-27 21:41:06,604 - root - INFO -   LINK/USDT: First available date 2024-12-12
2025-06-27 21:41:06,604 - root - INFO -   TRX/USDT: First available date 2024-12-12
2025-06-27 21:41:06,607 - root - INFO -   PEPE/USDT: First available date 2024-12-12
2025-06-27 21:41:06,609 - root - INFO -   DOGE/USDT: First available date 2024-12-12
2025-06-27 21:41:06,610 - root - INFO -   BNB/USDT: First available date 2024-12-12
2025-06-27 21:41:06,610 - root - INFO -   DOT/USDT: First available date 2024-12-12
2025-06-27 21:41:06,610 - root - INFO - Using provided start date for normalization: 2025-02-10 00:00:00+00:00
2025-06-27 21:41:06,617 - root - INFO - Normalized ETH/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:41:06,621 - root - INFO - Normalized BTC/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:41:06,627 - root - INFO - Normalized SOL/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:41:06,646 - root - INFO - Normalized SUI/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:41:06,669 - root - INFO - Normalized XRP/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:41:06,677 - root - INFO - Normalized AAVE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:41:06,682 - root - INFO - Normalized AVAX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:41:06,691 - root - INFO - Normalized ADA/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:41:06,698 - root - INFO - Normalized LINK/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:41:06,704 - root - INFO - Normalized TRX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:41:06,714 - root - INFO - Normalized PEPE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:41:06,718 - root - INFO - Normalized DOGE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:41:06,723 - root - INFO - Normalized BNB/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:41:06,728 - root - INFO - Normalized DOT/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-27 21:41:06,728 - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 197 points
2025-06-27 21:41:06,728 - root - INFO - ETH/USDT B&H total return: -9.22%
2025-06-27 21:41:06,741 - root - INFO - Added equity_curve to bnh_metrics for BTC/USDT with 197 points
2025-06-27 21:41:06,742 - root - INFO - BTC/USDT B&H total return: 9.77%
2025-06-27 21:41:06,748 - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 197 points
2025-06-27 21:41:06,749 - root - INFO - SOL/USDT B&H total return: -30.63%
2025-06-27 21:41:06,757 - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 197 points
2025-06-27 21:41:06,758 - root - INFO - SUI/USDT B&H total return: -19.11%
2025-06-27 21:41:06,763 - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 197 points
2025-06-27 21:41:06,763 - root - INFO - XRP/USDT B&H total return: -13.13%
2025-06-27 21:41:06,769 - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 197 points
2025-06-27 21:41:06,774 - root - INFO - AAVE/USDT B&H total return: -0.91%
2025-06-27 21:41:06,780 - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 197 points
2025-06-27 21:41:06,782 - root - INFO - AVAX/USDT B&H total return: -32.88%
2025-06-27 21:41:06,788 - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 197 points
2025-06-27 21:41:06,792 - root - INFO - ADA/USDT B&H total return: -22.16%
2025-06-27 21:41:06,797 - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 197 points
2025-06-27 21:41:06,799 - root - INFO - LINK/USDT B&H total return: -31.21%
2025-06-27 21:41:06,806 - root - INFO - Added equity_curve to bnh_metrics for TRX/USDT with 197 points
2025-06-27 21:41:06,808 - root - INFO - TRX/USDT B&H total return: 10.07%
2025-06-27 21:41:06,816 - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 197 points
2025-06-27 21:41:06,817 - root - INFO - PEPE/USDT B&H total return: -4.48%
2025-06-27 21:41:06,825 - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 197 points
2025-06-27 21:41:06,825 - root - INFO - DOGE/USDT B&H total return: -37.35%
2025-06-27 21:41:06,834 - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 197 points
2025-06-27 21:41:06,834 - root - INFO - BNB/USDT B&H total return: 3.82%
2025-06-27 21:41:06,845 - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 197 points
2025-06-27 21:41:06,845 - root - INFO - DOT/USDT B&H total return: -31.91%
2025-06-27 21:41:06,845 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-27 21:41:06,891 - root - INFO - Configuration loaded successfully.
2025-06-27 21:41:06,926 - root - INFO - Using colored segments for single-asset strategy visualization
2025-06-27 21:41:07,418 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-27 21:41:07,457 - root - INFO - Configuration loaded successfully.
2025-06-27 21:41:09,805 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:41:11,611 - root - INFO - Added ETH/USDT buy-and-hold curve with 197 points
2025-06-27 21:41:11,611 - root - INFO - Added BTC/USDT buy-and-hold curve with 197 points
2025-06-27 21:41:11,611 - root - INFO - Added SOL/USDT buy-and-hold curve with 197 points
2025-06-27 21:41:11,611 - root - INFO - Added SUI/USDT buy-and-hold curve with 197 points
2025-06-27 21:41:11,611 - root - INFO - Added XRP/USDT buy-and-hold curve with 197 points
2025-06-27 21:41:11,611 - root - INFO - Added AAVE/USDT buy-and-hold curve with 197 points
2025-06-27 21:41:11,611 - root - INFO - Added AVAX/USDT buy-and-hold curve with 197 points
2025-06-27 21:41:11,611 - root - INFO - Added ADA/USDT buy-and-hold curve with 197 points
2025-06-27 21:41:11,611 - root - INFO - Added LINK/USDT buy-and-hold curve with 197 points
2025-06-27 21:41:11,611 - root - INFO - Added TRX/USDT buy-and-hold curve with 197 points
2025-06-27 21:41:11,611 - root - INFO - Added PEPE/USDT buy-and-hold curve with 197 points
2025-06-27 21:41:11,621 - root - INFO - Added DOGE/USDT buy-and-hold curve with 197 points
2025-06-27 21:41:11,621 - root - INFO - Added BNB/USDT buy-and-hold curve with 197 points
2025-06-27 21:41:11,621 - root - INFO - Added DOT/USDT buy-and-hold curve with 197 points
2025-06-27 21:41:11,621 - root - INFO - Added 14 buy-and-hold curves to results
2025-06-27 21:41:11,621 - root - INFO -   - ETH/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:41:11,621 - root - INFO -   - BTC/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:41:11,623 - root - INFO -   - SOL/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:41:11,623 - root - INFO -   - SUI/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:41:11,623 - root - INFO -   - XRP/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:41:11,623 - root - INFO -   - AAVE/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:41:11,623 - root - INFO -   - AVAX/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:41:11,624 - root - INFO -   - ADA/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:41:11,624 - root - INFO -   - LINK/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:41:11,624 - root - INFO -   - TRX/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:41:11,624 - root - INFO -   - PEPE/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:41:11,624 - root - INFO -   - DOGE/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:41:11,624 - root - INFO -   - BNB/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:41:11,624 - root - INFO -   - DOT/USDT: 197 points from 2024-12-12 00:00:00+00:00 to 2025-06-26 00:00:00+00:00
2025-06-27 21:41:11,639 - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-06-27 21:41:11,639 - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-27 21:41:11,639 - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-06-27 21:41:11,639 - root - INFO - MTPI disabled - skipping MTPI signal calculation
2025-06-27 21:41:11,646 - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-06-27 21:41:11,646 - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 8.0, 'BTC/USDT': 13.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-27 21:41:11,646 - root - ERROR - [DEBUG] CONVERSION - Original USDT scores: {'ETH/USDT': 8.0, 'BTC/USDT': 13.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-27 21:41:11,646 - root - ERROR - [DEBUG] CONVERSION - Original USDT keys order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-27 21:41:11,646 - root - ERROR - [DEBUG] CONVERSION - Conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-27 21:41:11,646 - root - ERROR - [DEBUG] CONVERSION - ETH/USDT -> ETH/EUR (score: 8.0)
2025-06-27 21:41:11,646 - root - ERROR - [DEBUG] CONVERSION - BTC/USDT -> BTC/EUR (score: 13.0)
2025-06-27 21:41:11,646 - root - ERROR - [DEBUG] CONVERSION - SOL/USDT -> SOL/EUR (score: 6.0)
2025-06-27 21:41:11,646 - root - ERROR - [DEBUG] CONVERSION - SUI/USDT -> SUI/EUR (score: 2.0)
2025-06-27 21:41:11,649 - root - ERROR - [DEBUG] CONVERSION - XRP/USDT -> XRP/EUR (score: 9.0)
2025-06-27 21:41:11,649 - root - ERROR - [DEBUG] CONVERSION - AAVE/USDT -> AAVE/EUR (score: 9.0)
2025-06-27 21:41:11,649 - root - ERROR - [DEBUG] CONVERSION - AVAX/USDT -> AVAX/EUR (score: 3.0)
2025-06-27 21:41:11,649 - root - ERROR - [DEBUG] CONVERSION - ADA/USDT -> ADA/EUR (score: 3.0)
2025-06-27 21:41:11,649 - root - ERROR - [DEBUG] CONVERSION - LINK/USDT -> LINK/EUR (score: 7.0)
2025-06-27 21:41:11,649 - root - ERROR - [DEBUG] CONVERSION - TRX/USDT -> TRX/EUR (score: 12.0)
2025-06-27 21:41:11,649 - root - ERROR - [DEBUG] CONVERSION - PEPE/USDT -> PEPE/EUR (score: 0.0)
2025-06-27 21:41:11,649 - root - ERROR - [DEBUG] CONVERSION - DOGE/USDT -> DOGE/EUR (score: 4.0)
2025-06-27 21:41:11,649 - root - ERROR - [DEBUG] CONVERSION - BNB/USDT -> BNB/EUR (score: 11.0)
2025-06-27 21:41:11,649 - root - ERROR - [DEBUG] CONVERSION - DOT/USDT -> DOT/EUR (score: 1.0)
2025-06-27 21:41:11,649 - root - ERROR - [DEBUG] CONVERSION - Final EUR scores: {'ETH/EUR': 8.0, 'BTC/EUR': 13.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 7.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 4.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-27 21:41:11,649 - root - ERROR - [DEBUG] CONVERSION - Final EUR keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-27 21:41:11,649 - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 8.0, 'BTC/EUR': 13.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 7.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 4.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-27 21:41:11,660 - root - INFO - Saved metrics to new file: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250620_run_********_214013.csv
2025-06-27 21:41:11,660 - root - INFO - Saved performance metrics to CSV: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250620_run_********_214013.csv
2025-06-27 21:41:11,660 - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-06-27 21:41:11,662 - root - INFO - Results type: <class 'dict'>
2025-06-27 21:41:11,662 - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-06-27 21:41:11,662 - root - INFO - Success flag set to: True
2025-06-27 21:41:11,662 - root - INFO - Message set to: Strategy calculation completed successfully
2025-06-27 21:41:11,663 - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 197 entries
2025-06-27 21:41:11,663 - root - INFO -   - buy_hold_curves: dict with 14 entries
2025-06-27 21:41:11,663 - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 197 entries
2025-06-27 21:41:11,663 - root - INFO -   - mtpi_signals: <class 'NoneType'>
2025-06-27 21:41:11,663 - root - INFO -   - mtpi_score: <class 'NoneType'>
2025-06-27 21:41:11,663 - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 197 entries
2025-06-27 21:41:11,663 - root - INFO -   - performance_metrics: dict with 3 entries
2025-06-27 21:41:11,663 - root - INFO -   - metrics_file: <class 'str'>
2025-06-27 21:41:11,663 - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-06-27 21:41:11,663 - root - INFO -   - success: <class 'bool'>
2025-06-27 21:41:11,663 - root - INFO -   - message: <class 'str'>
2025-06-27 21:41:11,663 - root - INFO - MTPI disabled - using default bullish signal (1) for trading execution
2025-06-27 21:41:11,663 - root - ERROR - [DEBUG] STRATEGY RESULTS - Available keys: ['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message']
2025-06-27 21:41:11,663 - root - INFO - [DEBUG] ASSET SELECTION - Extracted best_asset from best_asset_series: BTC/EUR
2025-06-27 21:41:11,663 - root - INFO - [DEBUG] ASSET SELECTION - Last 5 entries in best_asset_series: 2025-06-22 00:00:00+00:00    TRX/EUR
2025-06-23 00:00:00+00:00    TRX/EUR
2025-06-24 00:00:00+00:00    TRX/EUR
2025-06-25 00:00:00+00:00    TRX/EUR
2025-06-26 00:00:00+00:00    BTC/EUR
dtype: object
2025-06-27 21:41:11,663 - root - ERROR - [DEBUG] ASSET SELECTION - Latest scores available: {'ETH/EUR': 8.0, 'BTC/EUR': 13.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 7.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 4.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-27 21:41:11,663 - root - ERROR - [DEBUG] ASSET SELECTION - Scores dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-27 21:41:11,663 - root - INFO - [DEBUG] TIE-BREAKING - Strategy: imcumbent
2025-06-27 21:41:11,663 - root - WARNING - [DEBUG] ASSET SELECTION - find_best_asset_for_day() called with 14 assets
2025-06-27 21:41:11,663 - root - WARNING - [DEBUG] ASSET SELECTION - Input scores: {'ETH/EUR': 8.0, 'BTC/EUR': 13.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 7.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 4.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-27 21:41:11,668 - root - WARNING - [DEBUG] ASSET SELECTION - Dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-27 21:41:11,668 - root - WARNING - [DEBUG] ASSET SELECTION - Maximum score found: 13.0
2025-06-27 21:41:11,668 - root - WARNING - [DEBUG] ASSET SELECTION - Assets with max score 13.0: ['BTC/EUR']
2025-06-27 21:41:11,668 - root - WARNING - [DEBUG] ASSET SELECTION - No tie detected, single winner: BTC/EUR
2025-06-27 21:41:11,668 - root - ERROR - [DEBUG] SELECTED BEST ASSET: BTC/EUR (score: 13.0)
2025-06-27 21:41:11,668 - root - ERROR - [DEBUG] ASSET SELECTION - Current day's best asset: BTC/EUR (MTPI signal: 1)
2025-06-27 21:41:11,668 - root - ERROR - [DEBUG] ASSET SELECTION - Maximum score: 13.0
2025-06-27 21:41:11,668 - root - ERROR - [DEBUG] ASSET SELECTION - Assets with max score: ['BTC/EUR']
2025-06-27 21:41:11,668 - root - ERROR - [DEBUG] NO TIE - CONFIRMED SELECTION: BTC/EUR
2025-06-27 21:41:11,670 - root - ERROR - [DEBUG] NO TIE - Single winner: BTC/EUR
2025-06-27 21:41:11,691 - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
       'BNB/EUR', 'DOT/EUR'],
      dtype='object')
2025-06-27 21:41:11,691 - root - INFO - Last row columns: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-27 21:41:11,691 - root - INFO - Single asset strategy with best asset: BTC/EUR
2025-06-27 21:41:11,691 - root - INFO - [DEBUG] FINAL ASSET SELECTION SUMMARY:
2025-06-27 21:41:11,691 - root - INFO - [DEBUG]   - Best asset selected: BTC/EUR
2025-06-27 21:41:11,691 - root - INFO - [DEBUG]   - Assets held: {'BTC/EUR': 1.0}
2025-06-27 21:41:11,691 - root - INFO - [DEBUG]   - MTPI signal: 1
2025-06-27 21:41:11,691 - root - INFO - [DEBUG]   - Use MTPI signal: False
2025-06-27 21:41:11,695 - root - INFO - Executing single-asset strategy with best asset: BTC/EUR
2025-06-27 21:41:11,695 - root - INFO - Executing strategy signal: best_asset=BTC/EUR, mtpi_signal=1, mode=paper
2025-06-27 21:41:11,695 - root - INFO - Incremented daily trade counter for BTC/EUR: 1/5
2025-06-27 21:41:11,695 - root - INFO - ASSET SELECTION - Attempting to enter position for selected asset: BTC/EUR
2025-06-27 21:41:11,695 - root - INFO - Attempting to enter position for BTC/EUR in paper mode
2025-06-27 21:41:11,695 - root - INFO - [DEBUG] TRADE - BTC/EUR: Starting enter_position attempt
2025-06-27 21:41:11,695 - root - INFO - [DEBUG] TRADE - BTC/EUR: Trading mode: paper
2025-06-27 21:41:11,695 - root - INFO - TRADE ATTEMPT - BTC/EUR: Getting current market price...
2025-06-27 21:41:11,695 - root - INFO - [DEBUG] PRICE - BTC/EUR: Starting get_current_price
2025-06-27 21:41:11,695 - root - INFO - [DEBUG] PRICE - BTC/EUR: Exchange ID: bitvavo
2025-06-27 21:41:11,695 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Initializing exchange bitvavo
2025-06-27 21:41:11,695 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Exchange initialized successfully
2025-06-27 21:41:11,695 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Exchange markets not loaded, loading now...
2025-06-27 21:41:12,100 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Symbol found after loading markets
2025-06-27 21:41:12,100 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Attempting to fetch ticker...
2025-06-27 21:41:12,139 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Ticker fetched successfully
2025-06-27 21:41:12,139 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Ticker data: {'symbol': 'BTC/EUR', 'timestamp': 1751053267215, 'datetime': '2025-06-27T19:41:07.215Z', 'high': 92535.0, 'low': 90764.0, 'bid': 91283.0, 'bidVolume': 0.00798019, 'ask': 91292.0, 'askVolume': 0.08051278, 'vwap': 91498.41601078269, 'open': 91928.0, 'close': 91289.0, 'last': 91289.0, 'previousClose': None, 'change': -639.0, 'percentage': -0.6951092159081019, 'average': 91608.5, 'baseVolume': 314.52856134, 'quoteVolume': 28778865.1527603, 'info': {'market': 'BTC-EUR', 'startTimestamp': 1750966867215, 'timestamp': 1751053267215, 'open': '91928', 'openTimestamp': 1750966875599, 'high': '92535', 'low': '90764', 'last': '91289', 'closeTimestamp': 1751053258338, 'bid': '91283', 'bidSize': '0.00798019', 'ask': '91292', 'askSize': '0.08051278', 'volume': '314.52856134', 'volumeQuote': '28778865.1527603'}, 'indexPrice': None, 'markPrice': None}
2025-06-27 21:41:12,139 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Last price: 91289.0
2025-06-27 21:41:12,139 - root - INFO - [DEBUG] TRADE - BTC/EUR: get_current_price returned: 91289.0
2025-06-27 21:41:12,139 - root - INFO - [DEBUG] TRADE - BTC/EUR: Price type: <class 'float'>
2025-06-27 21:41:12,139 - root - INFO - [DEBUG] TRADE - BTC/EUR: Price evaluation - not price: False
2025-06-27 21:41:12,139 - root - INFO - [DEBUG] TRADE - BTC/EUR: Price evaluation - price <= 0: False
2025-06-27 21:41:12,139 - root - INFO - TRADE ATTEMPT - BTC/EUR: Current price: 91289.********
2025-06-27 21:41:12,139 - root - INFO - Available balance for EUR: 100.********
2025-06-27 21:41:12,147 - root - INFO - Loaded market info for 176 trading pairs
2025-06-27 21:41:12,147 - root - INFO - Calculated position size for BTC/EUR: 0.******** (using 10% of 100, accounting for fees)
2025-06-27 21:41:12,147 - root - INFO - Calculated position size: 0.******** BTC
2025-06-27 21:41:12,147 - root - INFO - Entering position for BTC/EUR: 0.******** units at 91289.******** (value: 9.******** EUR)
2025-06-27 21:41:12,147 - root - INFO - Executing paper market buy order for BTC/EUR
2025-06-27 21:41:12,147 - root - INFO - Paper trading buy for BTC/EUR: original=0.********, adjusted=0.********, after_fee=0.********
2025-06-27 21:41:12,147 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-27 21:41:12,147 - root - INFO - Created paper market buy order: BTC/EUR, amount: 0.********108983557714, price: 91289.0
2025-06-27 21:41:12,147 - root - INFO - Filled amount: 0.******** BTC
2025-06-27 21:41:12,147 - root - INFO - Order fee: 0.******** EUR
2025-06-27 21:41:12,147 - root - INFO - Successfully entered position: BTC/EUR, amount: 0.********, price: 91289.********
2025-06-27 21:41:12,157 - root - INFO - Trade executed: BUY BTC/EUR, amount=0.********, price=91289.********, filled=0.********
2025-06-27 21:41:12,157 - root - INFO -   Fee: 0.******** EUR
2025-06-27 21:41:12,157 - root - INFO - TRADE SUCCESS - BTC/EUR: Successfully updated current asset
2025-06-27 21:41:12,157 - root - INFO - Trade executed: BUY BTC/EUR, amount=0.********, price=91289.********, filled=0.********
2025-06-27 21:41:12,157 - root - INFO -   Fee: 0.******** EUR
2025-06-27 21:41:12,157 - root - INFO - Single-asset trade result logged to trade log file
2025-06-27 21:41:12,157 - root - INFO - Trade executed: {'success': True, 'symbol': 'BTC/EUR', 'side': 'buy', 'amount': 0.********45119346252, 'price': 91289.0, 'order': {'id': 'paper-1751053272-BTC/EUR-buy-0.********108983557714', 'symbol': 'BTC/EUR', 'side': 'buy', 'type': 'market', 'amount': 0.********108983557714, 'price': 91289.0, 'cost': 9.90025********02, 'fee': {'cost': 0.****************01, 'currency': 'EUR', 'rate': 0.001}, 'status': 'closed', 'filled': 0.********108983557714, 'remaining': 0, 'timestamp': 1751053272147, 'datetime': '2025-06-27T21:41:12.147567', 'trades': [], 'average': 91289.0, 'average_price': 91289.0}, 'filled_amount': 0.********108983557714, 'fee': {'cost': 0.****************01, 'currency': 'EUR', 'rate': 0.001}, 'quote_currency': 'EUR', 'base_currency': 'BTC', 'timestamp': '2025-06-27T21:41:12.147567'}
2025-06-27 21:41:12,270 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-27 21:41:12,275 - root - INFO - Asset selection logged: 1 assets selected with single_asset allocation
2025-06-27 21:41:12,275 - root - INFO - Asset scores (sorted by score):
2025-06-27 21:41:12,275 - root - INFO -   BTC/EUR: score=13.0, status=SELECTED, weight=1.00
2025-06-27 21:41:12,275 - root - INFO -   TRX/EUR: score=12.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:41:12,275 - root - INFO -   BNB/EUR: score=11.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:41:12,282 - root - INFO -   XRP/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:41:12,282 - root - INFO -   AAVE/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:41:12,282 - root - INFO -   ETH/EUR: score=8.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:41:12,282 - root - INFO -   LINK/EUR: score=7.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:41:12,282 - root - INFO -   SOL/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:41:12,282 - root - INFO -   DOGE/EUR: score=4.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:41:12,282 - root - INFO -   AVAX/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:41:12,282 - root - INFO -   ADA/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:41:12,284 - root - INFO -   SUI/EUR: score=2.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:41:12,284 - root - INFO -   DOT/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:41:12,284 - root - INFO -   PEPE/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-06-27 21:41:12,284 - root - INFO - Asset selection logged with 14 assets scored and 1 assets selected
2025-06-27 21:41:12,284 - root - INFO - MTPI disabled - skipping MTPI score extraction
2025-06-27 21:41:12,284 - root - INFO - Extracted asset scores: {'ETH/EUR': 8.0, 'BTC/EUR': 13.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 7.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 4.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-27 21:41:12,343 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-27 21:41:12,349 - root - INFO - Strategy execution completed successfully in 58.36 seconds
2025-06-27 21:41:12,349 - root - INFO - Saved recovery state to data/state\recovery_state.json
2025-06-27 21:41:19,876 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:41:29,882 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:41:39,900 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:41:43,598 - root - INFO - Received signal 2, shutting down...
2025-06-27 21:41:49,927 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-27 21:41:53,614 - root - INFO - Network watchdog stopped
2025-06-27 21:41:53,614 - root - INFO - Network watchdog stopped
2025-06-27 21:41:53,614 - root - INFO - Background service stopped
2025-06-27 21:41:53,698 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
